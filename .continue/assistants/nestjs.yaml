name: NestJs Assistant
version: 1.0.0
schema: v1

rules:
  - |
    # Project Context

    - Framework: NestJS 11
    - Language: TypeScript (Node.js v23+)
    - API Layers: GraphQL with Apollo Server (federation) and REST
    - Data Layer: MongoDB with custom database abstractions
    - Authentication: OIDC Provider & Passport.js strategies
    - Project Structure: NX monorepo architecture

    For directory structure and ownership, see `instructions/seed.instructions.md`.

    # Internal Libraries & Patterns

    ## Key Libraries (from libs/)

    - core: Base classes, decorators, guards, and utilities
    - database: Entity services, repositories, and MongoDB abstractions
    - accounts: User management, organizations, and authentication
    - passport: OAuth strategies for third-party login
    - stripe: Payment integration services

    ## Key Services (from apps/services/)

    - services-core: Core domain services
    - services-ecommerce: E-commerce related services
    - gateway: GraphQL API gateway with federation
    - oidc: Identity provider with OpenID Connect
    - workers: Background processing (notifications, webhooks, cleaner)

    When implementing features, leverage existing libraries and follow established patterns rather than creating new abstractions.

    ## Key Base Classes & Patterns

    - MongoDBEntity: Base entity class with MongoDB-specific functionality (from @phigital-loyalty/database)
    - BaseResolver: Base resolver class for GraphQL endpoints (from @phigital-loyalty/database)
    - BaseController: Base controller class for REST endpoints (from @phigital-loyalty/database)
    - EntityType: Decorator for entity definitions with GraphQL/Swagger metadata (from @phigital-loyalty/core)
    - CheckPolicies: Authorization guards for enforcing access control (from @phigital-loyalty/core)
    - Private/Hidden: Decorators for field-level visibility control (from @phigital-loyalty/core)

    # Code Style & Conventions

    - Follow the project's Prettier configuration:
      - Use single quotes
      - Omit semicolons
      - 120 character line width
      - Maintain consistent spacing
    - Follow ESLint rules for imports:
      - Group imports by type with newlines between groups
      - Alphabetize imports
      - Pattern: builtin → external → internal → relative
    - Use TypeScript decorators following NestJS conventions
    - Leverage dependency injection and avoid static methods when possible
    - Follow object-oriented principles for service organization

    # Clean Code Principles

    - Code should be easy to read and understand.
    - Keep the code as simple as possible. Avoid unnecessary complexity.
    - Use meaningful names for variables, functions, etc. Names should reveal intent.
    - Functions should be small and do one thing well.
    - Function names should describe the action being performed.
    - Prefer fewer arguments in functions.
    - Only use comments when necessary, as they can become outdated. Instead, strive to make the code self-explanatory.
    - When comments are used, they should add useful information that is not readily apparent from the code itself.
    - Properly handle errors and exceptions to ensure the software's robustness.
    - Use exceptions rather than error codes for handling errors.
    - Consider security implications of the code. Implement security best practices to protect against vulnerabilities and attacks.

    # Testing Philosophy

    - Prioritize integration tests over isolated unit tests
    - Focus on testing critical functionality without excessive complexity
    - Test internal components through higher-level integration tests
    - Use real implementations where feasible, minimizing mocks
    - Only mock external dependencies when necessary
    - Write tests for actual use cases rather than implementation details
    - Avoid temporary workarounds or mocking to force test results

    # Database Patterns

    - Use the Entity pattern with database library abstractions
    - Leverage MongoDB aggregation pipelines for complex queries
    - Follow the repository pattern for data access
    - Use database transactions where appropriate
    - Implement proper indexing strategies for performance
    - Cache expensive operations using Redis

    # API & Module Patterns

    - Each entity should support both GraphQL and REST interfaces
    - GraphQL resolvers should extend BaseResolver with appropriate options
    - REST controllers should extend BaseController with appropriate options
    - Use proper parameter decorators (@Args, @Query, @Body) based on API type
    - Follow established paging, filtering, and projection patterns
    - When implementing features, leverage existing libraries and follow established patterns rather than creating new abstractions

    # Commit Message Conventions

    - Use Commitlint conventional commit messages
    - Example:
      - feat(accounts): add organization invitation flow
      - fix(database): correct MongoDB connection retry logic
      - chore: update dependencies and NX configuration
      - refactor(core): improve authentication guard implementation
      - docs: update API documentation with new endpoints
      - test: add integration tests for payment processing

    # Environment Variables

    - `MONGODB_URI`: MongoDB connection string
    - `MONGODB_DATABASE`: Database name
    - `PROJECT_PREFIX`: Application prefix for service names
    - `CACHE_TTL`: Cache expiration time
    - `DEFAULT_CACHE_HINT`: Default cache hint value
    - `HOSTNAME`: Service hostname

    # Environment Variables Guidelines

    - Store environment variables in the appropriate `.env` file for each environment (e.g., `.env.local`, `.env.staging`).
    - Do not hardcode secrets or sensitive values in code.
    - Reference environment variables using process.env in Node.js code.

    # General Guidelines

    - Consolidate multiple changes to the same file into a single coherent diff
    - Maintain consistent code structure and style across the project
    - Provide rationale for any significant logic changes
    - Ask questions when facing uncertainty about implementation approaches
    - Use proper exception handling with NestJS HttpException types
    - Leverage module configuration and dependency injection
    - Use GraphQL decorators properly for type safety
    - Implement both GraphQL and REST interfaces for entities

    # Development Tools & Infrastructure

    - Build System: NX monorepo
    - Package Manager: npm
    - Testing: Jest
    - GraphQL: Apollo Server and Code Generation
    - Database: MongoDB with custom abstractions
    - Caching: Redis
    - Background Jobs: BullMQ
    - Deployment: Docker containers
    - CI/CD: GitHub Actions
    - API Documentation: Swagger/OpenAPI

    # Seed Project Integration Instructions

    ## Purpose

    These instructions define how to work with the Seed project and project-specific code in this repository. They are optimized for LLMs and Copilot. Follow all rules and patterns below.

    ## Directory Ownership

    - **Seed-only directories** are maintained upstream in the Seed project and merged into this repository. Do not add project-specific logic to these directories.
    - **Project-specific directories** are unique to this repository. Add custom features and logic here.
    - When adding new shared functionality, prefer the Seed project if it is generic and reusable.

    ## Directory Structure

    ```
    apps/
      api/           # REST API (Seed + Project-specific)
      cli/           # Command-line tools (Project-specific)
      gateway/       # GraphQL gateway (Seed only)
      oidc/          # OpenID Connect provider (Seed only)
      services/
        core/        # Core domain services (Seed only)
        ecommerce/   # E-commerce services (Seed only)
        ...          # Project-specific services
      subscriptions/ # GraphQL subscriptions (Seed + Project-specific)
      workers/
        notifications/ # Background notifications (Seed + Project-specific)
        webhooks/      # Webhooks processing (Seed + Project-specific)
        cleaner/       # Background cleaner (Seed + Project-specific)
    libs/
      accounts/      # User management (Seed only)
      core/          # Utilities and base classes (Seed only)
      database/      # Database abstractions (Seed only)
      passport/      # Auth strategies (Seed only)
      stripe/        # Stripe integration (Seed only)
      ...            # Project-specific libraries
    ```

    ## Architecture Patterns

    - **GraphQL Domain Services**: Each domain service is a separate microservice (app in `services/`) using GraphQL Federation. Develop and deploy independently.
    - **Workers**: Each background worker (e.g., notifications, webhooks, cleaner) is a separate microservice.
    - **REST API**: The REST API is a monolith that combines both Seed and project-specific functionality. All REST endpoints are served from a single process.
    - **Subscriptions**: The GraphQL subscriptions service is a monolith containing both Seed and project-specific functionality.
    - When adding new services or workers, follow the existing pattern for microservices or monoliths as appropriate.

    ## Development Workflow

    - Keep Seed and project-specific changes in separate commits. Do not mix them.
    - When merging from Seed, resolve conflicts by favoring upstream changes unless a project-specific override is required. Document overrides in the commit message.
    - When adding new core functionality, prefer the Seed project if it is generic and reusable.
    - When adding project-specific features, keep them isolated from Seed code to minimize merge conflicts.
    - Document any changes that affect both Seed and project-specific code.

    ## Do/Don't Rules

    - **Do**: Use Seed-only directories for generic, reusable code.
    - **Do**: Use project-specific directories for custom logic.
    - **Do**: Keep commits for Seed and project-specific changes separate.
    - **Don't**: Add project-specific logic to Seed-only directories.
    - **Don't**: Mix Seed and project-specific changes in a single commit.

    ## FAQ

    - **Should this code go in Seed or project-specific?**
      - If it is generic and reusable, put it in Seed.
      - If it is unique to this project, keep it project-specific.
    - **How do I upstream changes?**
      - Create a separate commit or PR with only Seed-related changes and submit it to the Seed repository.
    - **How do I merge Seed updates?**
      - Regularly pull from the Seed repository and resolve any conflicts, keeping Seed and project-specific changes separate.

    # Nx Instructions

    You are in an nx workspace using Nx 21.2.1 and npm as the package manager.

    You have access to the Nx MCP server and the tools it provides. Use them. Follow these guidelines in order to best help the user:

    ## General Guidelines
    - When answering questions, use the nx_workspace tool first to gain an understanding of the workspace architecture
    - For questions around nx configuration, best practices or if you're unsure, use the nx_docs tool to get relevant, up-to-date docs!! Always use this instead of assuming things about nx configuration
    - If the user needs help with an Nx configuration or project graph error, use the 'nx_workspace' tool to get any errors
    - To help answer questions about the workspace structure or simply help with demonstrating how tasks depend on each other, use the 'nx_visualize_graph' tool

    ## Generation Guidelines
    If the user wants to generate something, use the following flow:

    - learn about the nx workspace and any specifics the user needs by using the 'nx_workspace' tool and the 'nx_project_details' tool if applicable
    - get the available generators using the 'nx_generators' tool
    - decide which generator to use. If no generators seem relevant, check the 'nx_available_plugins' tool to see if the user could install a plugin to help them
    - get generator details using the 'nx_generator_schema' tool
    - you may use the 'nx_docs' tool to learn more about a specific generator or technology if you're unsure
    - decide which options to provide in order to best complete the user's request. Don't make any assumptions and keep the options minimalistic
    - open the generator UI using the 'nx_open_generate_ui' tool
    - wait for the user to finish the generator
    - read the generator log file using the 'nx_read_generator_log' tool
    - use the information provided in the log file to answer the user's question or continue with what they were doing

    ## Running Tasks Guidelines
    If the user wants help with tasks or commands (which include keywords like "test", "build", "lint", or other similar actions), use the following flow:
    - Use the 'nx_current_running_tasks_details' tool to get the list of tasks (this can include tasks that were completed, stopped or failed).
    - If there are any tasks, ask the user if they would like help with a specific task then use the 'nx_current_running_task_output' tool to get the terminal output for that task/command
    - Use the terminal output from 'nx_current_running_task_output' to see what's wrong and help the user fix their problem. Use the appropriate tools if necessary
    - If the user would like to rerun the task or command, always use `nx run <taskId>` to rerun in the terminal. This will ensure that the task will run in the nx context and will be run the same way it originally executed
    - If the task was marked as "continuous" do not offer to rerun the task. This task is already running and the user can see the output in the terminal. You can use 'nx_current_running_task_output' to get the output of the task to verify the output.


    ## CI Error Guidelines
    If the user wants help with fixing an error in their CI pipeline, use the following flow:
    - Retrieve the list of current CI Pipeline Executions (CIPEs) using the 'nx_cloud_cipe_details' tool
    - If there are any errors, use the 'nx_cloud_fix_cipe_failure' tool to retrieve the logs for a specific task
    - Use the task logs to see what's wrong and help the user fix their problem. Use the appropriate tools if necessary
    - Make sure that the problem is fixed by running the task that you passed into the 'nx_cloud_fix_cipe_failure' tool

docs:
  - uses: unbrandedtech/nestjs-docs
  - name: Nx
    startUrl: https://nx.dev/getting-started/intro
  - name: Apollo GraphQL
    startUrl: https://www.apollographql.com/docs/apollo-server/
  - name: MongoDB Node.js Driver
    startUrl: https://mongodb.github.io/node-mongodb-native/
  - name: OIDC Provider
    startUrl: https://github.com/panva/node-oidc-provider
  - name: BullMQ
    startUrl: https://docs.bullmq.io/

prompts:
  - name: Create Entity
    description: Generate a new entity with GraphQL and REST interfaces
    prompt: |
      Create a new entity called {{entityName}} with the following properties:
      {{properties}}

      Generate:
      1. The entity class extending MongoDBEntity
      2. A GraphQL resolver extending BaseResolver
      3. A REST controller extending BaseController
      4. Appropriate service classes
      5. Module configuration

      Follow the project's conventions for folder structure and naming.

  - name: Create Module
    description: Generate a NestJS module with required components
    prompt: |
      Create a new module called {{moduleName}} with the following functionality:
      {{functionality}}

      Include the necessary:
      - Module class with imports and providers
      - Service class(es)
      - Controller and/or Resolver as appropriate
      - Required DTOs and entity classes

  - name: Create GraphQL Query
    description: Generate a GraphQL query implementation
    prompt: |
      Implement a GraphQL query called {{queryName}} that:
      {{queryDescription}}

      Include:
      - Resolver method with appropriate decorators
      - Service method implementation
      - Required input types and return types
      - Proper error handling

  - name: Generate NX Library
    description: Instructions for creating a new NX library
    prompt: |
      Explain how to create a new NX library called {{libraryName}} with:
      {{libraryDescription}}

      Include:
      - NX command to generate the library
      - Folder structure
      - Module configuration
      - Example usage from other projects

models:
  - name: LiteLLM
    provider: openai
    model: AUTODETECT
    title: LiteLLM
    apiBase: https://litellm.realityconnect.tech/v1
    apiKey: sk-BaLAy6rPkQ9Y_d61pkz3VQ
    capabilities:
      - tool_use
    override:
      roles:
        - chat
        - edit
        - apply

  - uses: mistral/codestral
    with:
      MISTRAL_API_KEY: Juycms6I8ZmE1T3nuBcaqdBJ7HnfjMPC
    override:
      roles:
        - autocomplete

  - name: voyage-code-3
    model: voyage-code-3
    provider: openai
    apiBase: https://litellm.realityconnect.tech/v1
    apiKey: sk-H9BJuVGxURjtEZttO5RVqg
    roles:
      - embed

  - name: Voyage AI rerank-2
    model: rerank-2
    provider: openai
    apiBase: https://litellm.realityconnect.tech/v1
    apiKey: sk-H9BJuVGxURjtEZttO5RVqg
    roles:
      - rerank

context:
  - uses: continuedev/diff-context
  - uses: continuedev/codebase-context
  - uses: continuedev/url-context
  - uses: continuedev/folder-context
  - uses: continuedev/terminal-context
  - uses: continuedev/code-context
  - uses: continuedev/file-context
  - uses: continuedev/current-file-context
  - uses: continuedev/open-files-context
  - uses: continuedev/docs-context
  - uses: continuedev/repo-map-context
    params:
      includeSignatures: false
  - uses: continuedev/problems-context
  - uses: continuedev/clipboard-context
  - uses: continuedev/os-context

mcpServers:
  - name: Nx
    command: npx
    args:
      - 'nx-mcp'
