{"inputs": [{"type": "promptString", "id": "local-access-token", "description": "Access Token", "password": true}, {"type": "promptString", "id": "develop-access-token", "description": "Access Token", "password": true}, {"type": "promptString", "id": "production-access-token", "description": "Access Token", "password": true}], "servers": {"local-graphql-api": {"command": "npx", "args": ["-y", "mcp-graphql", "--endpoint", "https://gateway.local.rc/graphql", "--headers={\"Authorization\":\"Bearer ${input:local-access-token}\"}"], "env": {"NODE_TLS_REJECT_UNAUTHORIZED": "0"}}, "develop-graphql-api": {"command": "npx", "args": ["-y", "mcp-graphql", "--endpoint", "https://seed-gateway-develop.realityconnect.space/graphql", "--headers={\"Authorization\":\"Bearer ${input:develop-access-token}\"}"], "env": {"NODE_TLS_REJECT_UNAUTHORIZED": "0"}}, "production-graphql-api": {"command": "npx", "args": ["-y", "mcp-graphql", "--endpoint", "https://seed-gateway.realityconnect.space/graphql", "--headers={\"Authorization\":\"Bearer ${input:production-access-token}\"}"], "env": {"NODE_TLS_REJECT_UNAUTHORIZED": "0"}}}}