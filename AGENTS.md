# AGENTS.md

## Build, Lint, and Test

- Build: `npm run build` (all) or `nx build <project>`
- Lint: `npm run lint` (all) or `nx lint <project>`
- Test: `npm run test` (all) or `nx test <project>`
- Run a single test: `nx test <project> --testNamePattern='<name>'`
- Format: `npm run format`

## Code Style & Conventions

- Use Prettier: single quotes, no semicolons, 120-char lines, consistent spacing
- ESLint: group/alpha imports (builtin → external → internal → relative)
- TypeScript: prefer decorators, dependency injection, avoid static methods
- Naming: meaningful, intent-revealing, camelCase for variables/functions, PascalCase for classes/types
- Functions: small, single-purpose, descriptive names, few args
- Error handling: use exceptions (NestJS HttpException), never error codes
- Comments: only when code is not self-explanatory
- Security: never hardcode secrets, use env vars
- Tests: prefer integration, minimal mocks, test real use cases
- Follow NX monorepo and project structure; leverage internal libs
- Use Commitlint conventional commit messages
- Do not add project-specific logic to Seed-only directories (see .github/instructions/seed.instructions.md)
- For Nx-specific help, use nx tools and follow .github/instructions/nx.instructions.md

See `.github/copilot-instructions.md` and `.github/instructions/seed.instructions.md` for more details.
