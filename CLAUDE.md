# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands

- `npm run db` - Start local databases (Redis, MongoDB) using Docker
- `npm start` - Start development server with all services
- `npm start -- --projects=oidc,gateway` - Start specific services only
- `npm run build` - Build all applications for production
- `npm run test` - Run unit tests across all projects
- `npm run lint` - Run ESLint with auto-fix across all projects
- `npm run migrate` - Run database migrations for all services
- `npm run migrate:status` - Check migration status

### Single Project Commands

- `nx test <project-name>` - Run tests for specific project
- `nx lint <project-name>` - Lint specific project
- `nx build <project-name>` - Build specific project
- `nx serve <project-name>` - Serve specific project

### Authentication & Development

- `npm run token` - Generate OIDC token for API testing (use <EMAIL> / test)
- Local dev URL: https://gateway.local.rc/graphql
- Apollo Sandbox preflight script: `tools/apollo.preflight.js`

## Architecture Overview

### Seed Project Integration

This repository integrates with the upstream Seed project. **Critical:** Understand directory ownership before making changes:

**Seed-only directories** (maintained upstream - DO NOT add project-specific logic):

- `apps/gateway/` - GraphQL API gateway with Apollo Federation
- `apps/oidc/` - OpenID Connect identity provider
- `apps/services/core/` - Core domain GraphQL services
- `apps/services/ecommerce/` - E-commerce GraphQL services
- `libs/accounts/` - User management, organizations, roles, invitations
- `libs/core/` - Base classes, guards, decorators, utilities, notifications, auth
- `libs/database/` - MongoDB entity services, repositories, GraphQL/REST abstractions
- `libs/passport/` - OAuth strategies (Google, Facebook, GitHub, Apple, Discord, Twitch, Stripe)
- `libs/stripe/` - Payment processing integration

**Project-specific directories** (unique to this repository):

- `apps/cli/` - Command-line interface with nest-commander
- Additional project-specific services and libraries

**Mixed directories** (Seed + Project-specific):

- `apps/api/` - REST API endpoints (monolith combining both)
- `apps/subscriptions/` - GraphQL subscriptions service (monolith combining both)
- `apps/workers/notifications/` - Notification processing worker
- `apps/workers/webhooks/` - Webhook processing worker
- `apps/workers/cleaner/` - Background cleanup worker

### NX Monorepo Structure

This uses NX monorepo architecture with microservices pattern:

- **GraphQL Domain Services**: Each domain service is a separate microservice using Apollo Federation
- **Workers**: Each background worker is a separate microservice
- **REST API**: Monolith combining Seed and project-specific functionality
- **Subscriptions**: Monolith containing both Seed and project-specific functionality

### Technology Stack

- **Framework:** NestJS 11 with TypeScript
- **API:** GraphQL (Apollo Server Federation) + REST
- **Database:** MongoDB with custom abstractions
- **Authentication:** OIDC Provider + Passport.js OAuth strategies
- **Caching:** Redis
- **Background Jobs:** BullMQ
- **Testing:** Jest
- **Package Manager:** npm (with GitHub Packages registry)

### Key Base Classes & Patterns

- `MongoDBEntity` - Base entity with MongoDB functionality (`@reality-connect/database`)
- `BaseResolver` - GraphQL resolver base class (`@reality-connect/database`)
- `BaseController` - REST controller base class (`@reality-connect/database`)
- `@EntityType()` - Decorator for entity definitions with GraphQL/Swagger metadata
- `@CheckPolicies()` - Authorization guards for access control
- `@Private/@Hidden` - Field-level visibility control decorators

### Import Path Aliases

Use these TypeScript path aliases for internal libraries:

- `@reality-connect/core` - Core utilities and base classes
- `@reality-connect/database` - Database abstractions
- `@reality-connect/accounts` - Account management
- `@reality-connect/passport` - OAuth strategies
- `@reality-connect/stripe` - Payment integration

## Development Guidelines

### Seed Project Integration Rules

**CRITICAL:** Always follow these rules when making changes:

- **DO NOT** add project-specific logic to Seed-only directories
- **DO** use project-specific directories for custom features
- **DO** keep Seed and project-specific changes in separate commits
- **DO NOT** mix Seed and project-specific changes in a single commit
- When merging from Seed, favor upstream changes unless project-specific override is required
- When adding new shared functionality, prefer the Seed project if generic and reusable
- Document any overrides or changes affecting both Seed and project-specific code

### Code Style

- Single quotes, no semicolons, 120 character line width
- Import grouping: builtin → external → internal → relative (with newlines between)
- Alphabetize imports within groups
- Use TypeScript decorators following NestJS conventions
- Leverage dependency injection over static methods

### Entity Implementation Pattern

Each entity should implement both GraphQL and REST interfaces:

- GraphQL resolvers extend `BaseResolver`
- REST controllers extend `BaseController`
- Use appropriate decorators (`@Args`, `@Query`, `@Body`) per API type
- Follow established paging, filtering, and projection patterns

### Database Patterns

- Use Entity pattern with MongoDB abstractions
- Leverage aggregation pipelines for complex queries
- Implement proper indexing strategies
- Use Redis for caching expensive operations
- Apply database transactions where appropriate

### Testing Philosophy

- Prioritize integration tests over isolated unit tests
- Test through higher-level integration rather than mocking internals
- Only mock external dependencies when necessary
- Focus on real use cases rather than implementation details

## Environment Setup

### Prerequisites

- Node.js 24+
- Docker (for local databases)
- GitHub Packages access for @reality-connect scope

### First-time Setup

1. `npm login --scope=@reality-connect --registry=https://npm.pkg.github.com`
2. `npm install`
3. `npm run db` (start databases)
4. `npm run migrate` (initialize database)
5. `npm start` (start development server)

### SSL Certificate Setup

- First run requires sudo/administrator for certificate authority registration
- Close Firefox before first run
- Uses devcert for local HTTPS development

## Common Patterns

### CLI Commands

Generate using nest-commander-schematics:

- `nx g nest-commander-schematics:command --sourceRoot="apps/cli/src/app" --name=<command-name>`
- `nx g nest-commander-schematics:question --sourceRoot="apps/cli/src/app" --name=<command-name>`

### Code Generation

- Applications: `nx generate @nx/nest:application <nest-app>`
- Libraries: `nx g @nx/nest:library <nest-lib> --publishable --importPath="@reality-connect/<nest-lib>"`

### Apollo Federation

- GraphQL services use Apollo Federation for schema stitching
- Each service publishes its schema to the gateway
- Use `npm run apollo:check` and `npm run apollo:publish` for schema management
