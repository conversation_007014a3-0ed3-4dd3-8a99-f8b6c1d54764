---
description: Seed Project Integration Instructions (Backend)
---

These instructions define how to work with the Seed project and project-specific code in this repository. They are optimized for LLMs and Copilot. Follow all rules and patterns below.

# Directory Ownership

- **Seed-only directories** are maintained upstream in the Seed project and merged into this repository. Do not add project-specific logic to these directories.
- **Project-specific directories** are unique to this repository. Add custom features and logic here.
- When adding new shared functionality, prefer the Seed project if it is generic and reusable.

# Directory Structure

```
apps/
  api/           # REST API (Seed + Project-specific)
  cli/           # Command-line tools (Project-specific)
  gateway/       # GraphQL gateway (Seed only)
  oidc/          # OpenID Connect provider (Seed only)
  services/
    core/        # Core domain services (Seed only)
    ecommerce/   # E-commerce services (Seed only)
    ...          # Project-specific services
  subscriptions/ # GraphQL subscriptions (Seed + Project-specific)
  workers/
    notifications/ # Background notifications (Seed + Project-specific)
    webhooks/      # Webhooks processing (Seed + Project-specific)
    cleaner/       # Background cleaner (Seed + Project-specific)
libs/
  accounts/      # User management (Seed only)
  core/          # Utilities and base classes (Seed only)
  database/      # Database abstractions (Seed only)
  passport/      # Auth strategies (Seed only)
  stripe/        # Stripe integration (Seed only)
  ...            # Project-specific libraries
```

# Architecture Patterns

- **GraphQL Domain Services**: Each domain service is a separate microservice (app in `services/`) using GraphQL Federation. Develop and deploy independently.
- **Workers**: Each background worker (e.g., notifications, webhooks, cleaner) is a separate microservice.
- **REST API**: The REST API is a monolith that combines both Seed and project-specific functionality. All REST endpoints are served from a single process.
- **Subscriptions**: The GraphQL subscriptions service is a monolith containing both Seed and project-specific functionality.
- When adding new services or workers, follow the existing pattern for microservices or monoliths as appropriate.

# Development Workflow

- Keep Seed and project-specific changes in separate commits. Do not mix them.
- When merging from Seed, resolve conflicts by favoring upstream changes unless a project-specific override is required. Document overrides in the commit message.
- When adding new core functionality, prefer the Seed project if it is generic and reusable.
- When adding project-specific features, keep them isolated from Seed code to minimize merge conflicts.
- Document any changes that affect both Seed and project-specific code.

# Do/Don't Rules

- **Do**: Use Seed-only directories for generic, reusable code.
- **Do**: Use project-specific directories for custom logic.
- **Do**: Keep commits for Seed and project-specific changes separate.
- **Don't**: Add project-specific logic to Seed-only directories.
- **Don't**: Mix Seed and project-specific changes in a single commit.

# FAQ

- **Should this code go in Seed or project-specific?**
  - If it is generic and reusable, put it in Seed.
  - If it is unique to this project, keep it project-specific.
- **How do I upstream changes?**
  - Create a separate commit or PR with only Seed-related changes and submit it to the Seed repository.
- **How do I merge Seed updates?**
  - Regularly pull from the Seed repository and resolve any conflicts, keeping Seed and project-specific changes separate.

---

For more details, see the Seed project documentation: https://github.com/reality-connect/seed-backend
