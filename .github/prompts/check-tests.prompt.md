---
mode: 'edit'
description: 'Check tests for bypasses and mocking to force testing results.'
---

Can you help me check these tests to make sure they are properly testing the main functionality?

- Verify that there are no bypasses or tricks that would make the tests pass without actually testing the main functionality. Including mocking to force testing results.
- If you find workaround within the tests that should be considered temporary and should be removed. We should not be working around problems.
