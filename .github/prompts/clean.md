Clean up the code, logs and comments.
I like to leave important comments that can really help reading the code or inform other developers but remove unnecessary and obvious comments.
If you find JSDocs you should leave then while cleaning up unnecessary parts.
You could also add missing JSDocs when you feel it is important.
In general I want to keep comments and JSDocs while not being too verbose. But still include object parameters' fields.
