name: Detect Changes
description: Nx detect changes
outputs:
  affected-apps:
    description: Space-delimited string of affected apps
    value: ${{ steps.detect-changes.outputs.affected-apps }}
  affected-libs:
    description: Space-delimited string of affected libs
    value: ${{ steps.detect-changes.outputs.affected-libs }}
  affected-all:
    description: Space-delimited string of affected projects
    value: ${{ steps.detect-changes.outputs.affected-all }}
  skip:
    description: Whether or not code changes affect the apps or libs
    value: ${{ steps.detect-changes.outputs.skip }}
  force-release:
    description: Whether or not code changes affect the apps or libs
    value: ${{ steps.detect-changes.outputs.force-release }}
  affected-matrix:
    description: JSON array matrix for affected projects
    value: ${{ steps.detect-changes.outputs.affected-matrix }}
runs:
  using: composite
  steps:
    - name: Detect Changes
      id: detect-changes
      shell: bash
      run: |
        set -xe

        if git log --format=%B ${{env.NX_BASE}}...$GITHUB_SHA | grep "\[force-release\]" >/dev/null; then
          echo "force-release=true" >> $GITHUB_OUTPUT

          echo "affected-apps=$(npx nx show projects --projects "apps/**" --exclude tag:disabled | tr '\n' ',')" >> $GITHUB_OUTPUT
          echo "affected-libs=$(npx nx show projects --projects "libs/**" --exclude tag:disabled | tr '\n' ',')" >> $GITHUB_OUTPUT

          echo "affected-all=$(npx nx show projects --exclude tag:disabled | tr '\n' ',')" >> $GITHUB_OUTPUT
          echo "affected-matrix=[$(npx nx show projects --exclude tag:disabled | tr '\n' ',')]" >> $GITHUB_OUTPUT

          if [ -n "$(npx nx show projects --exclude tag:disabled | tr '\n' ',')" ]; then
            echo "skip=false" >> $GITHUB_OUTPUT
          else
            echo "skip=true" >> $GITHUB_OUTPUT
          fi
        else
          echo "force-release=false" >> $GITHUB_OUTPUT

          echo "affected-apps=$(npx nx show projects --affected --projects "apps/**" --exclude tag:disabled | tr '\n' ',')" >> $GITHUB_OUTPUT
          echo "affected-libs=$(npx nx show projects --affected --projects "libs/**" --exclude tag:disabled | tr '\n' ',')" >> $GITHUB_OUTPUT

          echo "affected-all=$(npx nx show projects --affected --exclude tag:disabled | tr '\n' ',')" >> $GITHUB_OUTPUT
          echo "affected-matrix=[$(npx nx show projects --affected --exclude tag:disabled | tr '\n' ',')]" >> $GITHUB_OUTPUT

          if [ -n "$(npx nx show projects --affected --exclude tag:disabled | tr '\n' ',')" ]; then
            echo "skip=false" >> $GITHUB_OUTPUT
          else
            echo "skip=true" >> $GITHUB_OUTPUT
          fi
        fi
