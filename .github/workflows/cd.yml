name: Continuous Deployment

on:
  push:
    branches: [main, develop, stage, temp]
    tags-ignore: ['**']

env:
  NODE_VERSION: '24'
permissions:
  actions: write
  id-token: write
  contents: write
  packages: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  setup:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://npm.pkg.github.com'
          scope: '@reality-connect'
          always-auth: true

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Install Dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - uses: ./.github/actions/detect-changes
        id: nx-changes

      - name: Set Environment Variables
        shell: bash
        run: |
          set -xe
          echo "version_tag=$(echo ${GITHUB_REF#refs/heads/})-$RUN_NUMBER" >> $GITHUB_OUTPUT
          echo "chart_version_tag=$(node -p "require('./package.json').version")-$(echo ${GITHUB_REF#refs/heads/}).$RUN_NUMBER" >> $GITHUB_OUTPUT

          if [ "$GITHUB_REF" = "refs/heads/main" ]; then
            echo "docker_tag=latest" >> $GITHUB_OUTPUT
            echo "full_distribution_name=$(node -p "require('./package.json').name")" >> $GITHUB_OUTPUT
            echo "namespace=production" >> $GITHUB_OUTPUT
            echo "environment=production" >> $GITHUB_OUTPUT
            echo "database_stage=production" >> $GITHUB_OUTPUT
          else
            echo "docker_tag=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_OUTPUT
            echo "full_distribution_name=$(node -p "require('./package.json').name")-$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_OUTPUT
            echo "namespace=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
            echo "environment=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
            echo "database_stage=-${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
          fi

          echo "aws_access_key_id=AWS_ACCESS_KEY_ID" >> $GITHUB_OUTPUT
          echo "aws_secret_access_key=AWS_SECRET_ACCESS_KEY" >> $GITHUB_OUTPUT
          echo "aws_cluster_name=AWS_CLUSTER_NAME" >> $GITHUB_OUTPUT

          if [ "$GITHUB_EVENT_NAME" = 'pull_request' ]; then
            if [ "$GITHUB_BASE_REF" = "main" ]; then
              echo "variant=current" >> $GITHUB_OUTPUT
            else
              echo "variant=$GITHUB_BASE_REF" >> $GITHUB_OUTPUT
            fi
          else
            if [ "$GITHUB_REF" = "refs/heads/main" ]; then
              echo "variant=current" >> $GITHUB_OUTPUT
            else
              echo "variant=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
            fi
          fi
          echo "distribution_name=$(node -p "require('./package.json').name")" >> $GITHUB_OUTPUT
          echo "stage=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
        id: variables
        env:
          RUN_NUMBER: ${{ github.run_number }}

    outputs:
      affected-apps: ${{ steps.nx-changes.outputs.affected-apps }}
      affected-libs: ${{ steps.nx-changes.outputs.affected-libs }}
      affected-all: ${{ steps.nx-changes.outputs.affected-all }}
      skip: ${{ steps.nx-changes.outputs.skip }}
      force-release: ${{ steps.nx-changes.outputs.force-release }}
      affected-matrix: ${{ steps.nx-changes.outputs.affected-matrix }}

      version_tag: ${{ steps.variables.outputs.version_tag }}
      chart_version_tag: ${{ steps.variables.outputs.chart_version_tag }}
      docker_tag: ${{ steps.variables.outputs.docker_tag }}
      distribution_name: ${{ steps.variables.outputs.distribution_name }}
      full_distribution_name: ${{ steps.variables.outputs.full_distribution_name }}

      environment: ${{ steps.variables.outputs.environment }}
      namespace: ${{ steps.variables.outputs.namespace }}
      variant: ${{ steps.variables.outputs.variant }}
      database_stage: ${{ steps.variables.outputs.database_stage }}
      stage: ${{ steps.variables.outputs.stage }}

      aws_cluster_name: ${{ steps.variables.outputs.aws_cluster_name }}

  lint:
    needs: [setup]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - uses: nrwl/nx-set-shas@v4

      - run: npx nx affected -t lint --parallel=3 --exclude tag:disabled

  test:
    needs: [setup]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Start Redis
        uses: supercharge/redis-github-action@1.8.0

      - name: Start MongoDB
        uses: supercharge/mongodb-github-action@1.10.0

      - uses: nrwl/nx-set-shas@v4

      - name: Run Unit Tests
        run: npx nx affected -t test --parallel=3 --ci --coverage --exclude tag:disabled
        env:
          NODE_OPTIONS: '$NODE_OPTIONS --experimental-vm-modules'

      - name: Run E2E Tests
        run: npx nx affected -t e2e --parallel=3 --ci --configuration production --exclude tag:disabled

  apollo-check:
    needs: [setup]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        if: needs.setup.outputs.force-release == 'false'
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        if: needs.setup.outputs.force-release == 'false'
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        if: needs.setup.outputs.force-release == 'false'
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Start Redis
        if: needs.setup.outputs.force-release == 'false'
        uses: supercharge/redis-github-action@1.8.0

      - name: Start MongoDB
        if: needs.setup.outputs.force-release == 'false'
        uses: supercharge/mongodb-github-action@1.10.0

      - uses: nrwl/nx-set-shas@v4
        if: needs.setup.outputs.force-release == 'false'

      - name: Apollo Check
        if: needs.setup.outputs.force-release == 'false'
        run: npx nx affected -t apollo-check --nx-bail --variant ${{ needs.setup.outputs.variant }} --exclude tag:disabled --parallel 1
        env:
          APOLLO_KEY: ${{ secrets.APOLLO_KEY }}
          APOLLO_GRAPH_ID: ${{ vars.APOLLO_GRAPH_ID }}

  build:
    needs: [setup, lint, test, apollo-check]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - uses: actions/cache@v4
        with:
          path: dist
          key: dist/${{ github.run_id }}

      - uses: nrwl/nx-set-shas@v4

      - name: Build
        run: npx nx run-many -t build --prod --exclude=cli  --exclude tag:disabled

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/github-oidc-provider-aws
          role-session-name: OIDCSession

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build Docker
        run: npx nx run-many -t docker-build --nx-bail --affected ${{ needs.setup.outputs.affected-apps }} --tag ${{ needs.setup.outputs.version_tag }} --exclude tag:disabled
        env:
          # DOCKER_TARGET_PLATFORM: linux/amd64,linux/arm64
          DOCKER_TARGET_PLATFORM: linux/arm64
          DOCKER_CACHE_FROM: type=gha,scope=${{ needs.setup.outputs.distribution_name }}
          DOCKER_CACHE_TO: type=gha,scope=${{ needs.setup.outputs.distribution_name }}
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}

  deploy-k8s:
    needs: [setup, build]
    if: needs.setup.outputs.skip == 'false'
    environment:
      name: ${{ needs.setup.outputs.environment }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          repository: phigital-loyalty/infrastructure
          token: ${{ secrets.GH_PAT }}
          ref: ${{ needs.setup.outputs.stage }}
          fetch-depth: 0

      - name: Setup Helm Chart
        run: |
          echo "$(npx --package=yaml-cli yaml set helm-charts/Chart.yaml name ${CHART_NAME})" > helm-charts/Chart.yaml
          echo "$(npx --package=yaml-cli yaml set helm-charts/Chart.yaml version ${VERSION_TAG})" > helm-charts/Chart.yaml
          echo "$(npx --package=yaml-cli yaml set helm-charts/Chart.yaml appVersion ${VERSION_TAG})" > helm-charts/Chart.yaml
        env:
          CHART_NAME: ${{ needs.setup.outputs.distribution_name }}
          VERSION_TAG: ${{ needs.setup.outputs.chart_version_tag }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID }}:role/github-oidc-provider-aws
          role-session-name: OIDCSession

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Deploy Helm
        uses: bitovi/github-actions-deploy-eks-helm@v1.2.8
        with:
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}
          cluster-name: ${{ vars[needs.setup.outputs.aws_cluster_name] }}

          chart-path: helm-charts
          name: ${{ needs.setup.outputs.distribution_name }}
          namespace: ${{ needs.setup.outputs.namespace }}
          version: ${{ needs.setup.outputs.chart_version_tag }}

          # chart-repository: oci://${{ steps.login-ecr.outputs.registry }}/${{ needs.setup.outputs.distribution_name }}-helm

          config-files: helm-charts/${{ needs.setup.outputs.distribution_name }}/variables.yaml,helm-charts/${{ needs.setup.outputs.distribution_name }}/values.yaml,helm-charts/values.yaml
          values: environment=${{ needs.setup.outputs.environment }},stage=${{ needs.setup.outputs.stage }},version_tag=${{ needs.setup.outputs.version_tag }},docker_registry=${{ steps.login-ecr.outputs.registry }}

          timeout: 5m0s
          atomic: true

  db-migrate:
    needs: [setup, build]
    if: needs.setup.outputs.skip == 'false'
    environment:
      name: ${{ needs.setup.outputs.environment }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - uses: nrwl/nx-set-shas@v4

      - name: Migrate Database
        run: npx nx ${{ needs.setup.outputs.force-release == 'true' && 'run-many' || 'affected' }} -t migrate --parallel=1 --ci --exclude tag:disabled
        env:
          MONGODB_URI: ${{ secrets.MONGODB_URI }}
          MONGODB_DATABASE: ${{ vars.MONGODB_DATABASE }}

  apollo-publish:
    needs: [setup, deploy-k8s]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Start Redis
        uses: supercharge/redis-github-action@1.8.0

      - name: Start MongoDB
        uses: supercharge/mongodb-github-action@1.10.0

      - uses: nrwl/nx-set-shas@v4

      - name: Apollo Publish
        run: npx nx ${{ needs.setup.outputs.force-release == 'true' && 'run-many' || 'affected' }} -t apollo-publish --variant ${{ needs.setup.outputs.variant }} --exclude tag:disabled --parallel 1
        env:
          APOLLO_KEY: ${{ secrets.APOLLO_KEY }}
          APOLLO_GRAPH_ID: ${{ vars.APOLLO_GRAPH_ID }}
