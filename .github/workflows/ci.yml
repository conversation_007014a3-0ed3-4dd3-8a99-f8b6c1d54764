name: Continuous Integration

on:
  pull_request:
    branches: [main, develop, stage, temp]
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '24'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  setup:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://npm.pkg.github.com'
          scope: '@reality-connect'
          always-auth: true

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Install Dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4

      - uses: ./.github/actions/detect-changes
        id: nx-changes

      - name: Set Environment Variables
        shell: bash
        run: |
          set -xe
          if [ "$GITHUB_EVENT_NAME" = 'pull_request' ]; then
            if [ "$GITHUB_BASE_REF" = "main" ]; then
              echo "variant=current" >> $GITHUB_OUTPUT
            else
              echo "variant=$GITHUB_BASE_REF" >> $GITHUB_OUTPUT
            fi
          else
            if [ "$GITHUB_REF" = "refs/heads/main" ]; then
              echo "variant=current" >> $GITHUB_OUTPUT
            else
              echo "variant=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
            fi
          fi

          echo "aws_access_key_id=AWS_ACCESS_KEY_ID" >> $GITHUB_OUTPUT
          echo "aws_secret_access_key=AWS_SECRET_ACCESS_KEY" >> $GITHUB_OUTPUT
          echo "aws_cluster_name=AWS_CLUSTER_NAME" >> $GITHUB_OUTPUT
        id: variables

    outputs:
      affected-apps: ${{ steps.nx-changes.outputs.affected-apps }}
      affected-libs: ${{ steps.nx-changes.outputs.affected-libs }}
      affected-all: ${{ steps.nx-changes.outputs.affected-all }}
      skip: ${{ steps.nx-changes.outputs.skip }}
      force-release: ${{ steps.nx-changes.outputs.force-release }}
      affected-matrix: ${{ steps.nx-changes.outputs.affected-matrix }}
      variant: ${{ steps.variables.outputs.variant }}

      aws_access_key_id: ${{ steps.variables.outputs.aws_access_key_id }}
      aws_secret_access_key: ${{ steps.variables.outputs.aws_secret_access_key }}
      aws_cluster_name: ${{ steps.variables.outputs.aws_cluster_name }}

  lint:
    needs: [setup]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - uses: nrwl/nx-set-shas@v4

      - run: npx nx affected --target=lint --parallel=3 --exclude tag:disabled

  test:
    needs: [setup]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    env:
      APOLLO_VCS_COMMIT: ${{ github.event.pull_request.head.sha }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Start Redis
        uses: supercharge/redis-github-action@1.8.0

      - name: Start MongoDB
        uses: supercharge/mongodb-github-action@1.10.0

      - uses: nrwl/nx-set-shas@v4

      - name: Run Unit Tests
        run: npx nx affected --target=test --parallel=3 --ci --coverage --exclude tag:disabled
        env:
          NODE_OPTIONS: '$NODE_OPTIONS --experimental-vm-modules'

      - name: Run E2E Tests
        run: npx nx affected --target=e2e --parallel=3 --ci --configuration production --exclude tag:disabled

  apollo-check:
    needs: [setup]
    if: needs.setup.outputs.skip == 'false'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: node_modules/${{ github.run_id }}

      - name: Start Redis
        uses: supercharge/redis-github-action@1.8.0

      - name: Start MongoDB
        uses: supercharge/mongodb-github-action@1.10.0

      - uses: nrwl/nx-set-shas@v4

      - name: Apollo Check
        run: npx nx ${{ needs.setup.outputs.force-release == 'true' && 'run-many' || 'affected' }} --target=apollo-check --variant ${{ needs.setup.outputs.variant }} --exclude tag:disabled  --parallel 1
        env:
          APOLLO_KEY: ${{ secrets.APOLLO_KEY }}
          APOLLO_GRAPH_ID: ${{ vars.APOLLO_GRAPH_ID }}
