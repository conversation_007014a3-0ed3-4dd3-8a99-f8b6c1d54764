These instructions apply to the backend of the project.

# Project Context

- Framework: NestJS 11
- Language: TypeScript (Node.js v23+)
- API Layers: GraphQL with Apollo Server (federation) and REST
- Data Layer: MongoDB with custom database abstractions
- Authentication: OIDC Provider & Passport.js strategies
- Project Structure: NX monorepo architecture

For directory structure and ownership, see `instructions/seed.instructions.md`.

# Internal Libraries & Patterns

## Key Libraries (from libs/)

- core: Base classes, decorators, guards, and utilities
- database: Entity services, repositories, and MongoDB abstractions
- accounts: User management, organizations, and authentication
- passport: OAuth strategies for third-party login
- stripe: Payment integration services

## Key Services (from apps/services/)

- services-core: Core domain services
- services-ecommerce: E-commerce related services
- gateway: GraphQL API gateway with federation
- oidc: Identity provider with OpenID Connect
- workers: Background processing (notifications, webhooks, cleaner)

When implementing features, leverage existing libraries and follow established patterns rather than creating new abstractions.

## Key Base Classes & Patterns

- MongoDBEntity: Base entity class with MongoDB-specific functionality (from @phigital-loyalty/database)
- BaseResolver: Base resolver class for GraphQL endpoints (from @phigital-loyalty/database)
- BaseController: Base controller class for REST endpoints (from @phigital-loyalty/database)
- EntityType: Decorator for entity definitions with GraphQL/Swagger metadata (from @phigital-loyalty/core)
- CheckPolicies: Authorization guards for enforcing access control (from @phigital-loyalty/core)
- Private/Hidden: Decorators for field-level visibility control (from @phigital-loyalty/core)

# Code Style & Conventions

- Follow the project's Prettier configuration:
  - Use single quotes
  - Omit semicolons
  - 120 character line width
  - Maintain consistent spacing
- Follow ESLint rules for imports:
  - Group imports by type with newlines between groups
  - Alphabetize imports
  - Pattern: builtin → external → internal → relative
- Use TypeScript decorators following NestJS conventions
- Leverage dependency injection and avoid static methods when possible
- Follow object-oriented principles for service organization

# Clean Code Principles

- Code should be easy to read and understand.
- Keep the code as simple as possible. Avoid unnecessary complexity.
- Use meaningful names for variables, functions, etc. Names should reveal intent.
- Functions should be small and do one thing well.
- Function names should describe the action being performed.
- Prefer fewer arguments in functions.
- Only use comments when necessary, as they can become outdated. Instead, strive to make the code self-explanatory.
- When comments are used, they should add useful information that is not readily apparent from the code itself.
- Properly handle errors and exceptions to ensure the software's robustness.
- Use exceptions rather than error codes for handling errors.
- Consider security implications of the code. Implement security best practices to protect against vulnerabilities and attacks.

# Testing Philosophy

- Prioritize integration tests over isolated unit tests
- Focus on testing critical functionality without excessive complexity
- Test internal components through higher-level integration tests
- Use real implementations where feasible, minimizing mocks
- Only mock external dependencies when necessary
- Write tests for actual use cases rather than implementation details
- Avoid temporary workarounds or mocking to force test results

# Database Patterns

- Use the Entity pattern with database library abstractions
- Leverage MongoDB aggregation pipelines for complex queries
- Follow the repository pattern for data access
- Use database transactions where appropriate
- Implement proper indexing strategies for performance
- Cache expensive operations using Redis

# API & Module Patterns

- Each entity should support both GraphQL and REST interfaces
- GraphQL resolvers should extend BaseResolver with appropriate options
- REST controllers should extend BaseController with appropriate options
- Use proper parameter decorators (@Args, @Query, @Body) based on API type
- Follow established paging, filtering, and projection patterns
- When implementing features, leverage existing libraries and follow established patterns rather than creating new abstractions

# Commit Message Conventions

- Use Commitlint conventional commit messages
- Example:
  - feat(accounts): add organization invitation flow
  - fix(database): correct MongoDB connection retry logic
  - chore: update dependencies and NX configuration
  - refactor(core): improve authentication guard implementation
  - docs: update API documentation with new endpoints
  - test: add integration tests for payment processing

# Environment Variables

- `MONGODB_URI`: MongoDB connection string
- `MONGODB_DATABASE`: Database name
- `PROJECT_PREFIX`: Application prefix for service names
- `CACHE_TTL`: Cache expiration time
- `DEFAULT_CACHE_HINT`: Default cache hint value
- `HOSTNAME`: Service hostname

# Environment Variables Guidelines

- Store environment variables in the appropriate `.env` file for each environment (e.g., `.env.local`, `.env.staging`).
- Do not hardcode secrets or sensitive values in code.
- Reference environment variables using process.env in Node.js code.

# General Guidelines

- Consolidate multiple changes to the same file into a single coherent diff
- Maintain consistent code structure and style across the project
- Provide rationale for any significant logic changes
- Ask questions when facing uncertainty about implementation approaches
- Use proper exception handling with NestJS HttpException types
- Leverage module configuration and dependency injection
- Use GraphQL decorators properly for type safety
- Implement both GraphQL and REST interfaces for entities

# Development Tools & Infrastructure

- Build System: NX monorepo
- Package Manager: npm
- Testing: Jest
- GraphQL: Apollo Server and Code Generation
- Database: MongoDB with custom abstractions
- Caching: Redis
- Background Jobs: BullMQ
- Deployment: Docker containers
- CI/CD: GitHub Actions
- API Documentation: Swagger/OpenAPI

---

For project structure and merge policy, see `instructions/seed.instructions.md`.
For Nx-specific instructions, see `instructions/nx.instructions.md`.
