import { configs } from '@nx/eslint-plugin'
import stylistic from '@stylistic/eslint-plugin'
import prettier from 'eslint-config-prettier'
import { flatConfigs } from 'eslint-plugin-import'
import jsoncParser from 'jsonc-eslint-parser'

/** @type {import('eslint').Linter.Config[]} */
export default [
  ...configs['flat/base'],
  ...configs['flat/typescript'],
  ...configs['flat/javascript'],
  flatConfigs.recommended,
  flatConfigs.typescript,
  prettier,
  {
    ignores: ['**/dist', '**/build', '**/tmp', '**/deprecated'],
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.cts', '**/*.mts'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.*?.json',
        emitDecoratorMetadata: true,
        experimentalDecorators: true,
      },
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.cts', '**/*.mts', '**/*.js', '**/*.jsx', '**/*.cjs', '**/*.mjs'],
    plugins: {
      '@stylistic': stylistic,
    },

    settings: {
      'import/internal-regex': '@phigital-loyalty',
      'import/resolver': {
        alias: true,
        typescript: {
          alwaysTryTypes: true,
          project: ['tsconfig.base.json', 'libs/*/tsconfig.lib.json', 'apps/*/tsconfig.app.json'],
        },
        node: { extensions: ['.js', '.jsx', '.cjs', '.mjs'] },
      },
    },

    rules: {
      '@stylistic/no-extra-semi': 'off',
      '@stylistic/quotes': ['error', 'single', { avoidEscape: true, allowTemplateLiterals: 'always' }],

      '@nx/enforce-module-boundaries': [
        'error',
        {
          enforceBuildableLibDependency: true,
          allow: ['^.*/eslint(\\.base)?\\.config\\.[cm]?[jt]s$', '^@phigital-loyalty/generate-api(/.*)?$'],
          depConstraints: [
            {
              sourceTag: '*',
              onlyDependOnLibsWithTags: ['*'],
            },
          ],
        },
      ],

      'no-console': ['error', { allow: ['warn', 'error'] }],

      '@typescript-eslint/no-empty-object-type': 'error',
      '@typescript-eslint/no-unsafe-function-type': 'error',
      '@typescript-eslint/no-wrapper-object-types': 'error',

      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],

      '@typescript-eslint/consistent-type-imports': 'error',
      '@typescript-eslint/no-import-type-side-effects': 'error',

      'import/no-unresolved': ['error', { ignore: ['^k6.*'] }],
      'import/namespace': ['error', { allowComputed: true }],
      'import/order': [
        'error',
        {
          groups: ['builtin', 'external', 'internal', ['sibling', 'parent', 'index'], 'object', 'type'],
          'newlines-between': 'always',
          warnOnUnassignedImports: true,
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
        },
      ],
    },
  },
  {
    files: ['**/*.json'],
    rules: {},
    languageOptions: { parser: jsoncParser },
  },
  {
    files: ['**/package.json'],
    rules: {
      '@nx/nx-plugin-checks': 'error',
      '@nx/dependency-checks': ['warn', { ignoredDependencies: ['nx', 'typescript', '@nx/webpack'] }],
    },
    languageOptions: { parser: jsoncParser },
  },
]
