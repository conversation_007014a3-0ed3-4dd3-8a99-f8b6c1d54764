{"name": "services-tags", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/services/tags/src", "projectType": "application", "prefix": "services-tags", "tags": [], "generators": {}, "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/services/tags/webpack.config.js"}}, "serve": {"executor": "@nx/js:node"}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}, "apollo-check": {"executor": "@reality-connect/nx-apollo:check", "options": {"federated": true}}, "apollo-publish": {"executor": "@reality-connect/nx-apollo:publish", "options": {"federated": true}}, "migrate": {"executor": "@reality-connect/nx-migrate:execute"}, "migrate-status": {"executor": "@reality-connect/nx-migrate:status"}}}