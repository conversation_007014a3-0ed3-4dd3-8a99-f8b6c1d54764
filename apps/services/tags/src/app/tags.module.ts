import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import { ServiceModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database/mongodb'
import { Organization, TagsModule, User } from '@phigital-loyalty/tags'

import { AbilityFactory } from './ability.factory'
import { resolvers } from './resolvers'

@Module({
  imports: [
    ServiceModule.forRoot(AbilityFactory, {
      orphanedTypes: [User, Organization],
    }),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    TagsModule,
  ],
  providers: [...resolvers],
})
export class TagsAppModule {}
