import { <PERSON>rgs, <PERSON><PERSON>, ResolveField, Resolver } from '@nestjs/graphql'

import { CheckPolicies, Action } from '@phigital-loyalty/core'
import { FilterArgs, getPagingParametersForMongoDB } from '@phigital-loyalty/database'
import { PaginatedTags, Tag, TagService, User } from '@phigital-loyalty/tags'

@Resolver(() => User)
export class UserResolver {
  constructor(readonly tagService: TagService) {}

  @ResolveField(() => String)
  async name(@Parent() user: User) {
    return user.name || `${user.givenName} ${user.familyName}`
  }

  @ResolveField(() => String)
  async contactEmail(@Parent() user: User) {
    return user.contactEmail || user.email
  }

  @ResolveField(() => PaginatedTags)
  @CheckPolicies(Action.Read, Tag, { parentIdField: '_id', childReferenceField: 'userId' })
  async tags(@Parent() { _id }: User, @Args('filter', { nullable: true }) filter?: FilterArgs<Tag>) {
    const { query, options } = getPagingParametersForMongoDB(filter)
    const [nodes, totalCount] = await Promise.all([
      this.tagService.find({ ...query, userId: _id }, options),
      this.tagService.count({ ...filter?.where, userId: _id }),
    ])
    return { nodes, totalCount }
  }
}
