import { ModuleRef } from '@nestjs/core'
import { Parent, ResolveField, Resolver } from '@nestjs/graphql'

import { CheckPolicies, Action } from '@phigital-loyalty/core'
import { BaseResolver } from '@phigital-loyalty/database'
import { Product, ProductService, Organization, OrganizationService } from '@phigital-loyalty/tags'

@Resolver(() => Product)
export class ProductResolvers extends BaseResolver(Product) {
  constructor(
    readonly entityService: ProductService,
    readonly moduleRef: ModuleRef,
    readonly organizationService: OrganizationService,
  ) {
    super(entityService, moduleRef)
  }

  @ResolveField(() => Organization, { nullable: true })
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organization(@Parent() { organizationId }: Product): Promise<Organization | Error> {
    if (!organizationId) return

    return this.organizationService.load(organizationId)
  }
}
