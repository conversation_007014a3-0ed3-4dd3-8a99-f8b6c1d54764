import { ModuleRef } from '@nestjs/core'
import { Parent, ResolveField, Resolver } from '@nestjs/graphql'

import { CheckPolicies, Action } from '@phigital-loyalty/core'
import { BaseResolver } from '@phigital-loyalty/database'
import { Organization, OrganizationService, Program, ProgramService } from '@phigital-loyalty/tags'

@Resolver(() => Program)
export class ProgramResolvers extends BaseResolver(Program) {
  constructor(
    readonly entityService: ProgramService,
    readonly moduleRef: ModuleRef,
    readonly organizationService: OrganizationService,
  ) {
    super(entityService, moduleRef)
  }

  @ResolveField(() => Organization, { nullable: true })
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organization(@Parent() { organizationId }: Program): Promise<Organization | Error> {
    if (!organizationId) return

    return this.organizationService.load(organizationId)
  }
}
