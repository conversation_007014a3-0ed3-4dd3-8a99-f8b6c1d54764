import { ModuleRef } from '@nestjs/core'
import { Args, Info, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import graphqlFields from 'graphql-fields'

import {
  CheckPolicies,
  Action,
  CurrentAuth,
  isSystemUser,
  orgIdsForRoles,
  buildRolesMap,
  CurrentUserId,
  StatsInput,
  Stats,
  IntrospectionResponse,
} from '@phigital-loyalty/core'
import {
  BaseResolver,
  PaginationArgs,
  FilterArgs,
  getPagingParametersForMongoDB,
  WebhookService,
} from '@phigital-loyalty/database'
import {
  Activation,
  ActivationSearch,
  ActivationService,
  ChipValidationArguments,
  CreateTag,
  CreateTags,
  User,
  Organization,
  OrganizationService,
  PaginatedPerks,
  Product,
  ProductDetails,
  ProductService,
  Program,
  ProgramService,
  Tag,
  TagService,
  Tap,
  UserService,
  TagWebhookEvent,
} from '@phigital-loyalty/tags'

@Resolver(() => Tag)
export class TagResolvers extends BaseResolver(Tag, ['entity', 'entities', 'update', 'delete']) {
  constructor(
    readonly entityService: TagService,
    readonly moduleRef: ModuleRef,
    readonly userService: UserService,
    readonly organizationService: OrganizationService,
    readonly programService: ProgramService,
    readonly productService: ProductService,
    readonly activationService: ActivationService,
    readonly webhookService: WebhookService,
  ) {
    super(entityService, moduleRef)
  }

  @Query(() => Tap)
  async tap(
    @Args('id') id: string,
    @Args({
      name: 'args',
      type: () => ChipValidationArguments,
    })
    args: ChipValidationArguments,
  ) {
    return await this.authenticate(id, args).then(async (tap) => {
      await this.webhookService.emit(TagWebhookEvent.tap, tap.tag)

      return tap
    })
  }

  @Query(() => Tap)
  async authenticate(
    @Args('id') id: string,
    @Args({
      name: 'args',
      type: () => ChipValidationArguments,
    })
    args: ChipValidationArguments,
  ) {
    try {
      return await this.entityService.authenticate(id, args)
    } catch (error) {
      throw new GraphQLError(error, {
        extensions: {
          code: 'FORBIDDEN',
          http: { status: 403 },
        },
      })
    }
  }

  @Mutation(() => Tag)
  @CheckPolicies(Action.Create, Tag, { object: 'data' })
  async createTag(@Args({ name: 'data', type: () => CreateTag }) data: CreateTag) {
    const organization = await this.organizationService.findById(data.organizationId)

    if (!organization) {
      throw new Error(`Organization with id ${data.organizationId} not found`)
    }

    return this.entityService.createTag(data)
  }

  @Mutation(() => Number)
  @CheckPolicies(Action.Create, Tag, { object: 'data' })
  async createTags(@Args({ name: 'data', type: () => CreateTags }) data: CreateTags) {
    const organization = await this.organizationService.findById(data.organizationId)

    if (!organization) {
      throw new Error(`Organization with id ${data.organizationId} not found`)
    }

    if (!data.csv && !data.chips) {
      throw new Error('Either csv or chips must be provided')
    }

    return this.entityService.createTags(data)
  }

  @ResolveField(() => Organization, { nullable: true })
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organization(@Parent() { organizationId }: Tag): Promise<Organization | Error> {
    if (!organizationId) return

    return this.organizationService.load(organizationId)
  }

  @ResolveField(() => User, { nullable: true })
  @CheckPolicies(Action.Read, User, { parentIdField: 'userId' })
  async user(
    @Parent() { userId, organizationId }: Tag,
    @CurrentAuth() auth: IntrospectionResponse,
  ): Promise<User | Error> {
    if (!userId) return

    if (!auth) return

    if (userId.toString() === auth.sub) {
      return this.userService.load(userId)
    }

    if (isSystemUser(auth)) {
      return this.userService.load(userId)
    }

    if (organizationId && orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth)).includes(organizationId.toString())) {
      return this.userService.load(userId)
    }

    return
  }

  @ResolveField(() => Program, { nullable: true })
  @CheckPolicies(Action.Read, Program, { parentIdField: 'programId' })
  async program(@Parent() { programId }: Tag): Promise<Program | Error> {
    if (!programId) return

    return this.programService.load(programId)
  }

  @ResolveField(() => Product, { nullable: true })
  @CheckPolicies(Action.Read, Product, { parentIdField: 'productId' })
  async product(@Parent() { productId }: Tag): Promise<Product | Error> {
    if (!productId) return

    return this.productService.load(productId)
  }

  @ResolveField(() => PaginatedPerks)
  async perks(
    @CurrentAuth() auth: IntrospectionResponse,
    @Parent() { _id }: Tag,
    @Args('search') search: ActivationSearch,
    @Args('options', { nullable: true }) options?: PaginationArgs<Activation>,
  ) {
    return this.activationService.perksForTag(auth, _id, search, options)
  }

  @Mutation(() => Tag)
  @CheckPolicies('Claim', Tag, { id: 'id' })
  async claimTag(@CurrentUserId() currentUserId: string, @Args('id') id: string) {
    return this.entityService.claim(id, currentUserId)
  }

  @Mutation(() => Tag)
  @CheckPolicies(Action.Update, Tag, { id: 'id' })
  async releaseTag(@CurrentUserId() currentUserId: string, @Args('id') id: string) {
    return this.entityService.release(id, currentUserId)
  }

  @Mutation(() => Tag)
  @CheckPolicies(Action.Update, Tag, { id: 'id' })
  async transferTag(@CurrentUserId() currentUserId: string, @Args('id') id: string, @Args('userId') userId: string) {
    return this.entityService.transfer(id, currentUserId, userId)
  }

  @Mutation(() => [Tag])
  @CheckPolicies(Action.Manage, Tag, { filter: 'filter' })
  async attachProductToTags(
    @Args('filter') filter: FilterArgs<Tag>,
    @Args('productId') productId: string,
    @Args('productDetails') productDetails: ProductDetails,
  ) {
    const { query } = getPagingParametersForMongoDB(filter)
    return this.entityService.attachProductToTags(query, productId, productDetails)
  }

  @Mutation(() => [Tag])
  @CheckPolicies(Action.Manage, Tag, { filter: 'filter' })
  async attachProgramToTags(@Args('filter') filter: FilterArgs<Tag>, @Args('programId') programId: string) {
    const { query } = getPagingParametersForMongoDB(filter)
    return this.entityService.attachProgramToTags(query, programId)
  }

  @Mutation(() => [Tag])
  @CheckPolicies(Action.Manage, Tag, { filter: 'filter' })
  async detachProductFromTags(@Args('filter') filter: FilterArgs<Tag>) {
    const { query } = getPagingParametersForMongoDB(filter)
    return this.entityService.detachProductFromTags(query)
  }

  @Mutation(() => [Tag])
  @CheckPolicies(Action.Manage, Tag, { filter: 'filter' })
  async detachProgramFromTags(@Args('filter') filter: FilterArgs<Tag>) {
    const { query } = getPagingParametersForMongoDB(filter)
    return this.entityService.detachProgramFromTags(query)
  }

  @Query(() => Stats)
  @CheckPolicies(Action.Stats, Tag, { filter: 'filter' })
  async peopleStats(
    @Info() info: GraphQLResolveInfo,
    @Args('scope', { nullable: true }) scope?: StatsInput,
    @Args('filter', { nullable: true }) filter?: FilterArgs<Tag>,
  ) {
    const requestedFields = graphqlFields(info as never, {}, { excludedFields: ['__typename'] })
    return this.entityService.peopleStats(scope, filter?.where, Object.keys(requestedFields) as (keyof Stats)[])
  }
}
