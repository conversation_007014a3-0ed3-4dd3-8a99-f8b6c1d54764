import { BadRequestException, ForbiddenException, UnauthorizedException } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Args, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql'
import { Filter, ObjectId } from 'mongodb'
import { IntrospectionResponse } from 'openid-client'

import { CurrentAuth, isSystemUser, orgIdsForRoles, buildRolesMap, CheckPolicies, Action } from '@phigital-loyalty/core'
import { BaseResolver, FilterArgs, PaginationArgs, getPagingParametersForMongoDB } from '@phigital-loyalty/database'
import {
  Activation,
  ActivationService,
  CreateActivation,
  Organization,
  OrganizationService,
  Product,
  ProductService,
  Program,
  ProgramService,
  TagService,
  UserService,
  User,
  PaginatedUser,
  ActivationSearch,
  PaginatedActivations,
} from '@phigital-loyalty/tags'

@Resolver(() => Activation)
export class ActivationResolvers extends BaseResolver(Activation, ['update', 'delete']) {
  constructor(
    readonly entityService: ActivationService,
    readonly moduleRef: ModuleRef,
    readonly organizationService: OrganizationService,
    readonly tagService: TagService,
    readonly programService: ProgramService,
    readonly productService: ProductService,
    readonly userService: UserService,
  ) {
    super(entityService, moduleRef)
  }

  @Query(() => Activation)
  async activation(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args('id', { nullable: true }) id?: string,
    @Args('search', { nullable: true }) search?: ActivationSearch,
  ) {
    if (!auth) {
      throw new UnauthorizedException(`You must be authenticated to perform this query!`)
    }

    if (!search && !id) {
      throw new BadRequestException(`Missing either "search" or "id" argument for this query!`)
    }

    if (id) {
      const activation = await this.entityService.findById(id)

      if (!activation) {
        throw new BadRequestException(`Activation with id "${id}" not found!`)
      }

      if (isSystemUser(auth)) {
        return activation
      }

      if (orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth, ObjectId)).includes(activation.organizationId)) {
        return activation
      }

      try {
        await this.entityService.checkIfUserIsAllowedToParticipate(new ObjectId(auth.sub), activation)
      } catch {
        throw new ForbiddenException()
      }

      return activation
    }

    const filter: Filter<Activation> = {}

    if (!isSystemUser(auth)) {
      const orgIds = orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth, ObjectId))

      if (!orgIds.length) {
        return { nodes: [], totalCount: 0 }
      }

      filter.organizationId = { $in: orgIds }
    }

    return this.entityService.findOne(this.entityService.generateFilter(search, filter))
  }

  @Query(() => PaginatedActivations)
  async activations(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args('search') search: ActivationSearch,
    @Args('options', { nullable: true }) options?: PaginationArgs<Activation>,
  ) {
    if (!auth) return { nodes: [], totalCount: 0 }

    const filter: Filter<Activation> = {}

    if (!isSystemUser(auth)) {
      const orgIds = orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth, ObjectId))

      if (!orgIds.length) {
        return { nodes: [], totalCount: 0 }
      }

      filter.organizationId = { $in: orgIds }
    }

    const params = getPagingParametersForMongoDB({
      ...options,
      where: this.entityService.generateFilter(search, filter),
    })

    const [nodes, totalCount] = await Promise.all([
      this.entityService.find(params.query, params.options),
      this.entityService.count(params.query),
    ])

    return { nodes, totalCount }
  }

  @Mutation(() => Activation)
  @CheckPolicies(Action.Create, Activation)
  async createActivation(
    @Args({ name: 'data', type: () => CreateActivation })
    data: CreateActivation,
  ) {
    const organization = await this.organizationService.findById(data.organizationId)

    if (!organization) {
      throw new Error(`Organization with id ${data.organizationId} not found`)
    }

    return this.entityService.createOne({
      ...data,
      participantIds: [],
    })
  }

  @ResolveField(() => Organization, { nullable: true })
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organization(@Parent() { organizationId }: Activation): Promise<Organization | Error> {
    if (!organizationId) return

    return this.organizationService.load(organizationId)
  }

  @ResolveField(() => Number, { nullable: true })
  async numberOfParticipants(@Parent() { participantIds }: Activation): Promise<number | Error> {
    return participantIds?.length || 0
  }

  @ResolveField(() => [String], { nullable: true })
  async filter(@Parent() { filter, organizationId }: Activation, @CurrentAuth() auth: IntrospectionResponse) {
    if (!auth) return

    if (isSystemUser(auth)) {
      return filter
    }

    if (organizationId && orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth)).includes(organizationId.toString())) {
      return filter
    }

    return
  }

  @ResolveField(() => [String], { nullable: true })
  async participantIds(
    @Parent() { participantIds, organizationId }: Activation,
    @CurrentAuth() auth: IntrospectionResponse,
  ) {
    if (!auth) return []

    if (isSystemUser(auth)) {
      return participantIds || []
    }

    if (organizationId && orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth)).includes(organizationId.toString())) {
      return participantIds || []
    }

    return []
  }

  @ResolveField(() => PaginatedUser, { nullable: true })
  async participants(
    @Parent() { participantIds, organizationId }: Activation,
    @CurrentAuth() auth: IntrospectionResponse,
    @Args('filter', { nullable: true }) filter?: FilterArgs<User>,
  ) {
    if (!auth) return

    const where: Filter<User> = { _id: { $in: participantIds } }

    if (isSystemUser(auth)) {
      // DO NOTHING
    } else if (
      organizationId &&
      orgIdsForRoles(['Admin', 'Owner'], buildRolesMap(auth)).includes(organizationId.toString())
    ) {
      // DO NOTHING
    } else {
      where._id = new ObjectId(auth.sub)
    }

    if (!filter?.where) {
      filter.where = where
    }

    const { query, options } = getPagingParametersForMongoDB(filter)
    const [nodes, totalCount] = await Promise.all([
      this.userService.find({ ...query }, options),
      this.userService.count(filter?.where),
    ])
    return { nodes, totalCount }
  }

  @ResolveField(() => [Program], { nullable: true })
  async filterPrograms(@Parent() { filter }: Activation): Promise<Program[] | Error> {
    if (!filter?.programIds?.length) return []

    return this.programService.find({ _id: { $in: filter.programIds } })
  }

  @ResolveField(() => [Product], { nullable: true })
  async filterProducts(@Parent() { filter }: Activation): Promise<Product[] | Error> {
    if (!filter?.productIds) {
      return []
    }

    return this.productService.find({ _id: { $in: filter.productIds } })
  }
}
