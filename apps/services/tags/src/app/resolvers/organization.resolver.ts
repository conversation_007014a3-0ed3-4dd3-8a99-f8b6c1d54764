import { BadRequestException } from '@nestjs/common'
import { Args, Parent, ResolveField, Resolver, Query } from '@nestjs/graphql'
import { Filter, ObjectId } from 'mongodb'

import { CheckPolicies, Action } from '@phigital-loyalty/core'
import { FilterArgs, FilterOneArgs, getPagingParametersForMongoDB, parseFilter } from '@phigital-loyalty/database'
import {
  Activation,
  ActivationService,
  OidcClientService,
  Organization,
  OrganizationService,
  PaginatedActivations,
  PaginatedPrograms,
  PaginatedTags,
  Program,
  ProgramService,
  Tag,
  TagService,
  User,
  UserService,
} from '@phigital-loyalty/tags'

@Resolver(() => Organization)
export class OrganizationResolver {
  constructor(
    readonly organizationService: OrganizationService,
    readonly oidcClientService: OidcClientService,
    readonly userService: UserService,
    readonly programService: ProgramService,
    readonly activationService: ActivationService,
    readonly tagService: TagService,
  ) {}

  @ResolveField(() => PaginatedPrograms)
  @CheckPolicies(Action.Read, Program, { parentIdField: '_id', childReferenceField: 'userId' })
  async programs(@Parent() { _id }: Organization, @Args('filter', { nullable: true }) filter?: FilterArgs<Program>) {
    const { query, options } = getPagingParametersForMongoDB(filter)
    const [nodes, totalCount] = await Promise.all([
      this.programService.find({ ...query, userId: _id }, options),
      this.programService.count({ ...filter?.where, userId: _id }),
    ])
    return { nodes, totalCount }
  }

  @ResolveField(() => PaginatedActivations)
  @CheckPolicies(Action.Read, Activation, { parentIdField: '_id', childReferenceField: 'userId' })
  async activations(
    @Parent() { _id }: Organization,
    @Args('filter', { nullable: true }) filter?: FilterArgs<Activation>,
  ) {
    const { query, options } = getPagingParametersForMongoDB(filter)
    const [nodes, totalCount] = await Promise.all([
      this.activationService.find({ ...query, userId: _id }, options),
      this.activationService.count({ ...filter?.where, userId: _id }),
    ])
    return { nodes, totalCount }
  }

  @ResolveField(() => PaginatedTags)
  @CheckPolicies(Action.Read, Tag, { parentIdField: '_id', childReferenceField: 'userId' })
  async tags(@Parent() { _id }: Organization, @Args('filter', { nullable: true }) filter?: FilterArgs<Tag>) {
    const { query, options } = getPagingParametersForMongoDB(filter)
    const [nodes, totalCount] = await Promise.all([
      this.tagService.find({ ...query, userId: _id }, options),
      this.tagService.count({ ...filter?.where, userId: _id }),
    ])
    return { nodes, totalCount }
  }

  @ResolveField(() => [String], { nullable: true })
  async customDomains(@Parent() { _id }: Organization) {
    return this.oidcClientService
      .find({ organizationId: _id, customDomains: { $exists: true } })
      .then((clients) => [...new Set(clients.flatMap((c) => c.customDomains || []))])
  }

  @Query(() => User, { nullable: true })
  @CheckPolicies(Action.Read, Organization, { id: 'organizationId' })
  async person(@Args('organizationId') organizationId: string, @Args('filter') filter: FilterOneArgs<User>) {
    if (!filter.where && !filter?.id) {
      throw new BadRequestException(`Missing either "where" or "id" argument for this query!`)
    }

    let where: Filter<User>
    if (filter.id) {
      where = { _id: new ObjectId(filter.id) }
    } else {
      where = parseFilter(filter.where)
    }

    const result = await this.userService.aggregate([
      { $match: where },
      { $limit: 250 },
      {
        $lookup: {
          from: 'tag',
          localField: '_id',
          foreignField: 'userId',
          as: 'tag',
          pipeline: [
            {
              $match: {
                organizationId: new ObjectId(organizationId),
              },
            },
          ],
        },
      },
      { $match: { 'tag.0': { $exists: true } } },
      { $limit: 1 },
    ])

    return result?.[0] || null
  }
}
