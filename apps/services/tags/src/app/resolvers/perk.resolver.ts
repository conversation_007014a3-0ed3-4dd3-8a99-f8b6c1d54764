import { ForbiddenException, UnauthorizedException } from '@nestjs/common'
import { Args, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql'
import { ObjectId } from 'mongodb'

import { CurrentAuth, CheckPolicies, Action, IntrospectionResponse } from '@phigital-loyalty/core'
import { PaginationArgs } from '@phigital-loyalty/database'
import {
  Activation,
  ActivationService,
  Organization,
  OrganizationService,
  ActivationSearch,
  Perk,
  PaginatedPerks,
} from '@phigital-loyalty/tags'

@Resolver(() => Perk)
export class PerkResolvers {
  constructor(
    readonly entityService: ActivationService,
    readonly organizationService: OrganizationService,
  ) {}

  @Query(() => Perk)
  async perk(@CurrentAuth() auth: IntrospectionResponse, @Args('id') id: string) {
    if (!auth) {
      throw new UnauthorizedException(`You must be authenticated to perform this query!`)
    }

    const activation = await this.entityService.findById(id)

    try {
      await this.entityService.checkIfUserIsAllowedToParticipate(new ObjectId(auth.sub), activation)
    } catch {
      throw new ForbiddenException()
    }

    return new Perk({
      ...activation,
      isParticipant: activation.participantIds?.some((value) => value.toString() === auth.sub),
      isFull: activation.limit && activation.participantIds?.length >= activation.limit,
    } as Perk)
  }

  @Query(() => PaginatedPerks)
  async perks(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args('search', { nullable: true }) search?: ActivationSearch,
    @Args('options', { nullable: true }) options?: PaginationArgs<Activation>,
  ) {
    return this.entityService.perks(auth, search, options)
  }

  @ResolveField(() => Organization, { nullable: true })
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organization(@Parent() { organizationId }: Activation): Promise<Organization | Error> {
    if (!organizationId) return

    return this.organizationService.load(organizationId)
  }

  @Mutation(() => Perk)
  async participateInPerk(@CurrentAuth() auth: IntrospectionResponse, @Args('id') id: string) {
    const result = await this.entityService.participate(auth, id)

    return new Perk({
      ...result,
      isParticipant: result.participantIds?.some((value) => value.toString() === auth.sub),
      isFull: result.limit <= result.participantIds?.length,
    } as Perk)
  }

  @Mutation(() => Perk)
  async withdrawFromPerk(@CurrentAuth() auth: IntrospectionResponse, @Args('id') id: string) {
    const result = await this.entityService.withdraw(auth, id)

    return new Perk({
      ...result,
      isParticipant: result.participantIds?.some((value) => value.toString() === auth.sub),
      isFull: result.limit <= result.participantIds?.length,
    } as Perk)
  }
}
