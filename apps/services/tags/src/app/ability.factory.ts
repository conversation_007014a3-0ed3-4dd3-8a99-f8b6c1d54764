import { Injectable } from '@nestjs/common'
import { ObjectId } from 'mongodb'

import { BaseAbilityFactory, Can, Cannot, Action, IntrospectionResponse } from '@phigital-loyalty/core'
import { Activation, Organization, User, Product, Program, Tag } from '@phigital-loyalty/tags'

@Injectable()
export class AbilityFactory extends BaseAbilityFactory {
  async rules(auth: IntrospectionResponse, can: Can, _cannot: Cannot) {
    can(Action.Read, Tag)
    can(Action.Read, Product)
    can(Action.Read, Program)
    can(Action.Read, User)
    can(Action.Read, Organization)

    if (!auth) {
      return
    }

    const rolesMap = this.buildRolesMap(auth, ObjectId)

    can(Action.Read, Activation, {
      organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) },
    })
    can(Action.Create, Activation, {
      organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) },
    })
    can(Action.Update, Activation, {
      organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) },
    })
    can(Action.Delete, Activation, {
      organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) },
    })
    can(Action.Stats, Activation, {
      organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) },
    })
    can('Notify', Activation, {
      organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) },
    })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, Activation)
      can(Action.Create, Activation)
      can(Action.Manage, Activation)
      can(Action.Update, Activation)
      can(Action.Delete, Activation)
      can(Action.Stats, Activation)
      can('Notify', Activation)
    }

    can(Action.Read, Product)
    can(Action.Create, Product, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Update, Product, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Delete, Product, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Stats, Product, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Create, Product)
      can(Action.Manage, Product)
      can(Action.Update, Product)
      can(Action.Delete, Product)
      can(Action.Stats, Product)
    }

    can(Action.Read, Program)
    can(Action.Create, Program, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Update, Program, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Delete, Program, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Stats, Program, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Create, Program)
      can(Action.Manage, Program)
      can(Action.Update, Program)
      can(Action.Delete, Program)
      can(Action.Stats, Program)
    }

    can('Claim', Tag)

    can(Action.Update, Tag, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Manage, Tag, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Delete, Tag, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can(Action.Stats, Tag, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    can('Notify', Tag, { organizationId: { $in: this.orgIdsForRoles(['Admin', 'Owner'], rolesMap) } })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Create, Tag)
      can(Action.Manage, Tag)
      can(Action.Update, Tag)
      can(Action.Delete, Tag)
      can(Action.Stats, Tag)
      can('Notify', Tag)
    }
  }
}
