import { resolve } from 'path'

module.exports = {
  mongodb: {
    url: process.env.MONGODB_URI,
    databaseName: process.env.MONGODB_DATABASE,

    options: {
      //   connectTimeoutMS: 3600000, // increase connection timeout to 1 hour
      //   socketTimeoutMS: 3600000, // increase socket timeout to 1 hour
    },
  },

  migrationsDir: resolve(__dirname, 'migrations'),
  changelogCollectionName: 'migrations',
  migrationFileExtension: '.ts',
}
