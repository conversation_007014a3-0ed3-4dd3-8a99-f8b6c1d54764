import assert from 'node:assert/strict'

import { jest } from '@jest/globals'
import { GraphQLModule } from '@nestjs/graphql'
import { Test } from '@nestjs/testing'
import { gql } from 'graphql-tag'
import { ObjectId } from 'mongodb'

import { MongoDBTestClient } from '@phigital-loyalty/database/lib/mongodb/testing'
import { generateRandomEMChip, ChipType } from '@phigital-loyalty/tags'

import { TagsAppModule } from '../src/app/tags.module'

import type { ApolloServer } from '@apollo/server'
import type { ApolloDriver } from '@nestjs/apollo'
import type { INestApplication } from '@nestjs/common'
import type { TestingModule } from '@nestjs/testing'

jest.setTimeout(10000)

const Tag = gql`
  query Tag($filter: FilterOneArgs!) {
    tag(filter: $filter) {
      _id
      createdAt
      deletedAt
      updatedAt
      organizationId
      chip {
        type
      }
      userId
      programId
      productId
      organization {
        _id
        name
        logo
      }
      user {
        _id
        name
        picture
      }
      program {
        _id
        name
      }
      product {
        _id
        name
        picture
        description
        url
      }
      productDetails {
        sku
        batch
        color
        size
        picture
        stamp
      }
    }
  }
`

const populateDocuments = {
  user: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b41'),
      name: 'Regular User (Owner)',
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b42'),
      name: 'Regular User (Not Owner)',
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b43'),
      name: 'Org User',
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b44'),
      name: 'Org Admin',
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b45'),
      name: 'System Support',
    },
  ],
  organization: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b31'),
      name: 'Org',
    },
  ],
  role: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b21'),
      userId: new ObjectId('61b40e500aa38d35f8095b43'),
      organizationId: new ObjectId('61b40e500aa38d35f8095b31'),
      role: 'User',
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b22'),
      userId: new ObjectId('61b40e500aa38d35f8095b44'),
      organizationId: new ObjectId('61b40e500aa38d35f8095b31'),
      role: 'Admin',
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b23'),
      userId: new ObjectId('61b40e500aa38d35f8095b45'),
      role: 'Support',
    },
  ],
  program: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b51'),
      organizationId: new ObjectId('61b40e500aa38d35f8095b31'),
      name: 'Test Program',
    },
  ],
  product: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b61'),
      organizationId: new ObjectId('61b40e500aa38d35f8095b31'),
      name: 'Shirt',
      description: 'A branded shirt',
      picture: null,
      url: null,
    },
  ],
  tag: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b71'),
      organizationId: new ObjectId('61b40e500aa38d35f8095b31'),
      chip: generateRandomEMChip(ChipType.EM4425V12),
      productDetails: {
        batch: null,
        date: null,
        picture: null,
        stamp: null,
      },
      productId: new ObjectId('61b40e500aa38d35f8095b61'),
      programId: new ObjectId('61b40e500aa38d35f8095b51'),
    },
    {
      _id: new ObjectId('61b40e500aa38d35f8095b72'),
      organizationId: new ObjectId('61b40e500aa38d35f8095b31'),
      chip: generateRandomEMChip(ChipType.EM4425V12),
      productDetails: {
        batch: null,
        date: null,
        picture: null,
        stamp: null,
      },
      userId: new ObjectId('61b40e500aa38d35f8095b41'),
      productId: new ObjectId('61b40e500aa38d35f8095b61'),
      programId: new ObjectId('61b40e500aa38d35f8095b51'),
    },
  ],
  activation: [],
}

describe('tags', () => {
  const mongodbClient = new MongoDBTestClient('Tags')

  let app: INestApplication
  let moduleRef: TestingModule
  let apolloClient: ApolloServer

  process.env.CLS_NO_MOUNT = 'true'
  process.env.DISABLE_ENTITY_EVENTS = 'true'
  process.env.DISABLE_WEBHOOKS = 'true'

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [TagsAppModule],
    }).compile()

    app = moduleRef.createNestApplication({ forceCloseConnections: true })
    app.enableShutdownHooks()
    await app.init()

    const graphqlModule = app.get<GraphQLModule<ApolloDriver>>(GraphQLModule)
    apolloClient = graphqlModule.graphQlAdapter?.instance

    await mongodbClient.populate(populateDocuments)
  })

  afterAll(async () => {
    app.close()
    await mongodbClient.close()
  })

  it(`Anonymous should be able to see all tags.`, async () => {
    const open = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b71' } } },
      {
        contextValue: {},
      },
    )

    assert(open.body.kind === 'single')
    expect(open.body.singleResult.errors).toBeUndefined()
    expect(open.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b71',
        },
      },
    })

    const owned = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b72' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b42',
            active: true,
            roles: [],
          },
        },
      },
    )

    assert(owned.body.kind === 'single')
    expect(owned.body.singleResult.errors).toBeUndefined()
    expect(owned.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b72',
          userId: '61b40e500aa38d35f8095b41',
          user: null,
        },
      },
    })
  })

  it(`Authenticated User should be able to see all tags.`, async () => {
    const open = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b71' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b42',
            active: true,
            roles: [],
          },
        },
      },
    )

    assert(open.body.kind === 'single')
    expect(open.body.singleResult.errors).toBeUndefined()
    expect(open.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b71',
        },
      },
    })

    const owned = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b72' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b42',
            active: true,
            roles: [],
          },
        },
      },
    )

    assert(owned.body.kind === 'single')
    expect(owned.body.singleResult.errors).toBeUndefined()
    expect(owned.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b72',
          userId: '61b40e500aa38d35f8095b41',
          user: null,
        },
      },
    })
  })

  it(`Tag Owner should be able to see it's Tag's full info.`, async () => {
    const owned = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b72' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b41',
            active: true,
            roles: [],
          },
        },
      },
    )

    assert(owned.body.kind === 'single')
    expect(owned.body.singleResult.errors).toBeUndefined()
    expect(owned.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b72',
          userId: '61b40e500aa38d35f8095b41',
          user: {
            _id: '61b40e500aa38d35f8095b41',
            name: 'Regular User (Owner)',
            picture: null,
          },
        },
      },
    })
  })

  it(`Org User should not be able to see the User info for a registered Tag.`, async () => {
    const open = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b71' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b43',
            active: true,
            roles: [
              {
                organizationId: '61b40e500aa38d35f8095b31',
                role: 'User',
              },
            ],
          },
        },
      },
    )

    assert(open.body.kind === 'single')
    expect(open.body.singleResult.errors).toBeUndefined()
    expect(open.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b71',
        },
      },
    })

    const owned = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b72' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b43',
            active: true,
            roles: [
              {
                organizationId: '61b40e500aa38d35f8095b31',
                role: 'User',
              },
            ],
          },
        },
      },
    )

    assert(owned.body.kind === 'single')
    expect(owned.body.singleResult.errors).toBeUndefined()
    expect(owned.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b72',
          userId: '61b40e500aa38d35f8095b41',
          user: null,
        },
      },
    })
  })

  it(`Org Admin should be able to see it's Tags' full info.`, async () => {
    const open = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b71' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b44',
            active: true,
            roles: [
              {
                organizationId: '61b40e500aa38d35f8095b31',
                role: 'Admin',
              },
            ],
          },
        },
      },
    )

    assert(open.body.kind === 'single')
    expect(open.body.singleResult.errors).toBeUndefined()
    expect(open.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b71',
        },
      },
    })

    const owned = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b72' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b44',
            active: true,
            roles: [
              {
                organizationId: '61b40e500aa38d35f8095b31',
                role: 'Admin',
              },
            ],
          },
        },
      },
    )

    assert(owned.body.kind === 'single')
    expect(owned.body.singleResult.errors).toBeUndefined()
    expect(owned.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b72',
          userId: '61b40e500aa38d35f8095b41',
          user: {
            _id: '61b40e500aa38d35f8095b41',
            name: 'Regular User (Owner)',
            picture: null,
          },
        },
      },
    })
  })

  it(`System Support should be able to see the full Tag info.`, async () => {
    const open = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b71' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b45',
            active: true,
            roles: [
              {
                role: 'Support',
              },
            ],
          },
        },
      },
    )

    assert(open.body.kind === 'single')
    expect(open.body.singleResult.errors).toBeUndefined()
    expect(open.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b71',
        },
      },
    })

    const owned = await apolloClient.executeOperation(
      { query: Tag, variables: { filter: { id: '61b40e500aa38d35f8095b72' } } },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b45',
            active: true,
            roles: [
              {
                role: 'Support',
              },
            ],
          },
        },
      },
    )

    assert(owned.body.kind === 'single')
    expect(owned.body.singleResult.errors).toBeUndefined()
    expect(owned.body.singleResult).toMatchObject({
      data: {
        tag: {
          _id: '61b40e500aa38d35f8095b72',
          userId: '61b40e500aa38d35f8095b41',
          user: {
            _id: '61b40e500aa38d35f8095b41',
            name: 'Regular User (Owner)',
            picture: null,
          },
        },
      },
    })
  })
})
