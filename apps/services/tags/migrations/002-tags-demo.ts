import { ChipType, ActivityType, chipInterfaceResolver, generateRandomEMChip } from '@phigital-loyalty/tags'

import type { Program, Product, Tag, Activation } from '@phigital-loyalty/tags'
import type { Organization } from '@phigital-loyalty/user'
import type { Db, ObjectId } from 'mongodb'

module.exports = {
  up: async (db: Db) => {
    const orgs = await db.collection<Omit<Organization, '_id'>>('organization').insertMany(
      organizations.map((item) => ({
        ...item,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    )
    const progs = await db.collection<Omit<Program, '_id'>>('program').insertMany(
      programs(orgs.insertedIds).map((item) => ({
        ...item,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    )
    const prods = await db.collection<Omit<Product, '_id'>>('product').insertMany(
      products(orgs.insertedIds).map((item) => ({
        ...item,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    )

    await db.collection<Omit<Tag, '_id'>>('tag').insertMany(
      tags(orgs.insertedIds, progs.insertedIds, prods.insertedIds).map((item) => ({
        ...item,
        chip: chipInterfaceResolver(item.chip),
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    )
    await db.collection<Omit<Activation, '_id'>>('activation').insertMany(
      activations(orgs.insertedIds, progs.insertedIds, prods.insertedIds).map((item) => ({
        ...item,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    )
    return
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  down: async (db: Db) => {
    return
  },
}

const organizations = [
  {
    name: 'DapTap Wear',
    tag: 'daptapwear',
    billing: '<EMAIL>',
    contactEmail: '<EMAIL>',
    website: 'https://daptapgo.io/daptap-for-retail/',
    logo: '/icons/<EMAIL>',
    billingVerified: false,
    customerId: 'cus_OUbu7qOEiyRcII',
    template: {
      backgroundColor: 'white',
    },
  },
  {
    name: 'DapTap Sports',
    tag: 'daptapport',
    billing: '<EMAIL>',
    contactEmail: '<EMAIL>',
    website: 'https://daptapgo.io/daptap-for-retail/',
    logo: '/icons/<EMAIL>',
    billingVerified: false,
    customerId: 'cus_OUbvhuOMLuaDyd',
  },
]

const programs = (orgs: { [key: number]: ObjectId }) => [
  {
    organizationId: orgs[1],
    name: 'Test Program',
  },
  {
    organizationId: orgs[1],
    name: 'Second Program',
  },
  {
    organizationId: orgs[0],
    name: 'Summer 23',
  },
  {
    organizationId: orgs[0],
    name: 'Demo Program',
  },
  {
    organizationId: orgs[0],
    name: 'Autumn 23',
  },
  {
    organizationId: orgs[0],
    name: 'New Program',
  },
]

const products = (orgs: { [key: number]: ObjectId }) => [
  {
    organizationId: orgs[1],
    name: 'Shirt',
    description: 'A branded shirt',
    picture: null,
    url: null,
  },
  {
    organizationId: orgs[1],
    name: 'Pants',
    picture: null,
    description: null,
    url: null,
  },
  {
    organizationId: orgs[0],
    name: 'Adidas Training T-shirt 2023 2024',
    picture:
      'https://media.slbenfica.pt/-/media/benficadp/shop/produtos-2020-2021/equipamentos/23-24/linha-treino/fotos-adidas/tshirt_treino_adidas_2023_2024.png?v=638253728530000000',
    description:
      'Red training t-shirt used by the Benfica team in the 2023 2024 season.\n\nWith a design that encourages freedom of movement and absorbent AEROREADY technology, that keeps the skin dry.\n\nThis t-shirt is made with recycled polyester, to save resources and reduce emissions and it also includes mesh inserts on the side that increase the airflow.',
    url: 'https://www.slbenfica.pt/en-us/loja/equipamentos/entrenamiento/tshirt-treino-adidas-2023-2024-',
  },
]

const tags = (
  orgs: { [key: number]: ObjectId },
  progs: { [key: number]: ObjectId },
  prods: { [key: number]: ObjectId },
) => [
  {
    organizationId: orgs[1],
    chip: generateRandomEMChip(ChipType.EM4425V16),
    productDetails: {
      serialNumber: null,
      batch: null,
      date: null,
      picture: null,
      stamp: null,
    },
    productId: prods[0],
    programId: progs[0],
    labels: ['green'],
  },
  {
    organizationId: orgs[1],
    chip: generateRandomEMChip(ChipType.EM4425V12),
    labels: ['green'],
    programId: progs[0],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V16),
    organizationId: orgs[0],
    labels: ['green'],
    programId: progs[3],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V16),
    organizationId: orgs[0],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V12),
    organizationId: orgs[0],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V13),
    organizationId: orgs[0],
    productDetails: {
      serialNumber: 'UkJjTy9v',
      batch: '2/2023',
      date: null,
      picture: null,
      stamp: 'https://media.slbenfica.pt/-/media/images/slb-logo-new.svg?v=638097287260000000',
    },
    productId: prods[2],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V13),
    organizationId: orgs[0],
    productDetails: {
      serialNumber: 'EvbcuFuj',
      batch: '2/2023',
      date: null,
      picture: null,
      stamp: 'https://media.slbenfica.pt/-/media/images/slb-logo-new.svg?v=638097287260000000',
    },
    productId: prods[2],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V12),
    organizationId: orgs[0],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V12),
    organizationId: orgs[0],
    productDetails: {
      serialNumber: 'WyidZh6J',
      batch: '2/2023',
      date: null,
      picture: null,
      stamp: 'https://media.slbenfica.pt/-/media/images/slb-logo-new.svg?v=638097287260000000',
    },
    productId: prods[2],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V12),
    organizationId: orgs[0],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V13),
    organizationId: orgs[0],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V13),
    organizationId: orgs[0],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V12),
    labels: null,
    organizationId: orgs[0],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V16),
    labels: null,
    organizationId: orgs[0],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V16),
    labels: null,
    organizationId: orgs[0],
    programId: progs[2],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V16),
    labels: ['parrish'],
    organizationId: orgs[0],
    programId: progs[4],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V13),
    labels: ['parrish'],
    organizationId: orgs[0],
    programId: progs[4],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V13),
    labels: null,
    organizationId: orgs[0],
    programId: progs[5],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V12),
    labels: null,
    organizationId: orgs[0],
    programId: progs[5],
  },
  {
    chip: generateRandomEMChip(ChipType.EM4425V16),
    labels: null,
    organizationId: orgs[0],
    programId: progs[5],
  },
]

const activations = (
  orgs: { [key: number]: ObjectId },
  progs: { [key: number]: ObjectId },
  prods: { [key: number]: ObjectId },
) => [
  {
    organizationId: orgs[1],
    type: ActivityType.IN_PERSON,
    name: 'Shirt Activation',
    url: 'https://material.angular.io/components/form-field/api#SubscriptSizing',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      registeredMinDate: null,
      registeredMaxDate: null,
      productIds: [prods[0]],
      programIds: [],
      labels: [],
    },
    limit: 10,
    participantIds: [],
  },
  {
    organizationId: orgs[1],
    type: ActivityType.IN_PERSON,
    name: 'Green Tag Activation',
    url: 'https://material.angular.io/components/form-field/api#SubscriptSizing',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      labels: ['green'],
    },
    limit: 10,
    participantIds: [],
  },
  {
    organizationId: orgs[1],
    type: ActivityType.IN_PERSON,
    name: 'Expired activation',
    url: 'https://svelte.dev/docs/svelte-store#get',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      productIds: [],
      programIds: [],
      registeredMinDate: null,
      registeredMaxDate: null,
      labels: [],
    },
    limit: 10,
    participantIds: [],
  },
  {
    organizationId: orgs[0],
    type: ActivityType.IN_PERSON,
    name: 'Beach Sunset Meet & Greet',
    url: 'https://www.slbenfica.pt/en-us/fas/benficatoken',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      productIds: [prods[2]],
      programIds: [],
      registeredMinDate: null,
      registeredMaxDate: null,
      labels: [],
    },
    limit: 30,
    picture:
      'https://media.istockphoto.com/photos/summer-color-cocktail-on-the-beach-sunset-on-the-sea-picture-id815074464?k=6&m=815074464&s=612x612&w=0&h=K_yijpi3iE7phP49P88GyjSoR353MPT7oAYvAhYPFXE=',
    date: new Date('2024-08-01'),
    description: 'Celebrate the start of the new season with your favourite players enjoying the sunset at the beach.',
    participantIds: [],
  },
  {
    organizationId: orgs[0],
    type: ActivityType.IN_PERSON,
    name: 'Meet & Greet at the Stadium',
    url: 'https://www.slbenfica.pt/en-us/fas/benficatoken',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      productIds: [prods[2]],
      programIds: [],
      registeredMinDate: null,
      registeredMaxDate: null,
      labels: [],
    },
    limit: 25,
    picture: 'https://media.slbenfica.pt/-/media/BenficaDP/Benficatoken/info2',
    date: new Date('2024-08-01'),
    location: 'Lisbon, PT',
    description:
      'Meet your favourite players at the stadium and spend the afternoon playing games with players and fans.',
    participantIds: [],
  },
  {
    organizationId: orgs[0],
    type: ActivityType.VIRTUAL,
    name: 'Pre-Season Meet & Greet at the Stadium',
    url: 'https://www.slbenfica.pt/en-us/fas/benficatoken',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      productIds: [prods[2]],
      programIds: [],
      registeredMinDate: null,
      registeredMaxDate: null,
      labels: [],
    },
    limit: 25,
    picture: 'https://media.slbenfica.pt/-/media/BenficaDP/Benficatoken/info2',
    date: new Date('2024-08-01'),
    location: 'Estádio da Luz',
    description:
      'Meet your favourite players at the stadium and spend the afternoon playing games with players and fans.',
    participantIds: [],
  },
  {
    organizationId: orgs[0],
    type: ActivityType.VIRTUAL,
    name: 'Autumn Meet & Greet with the Players',
    url: 'https://www.slbenfica.pt/en-us/fas/benficatoken',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2024-08-30T23:00:00.000Z'),
    filter: {
      productIds: [],
      programIds: [],
      registeredMinDate: null,
      registeredMaxDate: null,
      labels: [],
    },
    limit: 50,
    picture: 'https://media.slbenfica.pt/-/media/BenficaDP/Benficatoken/info2',
    location: 'Lisbon, PT',
    description:
      'BENFICA Fan Tokens can get you closer to enjoying once-in-a-lifetime experiences.\n\nThe more you interact with the club using BENFICA Fan Tokens on Socios.com, the greater your chances are of winning amazing rewards. From VIP experiences to meet and greet with your heroes, to signed merchandise - our goal is to make your dreams come true.',
    participantIds: [],
  },
  {
    organizationId: orgs[0],
    type: ActivityType.IN_PERSON,
    name: 'Mid-Season Meet & Greet at the Stadium',
    url: 'https://www.slbenfica.pt/en-us/fas/benficatoken',
    runFrom: new Date('2024-02-31T23:00:00.000Z'),
    runTo: new Date('2023-10-31T00:00:00.000Z'),
    filter: {
      labels: [],
      productIds: [],
      programIds: [progs[4]],
      registeredMinDate: null,
      registeredMaxDate: null,
    },
    limit: 100,
    picture: 'https://media.slbenfica.pt/-/media/BenficaDP/Benficatoken/info2',
    date: new Date('2024-08-01'),
    location: 'Estádio da Luz',
    description:
      'Meet your favourite players at the stadium and spend the afternoon playing games with players and fans.',
    participantIds: [],
  },
]
