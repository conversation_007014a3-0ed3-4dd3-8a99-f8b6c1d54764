import type { Program, Product, Tag, Activation } from '@phigital-loyalty/tags'
import type { Db } from 'mongodb'

module.exports = {
  up: async (db: Db) => {
    // Program
    await db.collection<Program>('program').createIndex(
      {
        organizationId: 1,
      },
      {
        name: 'organizationId',
      },
    )
    await db.collection<Program>('program').createIndex(
      {
        organizationId: 1,
        name: 1,
      },
      {
        name: 'unique_organizationId_name',
        unique: true,
      },
    )

    // Product
    await db.collection<Product>('product').createIndex(
      {
        organizationId: 1,
      },
      {
        name: 'organizationId',
      },
    )
    await db.collection<Product>('product').createIndex(
      {
        organizationId: 1,
        name: 1,
      },
      {
        name: 'unique_organizationId_name',
        unique: true,
      },
    )

    // Tag
    await db.collection<Tag>('tag').createIndex(
      {
        organizationId: 1,
      },
      {
        name: 'organizationId',
      },
    )
    await db.collection<Tag>('tag').createIndex(
      {
        'chip.type': 1,
      },
      {
        name: 'chipType',
      },
    )
    await db.collection<Tag>('tag').createIndex(
      {
        'chip.id': 1,
      },
      {
        name: 'chipId',
        unique: true,
        partialFilterExpression: { 'chip.id': { $exists: true } },
      },
    )
    await db.collection<Tag>('tag').createIndex(
      {
        'chip.serialNumber': 1,
      },
      {
        name: 'chipSerialNumber',
        unique: true,
        partialFilterExpression: { 'chip.serialNumber': { $exists: true } },
      },
    )
    await db.collection<Tag>('tag').createIndex(
      {
        'chip.type': 1,
      },
      {
        name: 'chipType',
      },
    )
    await db.collection<Tag>('tag').createIndex(
      {
        productId: 1,
      },
      {
        name: 'productId',
      },
    )
    await db.collection<Tag>('tag').createIndex(
      {
        programId: 1,
      },
      {
        name: 'programId',
      },
    )

    // Activation
    await db.collection<Activation>('activation').createIndex(
      {
        organizationId: 1,
      },
      {
        name: 'organizationId',
      },
    )
    await db.collection<Activation>('activation').createIndex(
      {
        organizationId: 1,
        name: 1,
      },
      {
        name: 'unique_organizationId_name',
        unique: true,
      },
    )
    await db.collection<Activation>('activation').createIndex(
      {
        participantIds: 1,
      },
      {
        name: 'participantIds',
      },
    )
    return
  },
  down: async (db: Db) => {
    await db.collection<Program>('program').dropIndex('organizationId')
    await db.collection<Program>('program').dropIndex('unique_organizationId_name')

    await db.collection<Product>('product').dropIndex('organizationId')
    await db.collection<Product>('product').dropIndex('unique_organizationId_name')

    await db.collection<Tag>('tag').dropIndex('organizationId')
    await db.collection<Tag>('tag').dropIndex('chipType')
    await db.collection<Tag>('tag').dropIndex('chipId')
    await db.collection<Tag>('tag').dropIndex('chipSerialNumber')
    await db.collection<Tag>('tag').dropIndex('productId')
    await db.collection<Tag>('tag').dropIndex('programId')

    await db.collection<Activation>('activation').dropIndex('organizationId')
    await db.collection<Activation>('activation').dropIndex('unique_organizationId_name')
    await db.collection<Activation>('activation').dropIndex('participantIds')
    return
  },
}
