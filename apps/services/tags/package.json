{"name": "@phigital-loyalty/services-tags", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/services/tags"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@phigital-loyalty/tags": "0.0.0", "mongodb": "^6.9.0", "@nestjs/common": "^11.0.7", "@nestjs/graphql": "13.1.0", "@phigital-loyalty/database": "0.0.0", "graphql": "16.11.0", "graphql-fields": "^2.0.3", "@nestjs/config": "^4.0.0", "@phigital-loyalty/core": "0.0.0", "@nx/webpack": "20.0.6", "@nestjs/core": "11.1.6", "openid-client": "^6.1.7"}}