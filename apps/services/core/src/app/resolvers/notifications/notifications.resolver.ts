import { UseGuards } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Args, Mutation, Resolver } from '@nestjs/graphql'

import {
  Action,
  CheckPolicies,
  CreateNotificationInput,
  CurrentUserId,
  IsAuthenticated,
  NotificationCMarkAllReadResult,
  Notification,
  NotificationType,
} from '@phigital-loyalty/core'
import { BaseResolver, NotificationService } from '@phigital-loyalty/database'

@Resolver(() => Notification)
export class NotificationResolvers extends BaseResolver(Notification, ['entity', 'entities', 'update']) {
  constructor(
    readonly entityService: NotificationService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }

  @Mutation(() => Notification, { name: 'createNotification' })
  @CheckPolicies(Action.Create, Notification)
  async create(
    @Args({ name: 'notification', type: () => CreateNotificationInput }) notification: CreateNotificationInput,
  ): Promise<Notification> {
    return this.entityService.create(notification)
  }

  @Mutation(() => [Notification], { name: 'createNotifications' })
  @CheckPolicies(Action.Create, Notification)
  async createMany(
    @Args({ name: 'notification', type: () => [CreateNotificationInput] }) notifications: CreateNotificationInput[],
  ): Promise<Notification[]> {
    return Promise.all(notifications.map((notification) => this.entityService.create(notification)))
  }

  @Mutation(() => [NotificationCMarkAllReadResult], { name: 'markAllReadNotifications' })
  @UseGuards(IsAuthenticated)
  async markAllRead(@CurrentUserId() currentUserId: string): Promise<Pick<Notification, '_id'>[]> {
    const result = await this.entityService.find(
      {
        userId: currentUserId,
        type: { $nin: [NotificationType.INVITATION] },
        viewed: { $nin: [true] },
      },
      { projection: { _id: true } },
    )

    await this.entityService.updateMany(
      {
        userId: currentUserId,
        type: { $nin: [NotificationType.INVITATION] },
        viewed: { $nin: [true] },
      },
      { $set: { viewed: true } },
    )

    return result
  }
}
