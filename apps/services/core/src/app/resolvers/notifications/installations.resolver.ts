import { ModuleRef } from '@nestjs/core'
import { Resolver } from '@nestjs/graphql'

import { Installation } from '@phigital-loyalty/core'
import { BaseResolver, InstallationService } from '@phigital-loyalty/database'

@Resolver(() => Installation)
export class InstallationResolvers extends BaseResolver(Installation, ['entity', 'entities', 'create', 'delete']) {
  constructor(
    readonly entityService: InstallationService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }
}
