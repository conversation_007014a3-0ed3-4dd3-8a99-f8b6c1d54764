import { BadRequestException, UseGuards } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Args, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql'

import {
  User,
  Organization,
  OrganizationService,
  UserService,
  RoleService,
  OrganizationWithRole,
  Role,
  FederatedUser,
  FederatedUserService,
} from '@phigital-loyalty/accounts'
import {
  Action,
  CheckPolicies,
  CurrentAuth,
  CurrentUserId,
  Installation,
  type IntrospectionResponse,
  IsAuthenticated,
} from '@phigital-loyalty/core'
import {
  BaseResolver,
  FilterArgs,
  getPagingParametersForMongoDB,
  InstallationService,
} from '@phigital-loyalty/database'

@Resolver(() => User)
export class UserResolver extends BaseResolver(User, ['entity', 'entities', 'delete']) {
  constructor(
    readonly entityService: UserService,
    readonly moduleRef: ModuleRef,
    readonly organizationService: OrganizationService,
    readonly roleService: RoleService,
    readonly federatedUserService: FederatedUserService,
    readonly installationService: InstallationService,
  ) {
    super(entityService, moduleRef)
  }

  @Query(() => User)
  @UseGuards(IsAuthenticated)
  async me(@CurrentUserId() userId: string): Promise<User> {
    return this.entityService.findById(userId)
  }

  @Mutation(() => User, { name: `createUser` })
  @CheckPolicies(Action.Create, User, { object: 'partialEntity' })
  async create(
    @Args({ name: 'partialEntity', type: () => User.options.createInput() })
    partialEntity: Partial<User>,
  ) {
    return this.entityService.createOne(partialEntity)
  }

  @Mutation(() => [User], { name: `createUsers` })
  @CheckPolicies(Action.Manage, User)
  async createMany(
    @Args({ name: 'partialEntities', type: () => [User.options.createInput()] })
    partialEntities: Partial<User>[],
  ) {
    return this.entityService.createMany(partialEntities)
  }

  @Mutation(() => User, { name: `updateUser` })
  @CheckPolicies(Action.Update, User, { object: 'partialEntity' })
  async update(
    @Args({ name: 'partialEntity', type: () => User.options.updateInput() })
    partialEntity: Partial<User>,
  ) {
    if (!partialEntity._id) {
      throw new BadRequestException(`Missing User ID field when trying to update entity!`)
    }

    const filter = { _id: { ...partialEntity }._id }
    delete partialEntity._id
    return this.entityService.updateOne(filter, partialEntity)
  }

  @ResolveField(() => String)
  async name(@Parent() user: User) {
    return this.entityService.name(user)
  }

  @ResolveField(() => String)
  async contactEmail(@Parent() user: User) {
    return this.entityService.email(user)
  }

  @ResolveField(() => [OrganizationWithRole])
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organizations(
    @Parent() { _id }: User,
    @Args('filter', { nullable: true }) filter?: FilterArgs<Organization>,
  ): Promise<(OrganizationWithRole | Error)[]> {
    const roles = await this.roleService.find({ userId: _id, organizationId: { $exists: true } })
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), _id: { $in: roles.map((item) => item.organizationId) } },
    })
    const organizations = await this.organizationService.find(query, options)

    return organizations.map((org: OrganizationWithRole) => {
      org.role = roles.find((item) => item.organizationId.toString() === org._id.toString()).role

      return org
    })
  }

  @ResolveField(() => [Role])
  @CheckPolicies(Action.Read, Role, { parentIdField: '_id', childReferenceField: 'userId' })
  async roles(@Parent() { _id }: User, @Args('filter', { nullable: true }) filter?: FilterArgs<Role>) {
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), userId: _id },
    })
    return this.roleService.find(query, options)
  }

  @ResolveField(() => [FederatedUser])
  @CheckPolicies(Action.Read, FederatedUser, { parentIdField: '_id', childReferenceField: 'sub' })
  async identities(@Parent() { _id }: User, @Args('filter', { nullable: true }) filter?: FilterArgs<FederatedUser>) {
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), sub: _id },
    })
    return this.federatedUserService.find(query, options)
  }

  @ResolveField(() => [Installation])
  @CheckPolicies(Action.Read, Installation, { parentIdField: '_id', childReferenceField: 'userId' })
  async installations(@Parent() { _id }: User, @Args('filter', { nullable: true }) filter?: FilterArgs<Installation>) {
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), userId: _id.toString() },
    })
    return this.installationService.find(query, options)
  }

  @Mutation(() => User)
  @UseGuards(IsAuthenticated)
  async meChangePassword(
    @CurrentUserId() userId: string,
    @Args('currentPassword') currentPassword: string,
    @Args('newPassword') newPassword: string,
  ) {
    await this.entityService.changePassword(userId, currentPassword, newPassword)
    return this.entityService.findById(userId)
  }

  @Mutation(() => User)
  @UseGuards(IsAuthenticated)
  async meSendVerification(@CurrentAuth() auth: IntrospectionResponse, @Args('type') type: string) {
    await this.entityService.sendVerificationCodes(auth.sub, auth.client_id, type)
    return this.entityService.findById(auth.sub)
  }
}
