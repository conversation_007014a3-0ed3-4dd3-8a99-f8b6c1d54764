import { ModuleRef } from '@nestjs/core'
import { Args, ObjectType, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql'
import { Document } from 'mongodb'

import {
  Role,
  RoleService,
  Organization,
  OrganizationService,
  User,
  UserService,
  Admins,
} from '@phigital-loyalty/accounts'
import { Roles, Role as IRole, AdminRole, paginated } from '@phigital-loyalty/core'
import { BaseResolver, FilterArgs, getPagingParametersForMongoDB } from '@phigital-loyalty/database'

@ObjectType()
export class PaginatedAdmins extends paginated(Admins) {}

@Resolver(Role)
export class RoleResolver extends BaseResolver(Role) {
  constructor(
    readonly entityService: RoleService,
    readonly moduleRef: ModuleRef,
    readonly userService: UserService,
    readonly organizationService: OrganizationService,
  ) {
    super(entityService, moduleRef)
  }

  @Query(() => Role)
  async role() {
    return null
  }

  @ResolveField(() => User)
  async user(@Parent() { userId }: Role): Promise<User | Error> {
    return this.userService.load(userId)
  }

  @ResolveField(() => Organization, { nullable: true })
  async organization(@Parent() { organizationId }: Role): Promise<Organization | Error> {
    return this.organizationService.load(organizationId)
  }

  @Query(() => PaginatedAdmins)
  @Roles('Support', 'Developer')
  async admins(
    @Args('roles', { type: () => [AdminRole], nullable: true }) roles?: AdminRole[],
    @Args('filter', { nullable: true }) filter?: FilterArgs<User>,
  ) {
    const { query, options } = getPagingParametersForMongoDB(filter)

    const aggregation: Document[] = [
      {
        $match: {
          role: { $in: roles?.length ? roles : [IRole.SystemAdmin, IRole.Support, IRole.Developer] },
        },
      },
    ]

    if (query) {
      aggregation.push({
        $lookup: {
          from: 'user',
          localField: 'userId',
          foreignField: '_id',
          as: 'user',
          pipeline: [{ $match: query }],
        },
      })
    } else {
      aggregation.push({
        $lookup: {
          from: 'user',
          localField: 'userId',
          foreignField: '_id',
          as: 'user',
        },
      })
    }

    aggregation.push({
      $unwind: {
        path: '$user',
      },
    })

    aggregation.push({
      $group: {
        _id: '$userId',
        user: { $addToSet: '$user' },
        // roles: { $addToSet: '$role' },
      },
    })

    aggregation.push({
      $lookup: {
        from: 'role',
        localField: '_id',
        foreignField: 'userId',
        as: 'roles',
        pipeline: [
          {
            $match: {
              role: { $in: [IRole.SystemAdmin, IRole.Support, IRole.Developer] },
            },
          },
        ],
      },
    })

    aggregation.push({
      $unwind: {
        path: '$user',
      },
    })

    aggregation.push({
      $addFields: {
        'user.roles': {
          $map: {
            input: '$roles',
            as: 'role',
            in: '$$role.role',
          },
        },
      },
    })

    aggregation.push({
      $replaceRoot: {
        newRoot: '$user',
      },
    })

    aggregation.push({
      $facet: {
        nodes: [
          {
            $sort: options.sort ?? { _id: -1 },
          },
          ...(options?.skip
            ? [
                {
                  $skip: options.skip,
                },
              ]
            : []),
          { $limit: options?.limit || 10 },
        ],
        totalCount: [
          {
            $count: 'count',
          },
        ],
      },
    })

    const results = await this.entityService.aggregate(aggregation)

    return { nodes: results[0]?.nodes ?? [], totalCount: results[0]?.totalCount[0]?.count ?? 0 }
  }
}
