import { BadRequestException } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Args, Mu<PERSON>, Parent, ResolveField, Resolver } from '@nestjs/graphql'

import {
  OidcClient,
  OidcClientService,
  Organization,
  OrganizationService,
  RoleService,
  User,
  UserService,
  UserWithRole,
} from '@phigital-loyalty/accounts'
import { Action, CheckPolicies, CurrentAuth, type IntrospectionResponse } from '@phigital-loyalty/core'
import { BaseResolver, FilterArgs, getPagingParametersForMongoDB } from '@phigital-loyalty/database'

@Resolver(() => Organization)
export class OrganizationResolver extends BaseResolver(Organization, ['entity', 'entities', 'delete']) {
  constructor(
    readonly entityService: OrganizationService,
    readonly moduleRef: ModuleRef,
    readonly roleService: RoleService,
    readonly userService: UserService,
    readonly oidcClientService: OidcClientService,
  ) {
    super(entityService, moduleRef)
  }

  @Mutation(() => Organization, { name: `createOrganization` })
  @CheckPolicies(Action.Create, Organization, { object: 'partialEntity' })
  async create(
    @Args({ name: 'partialEntity', type: () => Organization.options?.createInput() })
    partialEntity: Partial<Organization>,
  ) {
    return this.entityService.createOne(partialEntity)
  }

  @Mutation(() => [Organization], { name: `createOrganizations` })
  @CheckPolicies(Action.Manage, Organization)
  async createMany(
    @Args({ name: 'partialEntities', type: () => [Organization.options?.createInput()] })
    partialEntities: Partial<Organization>[],
  ) {
    return this.entityService.createMany(partialEntities)
  }

  @Mutation(() => Organization, { name: `updateOrganization` })
  @CheckPolicies(Action.Update, Organization, { object: 'partialEntity' })
  async update(
    @Args({ name: 'partialEntity', type: () => Organization.options?.updateInput() })
    partialEntity: Partial<Organization>,
  ) {
    if (!partialEntity._id) {
      throw new BadRequestException(`Missing Organization ID field when trying to update entity!`)
    }

    const filter = { _id: { ...partialEntity }._id }
    delete partialEntity._id
    return this.entityService.updateOne(filter, partialEntity)
  }

  @ResolveField(() => UserWithRole)
  @CheckPolicies(Action.Manage, Organization)
  async users(
    @Parent() { _id }: Organization,
    @Args('filter', { nullable: true }) filter?: FilterArgs<User>,
  ): Promise<(UserWithRole | Error)[]> {
    const usersInOrg = await this.roleService.find({ organizationId: _id })
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), _id: { $in: usersInOrg.map((item) => item.userId) } },
    })
    const users = await this.userService.find(query, options)

    return users.map((user: UserWithRole) => {
      user.role = usersInOrg.find((item) => item.userId.toString() === user._id.toString()).role

      return user
    })
  }

  @ResolveField(() => OidcClient)
  @CheckPolicies(Action.Read, OidcClient, { parentIdField: '_id' })
  async oidcClients(
    @Parent() { _id }: Organization,
    @Args('filter', { nullable: true }) filter?: FilterArgs<OidcClient>,
  ): Promise<(OidcClient | Error)[]> {
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), organizationId: _id },
    })
    return this.oidcClientService.find(query, options)
  }

  @ResolveField(() => [String], { nullable: true })
  async customDomains(@Parent() { _id }: Organization) {
    return this.oidcClientService
      .find({ organizationId: _id, customDomains: { $exists: true } })
      .then((clients) => [...new Set(clients.flatMap((c) => c.customDomains || []))])
  }

  @Mutation(() => User)
  @CheckPolicies(Action.Update, OidcClient, { reference: 'id' })
  async organizationSendVerification(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args('id') id: string,
    @Args('type') type: string,
  ) {
    await this.entityService.sendVerificationCodes(id, auth.client_id, type)
    return this.entityService.findById(id)
  }
}
