import { FederatedUserResolver } from './federated-user.resolver'
import { InvitationResolver } from './invitation.resolver'
import { OidcClientResolver } from './oidc-client.resolver'
import { OrganizationResolver } from './organization.resolver'
import { RoleResolver } from './role.resolver'
import { UserResolver } from './user.resolver'

export const accountsResolvers = [
  InvitationResolver,
  OidcClientResolver,
  OrganizationResolver,
  RoleResolver,
  UserResolver,

  FederatedUserResolver,
]
