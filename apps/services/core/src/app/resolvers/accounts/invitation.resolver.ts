import { BadRequestException } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Args, Mutation, Parent, ResolveField, Resolver } from '@nestjs/graphql'
import { ObjectId } from 'mongodb'

import {
  User,
  Organization,
  OrganizationService,
  UserService,
  Invitation,
  InvitationService,
} from '@phigital-loyalty/accounts'
import { CheckPolicies, Action, CurrentUserId, CurrentAuth, type IntrospectionResponse } from '@phigital-loyalty/core'
import { BaseResolver, FilterOneArgs } from '@phigital-loyalty/database'

@Resolver(() => Invitation)
export class InvitationResolver extends BaseResolver(Invitation, ['entity', 'entities', 'delete']) {
  constructor(
    readonly entityService: InvitationService,
    readonly moduleRef: ModuleRef,
    readonly userService: UserService,
    readonly organizationService: OrganizationService,
  ) {
    super(entityService, moduleRef)
  }

  @Mutation(() => Invitation, { name: 'createInvitation' })
  @CheckPolicies(Action.Create, Invitation, { object: 'partialEntity' })
  async create(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args({ name: 'partialEntity', type: () => Invitation.options.createInput() }) partialEntity: Partial<Invitation>,
  ) {
    return this.entityService.createInvitation(
      { ...partialEntity, createdById: new ObjectId(auth.sub) },
      auth.client_id,
    )
  }

  @Mutation(() => [Invitation], { name: 'createInvitations' })
  @CheckPolicies(Action.Manage, Invitation)
  async createMany(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args({ name: 'partialEntities', type: () => [Invitation.options.createInput()] })
    partialEntities: Partial<Invitation>[],
  ) {
    return this.entityService.createInvitations(
      partialEntities.map((item) => ({ ...item, createdById: new ObjectId(auth.sub) })),
      auth.client_id,
    )
  }

  @Mutation(() => Invitation, { name: 'updateInvitation' })
  @CheckPolicies(Action.Update, Invitation, { object: 'partialEntity' })
  async update(
    @CurrentAuth() auth: IntrospectionResponse,
    @Args({ name: 'partialEntity', type: () => Invitation.options.updateInput() }) partialEntity: Partial<Invitation>,
    @Args({ name: 'upsert', type: () => Boolean }) upsert = true,
  ) {
    if (Object.keys(partialEntity).find((f) => f === 'id' || f === '_id')) {
      if (partialEntity.code) {
        const exists = await this.entityService.findOne({ code: partialEntity.code })
        if (exists && exists._id.toString() !== partialEntity._id.toString()) {
          throw new BadRequestException('Duplicate code. Code must be unique.')
        }
      }

      return this.entityService.updateOne({ _id: partialEntity._id }, partialEntity)
    } else if (upsert) {
      const newInvitation = await this.entityService.createInvitation(partialEntity, auth.client_id)
      return this.entityService.createOne(newInvitation)
    } else {
      throw new BadRequestException(`Missing Invitation ID field when trying to create new entity!`)
    }
  }

  @Mutation(() => Invitation, { name: 'answerInvitation' })
  @CheckPolicies('Answer', Invitation, { filter: 'filter' })
  async answer(
    @CurrentUserId() currentUserId: ObjectId,
    @Args({ name: 'filter' }) filter: FilterOneArgs<Invitation>,
    @Args({ name: 'answer', type: () => Boolean }) answer: boolean,
  ) {
    if (!filter.where && !filter.id) {
      throw new BadRequestException(`Missing either "where" or "id" argument for this query!`)
    }

    const invitation = await this.entityService.findOne(filter.id ? { _id: new ObjectId(filter.id) } : filter.where)

    return this.entityService.consumeCode(invitation.code, currentUserId, answer)
  }

  @ResolveField(() => [User], { nullable: true })
  @CheckPolicies(Action.Read, User, { parentIdField: 'consumerIds' })
  async consumers(@Parent() { consumerIds }: Invitation) {
    if (!consumerIds?.length) {
      return []
    }

    return this.userService.find({ _id: { $in: consumerIds } })
  }

  @ResolveField(() => [User], { nullable: true })
  @CheckPolicies(Action.Read, User, { parentIdField: 'userIds' })
  async users(@Parent() { userIds }: Invitation) {
    if (!userIds?.length) {
      return []
    }

    return this.userService.find({ _id: { $in: userIds } })
  }

  @ResolveField(() => Organization, { nullable: true })
  async organization(@Parent() { organizationId }: Invitation) {
    if (!organizationId) {
      return null
    }

    return this.organizationService.load(organizationId)
  }

  @ResolveField(() => User, { nullable: true })
  @CheckPolicies(Action.Read, User, { parentIdField: 'createdById' })
  async createdBy(@Parent() { createdById }: Invitation) {
    if (!createdById) {
      return
    }

    return this.userService.findById(createdById)
  }
}
