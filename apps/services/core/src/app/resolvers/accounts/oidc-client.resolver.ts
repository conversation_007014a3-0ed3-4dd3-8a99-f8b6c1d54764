import { ModuleRef } from '@nestjs/core'
import { Args, Mu<PERSON>, Parent, ResolveField, Resolver } from '@nestjs/graphql'
import { ObjectId } from 'mongodb'

import {
  OidcClientCreateInput,
  OidcClient,
  OidcClientService,
  Organization,
  OrganizationService,
} from '@phigital-loyalty/accounts'
import { Action, CheckPolicies } from '@phigital-loyalty/core'
import { BaseResolver } from '@phigital-loyalty/database'

@Resolver(() => OidcClient)
export class OidcClientResolver extends BaseResolver(OidcClient, ['entity', 'entities', 'update', 'delete']) {
  constructor(
    readonly entityService: OidcClientService,
    readonly moduleRef: ModuleRef,
    readonly organizationService: OrganizationService,
  ) {
    super(entityService, moduleRef)
  }

  @Mutation(() => OidcClient, { name: 'createOidcClient' })
  @CheckPolicies(Action.Create, OidcClient)
  async create(
    @Args({ name: 'partialEntity', type: () => OidcClient.options.createInput() }) partialEntity: OidcClientCreateInput,
  ) {
    if (partialEntity.grant_types?.length && partialEntity.grant_types.includes('client_credentials')) {
      ;(partialEntity as OidcClient).client_id = new ObjectId().toString()
    }

    return this.entityService.createOne({ ...partialEntity })
  }

  @Mutation(() => [OidcClient], { name: 'createOidcClients' })
  @CheckPolicies(Action.Create, OidcClient)
  async createMany(
    @Args({ name: 'partialEntities', type: () => [OidcClient.options.createInput()] })
    partialEntities: OidcClientCreateInput[],
  ) {
    for (const partialEntity of partialEntities) {
      if (partialEntity.grant_types?.length && partialEntity.grant_types.includes('client_credentials')) {
        ;(partialEntity as OidcClient).client_id = new ObjectId().toString()
      }
    }

    return this.entityService.createMany(partialEntities)
  }

  @ResolveField(() => Organization)
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  async organization(@Parent() { organizationId }: OidcClient): Promise<Organization | Error> {
    return this.organizationService.load(organizationId)
  }
}
