import { ModuleRef } from '@nestjs/core'
import { Resolver } from '@nestjs/graphql'

import { FederatedUser, FederatedUserService } from '@phigital-loyalty/accounts'
import { BaseResolver } from '@phigital-loyalty/database'

@Resolver(FederatedUser)
export class FederatedUserResolver extends BaseResolver(FederatedUser, ['entity', 'entities', 'create', 'delete']) {
  constructor(
    readonly entityService: FederatedUserService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }
}
