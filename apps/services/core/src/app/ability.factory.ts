import { Injectable } from '@nestjs/common'
import { ObjectId } from 'mongodb'

import { User, OidcClient, Organization, Role, Invitation, FederatedUser } from '@phigital-loyalty/accounts'
import {
  Action,
  BaseAbilityFactory,
  Can,
  Cannot,
  IntrospectionResponse,
  Role as IRole,
  Comment,
  Rate,
  EntityEvent,
  Installation,
  Notification,
  Webhook,
  type UserOrgRolesMap,
} from '@phigital-loyalty/core'

@Injectable()
export class AbilityFactory extends BaseAbilityFactory {
  async rules(auth: IntrospectionResponse, can: Can, _cannot: Cannot) {
    // User Entity
    can(Action.Read, User)
    can(Action.Read, OidcClient)

    can(Action.Read, Comment)
    can(Action.Read, Rate)

    if (!auth) {
      return
    }

    const rolesMap = this.buildRolesMap(auth) as UserOrgRolesMap<never> // TODO: fix can types to support strings as ids
    const organizations = this.listOrganizationIds(auth, ObjectId)

    const orgsAsOwner = this.orgIdsForRoles(['Owner'], rolesMap)
    const orgsAsAdmin = this.orgIdsForRoles(['Owner', 'Admin'], rolesMap)

    // User Entity
    can(Action.Update, User, { _id: auth.sub })
    can(Action.Delete, User, { _id: auth.sub })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Update, User)
      can(Action.Stats, User)
    }

    // Invitation Entity
    can(Action.Read, Invitation, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Create, Invitation, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Update, Invitation, { organizationId: { $in: orgsAsAdmin } })
    can('Answer', Invitation, {
      userIds: auth.sub,
    })
    can(Action.Delete, Invitation, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Stats, Invitation, {
      organizationId: { $in: orgsAsAdmin },
    })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, Invitation)
      can(Action.Create, Invitation)
      can(Action.Update, Invitation)
      can(Action.Delete, Invitation)
      can(Action.Stats, Invitation)
    }

    // Organization Entity
    can(Action.Read, Organization)
    can(Action.Create, Organization)
    can(Action.Update, Organization, { _id: { $in: orgsAsAdmin } })
    can(Action.Delete, Organization, { _id: { $in: orgsAsOwner } })
    can(Action.Stats, Organization, { _id: { $in: orgsAsOwner } })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Update, Organization)
      can(Action.Delete, Organization)
      can(Action.Stats, Organization)
    }

    // FederatedUser Entity
    can(Action.Read, FederatedUser, { sub: auth.sub })
    can(Action.Create, FederatedUser, { sub: auth.sub })
    can(Action.Update, FederatedUser, { sub: auth.sub })
    can(Action.Delete, FederatedUser, { sub: auth.sub })
    can(Action.Stats, FederatedUser, { sub: auth.sub })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, FederatedUser)
      can(Action.Create, FederatedUser)
      can(Action.Update, FederatedUser)
      can(Action.Delete, FederatedUser)
      can(Action.Stats, FederatedUser)
    }

    // Role Entity
    can(Action.Read, Role, { userId: auth.sub })
    can(Action.Read, Role, { organizationId: { $in: organizations } })
    can(Action.Update, Role, {
      organizationId: { $in: orgsAsOwner },
      role: {
        $in: [
          IRole.Owner,
          IRole.Admin,
          IRole.Author,
          IRole.Contributor,
          IRole.Editor,
          IRole.Moderator,
          IRole.Accountant,
          IRole.User,
        ],
      },
    })
    can(Action.Update, Role, {
      organizationId: { $in: this.orgIdsForRoles(['Admin'], rolesMap) },
      role: {
        $in: [IRole.Author, IRole.Contributor, IRole.Editor, IRole.Moderator, IRole.Accountant, IRole.User],
      },
    })
    can(Action.Delete, Role, { organizationId: { $in: orgsAsOwner } })
    can(Action.Stats, Role, { organizationId: { $in: orgsAsOwner } })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, Role)
      can(Action.Create, Role)
      can(Action.Manage, Role)
      can(Action.Update, Role)
      can(Action.Delete, Role)
      can(Action.Stats, Role)
    }

    // Installation Entity
    can(Action.Read, Installation, { userId: auth.sub })
    can(Action.Create, Installation, { userId: auth.sub })
    can(Action.Update, Installation, { userId: auth.sub })
    can(Action.Delete, Installation, { userId: auth.sub })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, Installation)
      can(Action.Create, Installation)
      can(Action.Update, Installation)
      can(Action.Delete, Installation)
    }

    // Notification Entity
    can(Action.Read, Notification, { userId: auth.sub })
    can(Action.Update, Notification, { userId: auth.sub })

    // OidcClient Entity
    can(Action.Read, OidcClient, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Create, OidcClient, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Update, OidcClient, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Delete, OidcClient, { organizationId: { $in: orgsAsAdmin } })
    can(Action.Stats, OidcClient, { organizationId: { $in: orgsAsAdmin } })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, OidcClient)
      can(Action.Create, OidcClient)
      can(Action.Manage, OidcClient)
      can(Action.Update, OidcClient)
      can(Action.Delete, OidcClient)
      can(Action.Stats, OidcClient)
    }

    // User Friends Plugin
    can('Friends', User, { _id: auth.sub })
    can('FriendRequests', User, { _id: auth.sub })

    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can('Friends', User)
      can('FriendRequests', User)
    }

    // User Follow Plugin
    can('Follow', User, { _id: auth.sub })
    can('Following', User, { _id: auth.sub })
    can('Followers', User, { _id: auth.sub })

    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can('Following', User)
      can('Followers', User)
    }

    // User Block Plugin
    can('Blocking', User, { _id: auth.sub })
    can('Block', User, { _id: auth.sub })
    can('Blocked', User, { _id: auth.sub })

    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can('Blocking', User)
      can('Block', User)
      can('Blocked', User)
    }

    can(Action.Create, Comment)
    can(Action.Update, Comment, { [Comment.options.owner]: auth.sub })
    can(Action.Delete, Comment, { [Comment.options.owner]: auth.sub })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Create, Comment)
      can(Action.Manage, Comment)
      can(Action.Update, Comment)
      can(Action.Delete, Comment)
      can(Action.Stats, Comment)
    }

    can(Action.Create, Rate)
    can(Action.Update, Rate, { [Rate.options.owner]: auth.sub })
    can(Action.Delete, Rate, { [Rate.options.owner]: auth.sub })
    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Create, Rate)
      can(Action.Manage, Rate)
      can(Action.Update, Rate)
      can(Action.Delete, Rate)
      can(Action.Stats, Rate)
    }

    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, EntityEvent)
      can(Action.Stats, EntityEvent)
    }

    // Webhooks Plugin
    can(Action.Read, Webhook, { parent: 'Organization', parentId: { $in: orgsAsAdmin } })
    can(Action.Create, Webhook, { parent: 'Organization', parentId: { $in: orgsAsAdmin } })
    can(Action.Update, Webhook, { parent: 'Organization', parentId: { $in: orgsAsAdmin } })
    can(Action.Delete, Webhook, { parent: 'Organization', parentId: { $in: orgsAsAdmin } })
    can(Action.Stats, Webhook, { parent: 'Organization', parentId: { $in: orgsAsAdmin } })

    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can(Action.Read, Webhook)
      can(Action.Create, Webhook)
      can(Action.Manage, Webhook)
      can(Action.Update, Webhook)
      can(Action.Delete, Webhook)
      can(Action.Stats, Webhook)
    }
  }
}
