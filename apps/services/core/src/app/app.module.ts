import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import { AccountsModule } from '@phigital-loyalty/accounts'
import { ServiceModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database/mongodb'

import { AbilityFactory } from './ability.factory'
import { resolvers } from './resolvers'

@Module({
  imports: [
    ServiceModule.forRoot(AbilityFactory, {
      cls: {},
    }),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    AccountsModule.forRoot(),
  ],
  controllers: [],
  providers: [...resolvers],
})
export class AppModule {}
