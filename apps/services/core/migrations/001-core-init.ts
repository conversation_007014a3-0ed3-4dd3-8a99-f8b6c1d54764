import { hash } from 'argon2'
import { nanoid } from 'nanoid'

import { InvitationType } from '@phigital-loyalty/accounts'
import { Role as IRole } from '@phigital-loyalty/core'

import type { Organization, OidcClient, User, Role, Invitation } from '@phigital-loyalty/accounts'
import type { TimeZone } from '@phigital-loyalty/core'
import type { Db, ObjectId } from 'mongodb'

module.exports = {
  up: async (db: Db) => {
    // Internal Users
    const { admin } = await createInternalUsers(db)

    // Reality Connect

    const rc = await db.collection<Omit<Organization, '_id'>>('organization').insertOne({
      name: 'DapTap',
      tag: 'daptap',
      description: 'DapTap',
      logo: '/assets/icon/logo.png',
      contactEmail: '<EMAIL>',
      billing: '<EMAIL>',
      billingVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    await createUsersForOrg(db, rc.insertedId)

    await db.collection<Omit<Invitation, '_id'>>('invitation').insertMany([
      {
        organizationId: rc.insertedId,

        code: nanoid(6),
        type: InvitationType.INVITATION_MULTIPLE,
        limit: 20,

        createdAt: new Date(),
        updatedAt: new Date(),

        createdById: admin.insertedId,
      },
    ])

    // Demo

    const demo = await db.collection<Omit<Organization, '_id'>>('organization').insertOne({
      name: 'Demo',
      tag: 'demo',
      billing: '<EMAIL>',
      contactEmail: '<EMAIL>',
      logo: '/icons/<EMAIL>',
      billingVerified: false,
      contactPhoneNumber: null,
      description: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    await createUsersForOrg(db, demo.insertedId)

    await createZapierTestUser(db, demo.insertedId)

    // OIDC Clients

    await db.collection<Omit<OidcClient, '_id'>>('oidcClient').insertMany([
      {
        grant_types: ['authorization_code'],
        id_token_signed_response_alg: 'RS256',
        authorization_signed_response_alg: 'RS256',
        response_types: ['code'],
        token_endpoint_auth_method: 'client_secret_basic',
        application_type: 'web',
        post_logout_redirect_uris: ['https://local.rc:3000/logout/cb'],
        require_auth_time: false,
        subject_type: 'public',
        introspection_endpoint_auth_method: 'client_secret_basic',
        revocation_endpoint_auth_method: 'client_secret_basic',
        backchannel_logout_session_required: false,
        frontchannel_logout_session_required: false,
        require_signed_request_object: false,
        request_uris: [],
        require_pushed_authorization_requests: false,
        tls_client_certificate_bound_access_tokens: false,
        client_id_issued_at: 1602787659,
        client_id: 'api',
        client_secret_expires_at: 0,
        client_secret: await hash(
          'qQwTUbax5lmGW94zk80zWGsUK7QrSAaczHUyrq62PK4VsdtZHtZiAzcqIjFoHPh3Hq5c9b5VRMmIcOi8VQVaoA',
        ),
        redirect_uris: [
          'http://localhost:3876/callback',
          'http://localhost:3876/close',
          'https://local.rc:3000/callback',
          'https://oidc.local.rc/callback',

          'https://gateway.daptapgo.io',
          'https://gateway-develop.daptapgo.io',
          'https://gateway-stage.daptapgo.io',
          'https://gateway-temp.daptapgo.io',
        ],
        userinfo_signed_response_alg: 'RS256',
        registration_client_uri: 'https://local.rc:4000/reg/api',
        registration_access_token: '3sBRCwmgmP2bysE32RhuYWUIVFWt1zhmLg0KA5sjEV3',
      } as unknown as OidcClient,
      {
        client_id: 'web',
        redirect_uris: [
          'http://localhost:3876/callback',
          'https://studio.apollographql.com/explorer-oauth2',
          'https://sandbox.embed.apollographql.com/explorer-oauth2',
          'https://openidconnect.net/callback',
          'https://oauth.pstmn.io/v1/browser-callback',
          'https://localhost:5173/',
          'https://localhost:5173/auth/callback/oidc',
          'https://*.local.rc/',
          'https://*.local.rc/auth/callback/oidc',
          'https://*.localrc.net/',
          'https://*.localrc.net/auth/callback/oidc',
          'https://*.daptapgo-io.pages.dev/',
          'https://*.daptapgo-io.pages.dev/auth/callback/oidc',
          'https://*.daptapgo.io/',
          'https://*.daptapgo.io/auth/callback/oidc',
          'https://daptapgo.io/',
          'https://daptapgo.io/auth/callback/oidc',
        ],
        application_type: 'web',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        client_id: 'android',
        redirect_uris: [
          'https://local.rc:4200',
          'https://local.rc:4200/index.html',
          'https://local.rc:4200/silent-refresh.html',
          'http://localhost:3876/callback',
        ],
        application_type: 'native',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        client_id: 'ios',
        redirect_uris: [
          'https://local.rc:4200',
          'https://local.rc:4200/index.html',
          'https://local.rc:4200/silent-refresh.html',
          'http://localhost:3876/callback',
        ],
        application_type: 'native',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        organizationId: demo.insertedId,
        client_id: 'demo',
        redirect_uris: [
          'https://localhost:5173/auth/callback/oidc',
          'https://demo.daptapgo.io/auth/callback/oidc',
          'https://demo-developer.daptapgo.io/auth/callback/oidc',
        ],
        application_type: 'web',
        customDomains: ['demo.daptapgo.io', 'demo-develop.daptapgo.io', 'localhost:5173'],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ])

    return
  },
  down: async (_db: Db) => {
    return
  },
}

async function createInternalUsers(db: Db) {
  const userCollection = db.collection<Omit<User, '_id'>>('user')
  const roleCollection = db.collection<Omit<Role, '_id'>>('role')

  const admin = await userCollection.insertOne({
    name: 'System Admin',

    familyName: 'Admin',
    givenName: 'System',
    nickname: 'SystemAdmin',
    password: await hash('MOMENTO-sculptor-mitten'),

    picture: '/icons/<EMAIL>',

    email: '<EMAIL>',
    emailVerified: true,
    locale: 'en-US',
    zoneinfo: 'Europe/Lisbon' as TimeZone,

    createdAt: new Date(),
    updatedAt: new Date(),
  })

  const ribeiro = await userCollection.insertOne({
    name: 'João Ribeiro',
    familyName: 'Ribeiro',
    givenName: 'João',
    password: '$argon2id$v=19$m=4096,t=3,p=1$jDHVE/gxPLkdfoGZrOBMqg$ljeNcGP9CTikB0xKfVePr+AQSfnh4l7/zxqf+0kZxRI',
    // middleName: string
    nickname: 'JonnyBGod',
    picture: 'https://s.gravatar.com/avatar/5a92959c6ad831a200616e277c136997',
    // birthdate: Date
    // Emails
    email: '<EMAIL>',
    emailVerified: true,

    // contactEmail: string
    // contactEmailVerified: boolean
    // recoveryEmail: 'string'
    // recoveryEmailVerified: boolean
    // adicionalEmail: string
    // adicionalEmailVerified: boolean
    // Phone
    phoneNumber: '+351916002675',
    phoneNumberVerified: true,

    // Address
    address: {
      formatted: 'Rua António Saúde 7, 4Andar',
      streetAddress: 'Rua António Saúde 7, 4Andar',
      locality: 'Lisboa',
      postalCode: '1500-048',
      region: 'Lisboa',
      country: 'Portugal',
    },

    // Extras
    // gender: string
    // website: URL
    locale: 'pt-PT',
    zoneinfo: 'Europe/Lisbon' as TimeZone,

    createdAt: new Date(),
    updatedAt: new Date(),
  })

  await roleCollection.insertMany([
    {
      userId: admin.insertedId,
      role: IRole.SystemAdmin,

      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      userId: ribeiro.insertedId,
      role: IRole.SystemAdmin,

      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ])

  return { admin, ribeiro }
}

async function createUsersForOrg(db: Db, orgId: ObjectId) {
  const userCollection = db.collection<Omit<User, '_id'>>('user')
  const roleCollection = db.collection<Omit<Role, '_id'>>('role')

  const [ribeiro] = await Promise.all([userCollection.findOne({ email: '<EMAIL>' })])

  if (ribeiro) {
    await roleCollection.insertMany([
      {
        userId: ribeiro._id,
        organizationId: orgId,
        role: IRole.Owner,

        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ])
  }
}

async function createZapierTestUser(db: Db, orgId: ObjectId) {
  const userCollection = db.collection<Omit<User, '_id'>>('user')
  const roleCollection = db.collection<Omit<Role, '_id'>>('role')

  const zapierTest = await userCollection.insertOne({
    password: await hash('zapierTesting123!'),

    address: {
      country: '000',
      formatted: '000',
      locality: '000',
      postalCode: '000',
      region: '000',
      streetAddress: '000',
    },
    birthdate: '1987-10-16' as unknown as Date,
    email: '<EMAIL>',
    emailVerified: true,
    familyName: 'Zapier',
    givenName: 'Integration',
    middleName: 'Testing',
    name: 'Integration Testing Zapier',
    nickname: 'Zapier',
    phoneNumber: '+49 000 000000',
    phoneNumberVerified: true,
    picture: 'https://picsum.photos/400/200/',

    createdAt: new Date(),
    updatedAt: new Date(),
  })

  await roleCollection.insertMany([
    {
      userId: zapierTest.insertedId,
      organizationId: orgId,
      role: IRole.User,

      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ])
}
