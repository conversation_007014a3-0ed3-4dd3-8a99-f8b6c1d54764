import { accountsIndexes } from '@phigital-loyalty/accounts'
import { featuresIndexes } from '@phigital-loyalty/database'

import type { Db } from 'mongodb'

module.exports = {
  up: async (db: Db) => {
    for (const indexMap of [featuresIndexes(), accountsIndexes()]) {
      for (const [collection, indexes] of Object.entries(indexMap)) {
        for (const index of indexes) {
          await db.collection(collection).createIndex(index.spec, index.option)
        }
      }
    }
  },
  down: async (db: Db) => {
    for (const indexMap of [featuresIndexes(), accountsIndexes()]) {
      for (const [collection, indexes] of Object.entries(indexMap)) {
        for (const index of indexes) {
          await db.collection(collection).dropIndex(index.option.name)
        }
      }
    }
  },
}
