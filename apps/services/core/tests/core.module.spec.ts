import assert from 'node:assert/strict'

import { jest } from '@jest/globals'
import { GraphQLModule } from '@nestjs/graphql'
import { Test } from '@nestjs/testing'
import { gql } from 'graphql-tag'
import { ObjectId } from 'mongodb'

import { MongoDBTestClient } from '@phigital-loyalty/database/lib/mongodb/testing'

import { AppModule } from '../src/app/app.module'

import type { ApolloServer } from '@apollo/server'
import type { ApolloDriver } from '@nestjs/apollo'
import type { INestApplication } from '@nestjs/common'
import type { TestingModule } from '@nestjs/testing'

jest.setTimeout(10000)

const MeQuery = gql`
  query {
    me {
      _id
      name
    }
  }
`

const populateDocuments = {
  user: [
    {
      _id: new ObjectId('61b40e500aa38d35f8095b48'),
      name: '<PERSON>',
    },
  ],
}

describe('Core', () => {
  const mongodbClient = new MongoDBTestClient('Core')

  let app: INestApplication
  let moduleRef: TestingModule
  let apolloClient: ApolloServer

  process.env.CLS_NO_MOUNT = 'true'
  process.env.DISABLE_ENTITY_EVENTS = 'true'
  process.env.DISABLE_WEBHOOKS = 'true'

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [AppModule],
    }).compile()

    app = moduleRef.createNestApplication({ forceCloseConnections: true })
    app.enableShutdownHooks()
    await app.init()

    const graphqlModule = app.get<GraphQLModule<ApolloDriver>>(GraphQLModule)
    apolloClient = graphqlModule.graphQlAdapter?.instance

    await mongodbClient.populate(populateDocuments)
  })

  afterAll(async () => {
    app?.close()
    await mongodbClient?.close()
  })

  it(`Me query should fail when no auth is passed.`, async () => {
    const response = await apolloClient.executeOperation(
      { query: MeQuery },
      {
        contextValue: {},
      },
    )

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeDefined()
    expect(response.body.singleResult.errors[0]?.message).toBe('Forbidden resource')
  })

  it(`Me query should return current user when auth is passed.`, async () => {
    const response = await apolloClient.executeOperation(
      { query: MeQuery },
      {
        contextValue: {
          auth: {
            sub: '61b40e500aa38d35f8095b48',
            active: true,
            roles: [],
          },
        },
      },
    )

    assert(response.body.kind === 'single')
    expect(response.body.singleResult.errors).toBeUndefined()
    expect(response.body.singleResult).toMatchObject({
      data: {
        me: {
          _id: '61b40e500aa38d35f8095b48',
          name: 'John Doe',
        },
      },
    })
  })
})
