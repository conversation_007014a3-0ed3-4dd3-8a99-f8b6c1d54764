import { sleep } from 'k6'
import http from 'k6/http'

export const options = {
  vus: 100,
  duration: '30s',
  thresholds: {
    http_req_duration: ['p(99)<1500'], // 99% of requests must complete below 1.5s
  },
}

// Smoke Testing
// export const options = {
//   vus: 1, // 1 user looping for 1 minute
//   duration: '1m',
//   thresholds: {
//     http_req_duration: ['p(99)<1500'], // 99% of requests must complete below 1.5s
//   },
// }

// Load Testing
// export const options = {
//   stages: [
//     { duration: '5m', target: 100 }, // simulate ramp-up of traffic from 1 to 100 users over 5 minutes.
//     { duration: '10m', target: 100 }, // stay at 100 users for 10 minutes
//     { duration: '5m', target: 0 }, // ramp-down to 0 users
//   ],
//   thresholds: {
//     http_req_duration: ['p(99)<1500'], // 99% of requests must complete below 1.5s
//     'logged in successfully': ['p(99)<1500'], // 99% of requests must complete below 1.5s
//   },
// }

// Stress Testing
// export const options = {
//   stages: [
//     { duration: '2m', target: 100 }, // below normal load
//     { duration: '5m', target: 100 },
//     { duration: '2m', target: 200 }, // normal load
//     { duration: '5m', target: 200 },
//     { duration: '2m', target: 300 }, // around the breaking point
//     { duration: '5m', target: 300 },
//     { duration: '2m', target: 400 }, // beyond the breaking point
//     { duration: '5m', target: 400 },
//     { duration: '10m', target: 0 }, // scale down. Recovery stage.
//   ],
// }

// Spike Testing
// export const options = {
//   stages: [
//     { duration: '10s', target: 100 }, // below normal load
//     { duration: '1m', target: 100 },
//     { duration: '10s', target: 1400 }, // spike to 1400 users
//     { duration: '3m', target: 1400 }, // stay at 1400 for 3 minutes
//     { duration: '10s', target: 100 }, // scale down. Recovery stage.
//     { duration: '3m', target: 100 },
//     { duration: '10s', target: 0 },
//   ],
// }

const accessToken = '9KqKqRtpe3OXodnFpr_oYAYCs3lS4zi3A63QVHddpWf'

export default function () {
  const query = `
    query me {
      me {
        _id
        name
        picture
        email
        emailVerified
        contactEmail
        contactEmailVerified
        phoneNumber
        phoneNumberVerified
        address {
          streetAddress
          locality
          postalCode
          region
          country
        }
        locale
        zoneinfo
        organizations {
          _id
          name
          description
          logo
          role
        }
        roles {
          _id
          userId
          organizationId
          role
        }
        installations {
          _id
          subscription
          type
          userId
        }
      }
    }`

  const headers = {
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
    'accept-encoding': 'gzip, deflate, br',
  }

  const res = http.post('https://gateway.local.rc/graphql', JSON.stringify({ query: query }), {
    headers: headers,
    compression: 'deflate',
  })

  if (res.status !== 200) {
    // eslint-disable-next-line no-console
    console.log(res.body)
  }

  sleep(0.3)
}
