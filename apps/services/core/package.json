{"name": "@phigital-loyalty/services-core", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/reality-connect/seed-backend.git", "directory": "apps/services/core"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@nx/webpack": "20.0.10", "@phigital-loyalty/database": "0.0.0", "@phigital-loyalty/core": "0.0.0", "@phigital-loyalty/accounts": "0.0.0", "mongodb": "^6.11.0", "argon2": "^0.43.0", "nanoid": "^5.0.9", "@nestjs/common": "^11.0.7", "@nestjs/config": "^4.0.0", "@nestjs/core": "11.1.6", "@nestjs/graphql": "13.1.0", "webpack": "5.99.9"}}