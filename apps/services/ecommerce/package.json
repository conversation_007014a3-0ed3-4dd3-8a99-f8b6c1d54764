{"name": "@phigital-loyalty/services-ecommerce", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/services/ecommerce"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"stripe": "17.7.0", "mongodb": "^6.9.0", "@nx/webpack": "20.0.10", "@phigital-loyalty/stripe": "0.0.0", "@phigital-loyalty/core": "0.0.0", "@phigital-loyalty/database": "0.0.0", "@nestjs/common": "^11.0.7", "@nestjs/config": "^4.0.0", "@nestjs/graphql": "13.1.0", "nestjs-stripe": "^1.0.0", "webpack": "5.99.9"}}