import { Injectable } from '@nestjs/common'
import { ObjectId } from 'mongodb'

import { BaseAbilityFactory, Can, Cannot, IntrospectionResponse } from '@phigital-loyalty/core'
import {
  Organization,
  User,
  customerAbilities,
  invoiceAbilities,
  subscriptionAbilities,
  paymentIntentAbilities,
  paymentMethodAbilities,
} from '@phigital-loyalty/stripe'

@Injectable()
export class AbilityFactory extends BaseAbilityFactory {
  async rules(auth: IntrospectionResponse, can: Can, cannot: Cannot) {
    if (!auth) {
      customerAbilities(auth, can, cannot)
      invoiceAbilities(auth, can, cannot)
      subscriptionAbilities(auth, can, cannot)
      paymentIntentAbilities(auth, can, cannot)
      paymentMethodAbilities(auth, can, cannot)

      return
    }

    const rolesMap = this.buildRolesMap(auth)

    const orgsAsAdmin = this.orgIdsForRoles(['Owner', 'Admin'], rolesMap)

    // User Entity
    can('ReadCustomer', User, { _id: auth.sub })
    can('UpdateCustomer', User, { _id: auth.sub })

    // Organization Entity
    can('ReadCustomer', Organization, { _id: { $in: orgsAsAdmin as unknown as ObjectId[] } })
    can('UpdateCustomer', Organization, {
      _id: { $in: orgsAsAdmin as unknown as ObjectId[] },
    })

    customerAbilities(auth, can, cannot, rolesMap, orgsAsAdmin)
    invoiceAbilities(auth, can, cannot, rolesMap, orgsAsAdmin)
    subscriptionAbilities(auth, can, cannot, rolesMap, orgsAsAdmin)
    paymentIntentAbilities(auth, can, cannot, rolesMap, orgsAsAdmin)
    paymentMethodAbilities(auth, can, cannot, rolesMap, orgsAsAdmin)
  }
}
