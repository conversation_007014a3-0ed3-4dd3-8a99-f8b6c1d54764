import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import { ServiceModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database/mongodb'
import { Organization, StripeModule, User } from '@phigital-loyalty/stripe'

import { AbilityFactory } from './ability.factory'
import { resolvers } from './resolvers'

@Module({
  imports: [
    ServiceModule.forRoot(AbilityFactory, {
      cls: {},
      orphanedTypes: [User, Organization],
    }),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    StripeModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        apiKey: config.get('STRIPE_API_KEY'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [...resolvers],
})
export class AppModule {}
