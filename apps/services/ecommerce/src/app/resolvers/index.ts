import { UserResolver } from '@phigital-loyalty/stripe'

import { CustomerResolver } from './customer.resolver'
import { InvoiceResolver } from './invoice.resolver'
import { PaymentIntentResolver } from './payment-intent.resolver'
import { PaymentMethodResolver } from './payment-method.resolver'
import { SubscriptionResolver } from './subscription.resolver'

export const resolvers = [
  CustomerResolver,
  InvoiceResolver,
  SubscriptionResolver,
  PaymentIntentResolver,
  PaymentMethodResolver,

  UserResolver,
]
