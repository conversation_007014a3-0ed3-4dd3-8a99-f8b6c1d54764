import { Resolver } from '@nestjs/graphql'
import { InjectStripe } from 'nestjs-stripe'
import Stripe from 'stripe'

import { PaymentMethod, paymentMethodResolver, Organization, OrganizationService } from '@phigital-loyalty/stripe'

@Resolver(() => PaymentMethod)
export class PaymentMethodResolver extends paymentMethodResolver<Organization>(Organization) {
  constructor(
    @InjectStripe() readonly stripe: Stripe,
    entityService: OrganizationService,
  ) {
    super(stripe, entityService)
  }
}
