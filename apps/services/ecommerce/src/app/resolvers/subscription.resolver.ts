import { Resolver } from '@nestjs/graphql'
import { InjectStripe } from 'nestjs-stripe'
import Stripe from 'stripe'

import { subscriptionResolver, Organization, StripeSubscription, OrganizationService } from '@phigital-loyalty/stripe'

@Resolver(() => StripeSubscription)
export class SubscriptionResolver extends subscriptionResolver<Organization>(Organization) {
  constructor(
    @InjectStripe() readonly stripe: Stripe,
    entityService: OrganizationService,
  ) {
    super(stripe, entityService)
  }
}
