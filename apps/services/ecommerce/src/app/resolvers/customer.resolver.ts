import { Resolver } from '@nestjs/graphql'
import { InjectStripe } from 'nestjs-stripe'
import Stripe from 'stripe'

import { Customer, customerResolver, Organization, OrganizationService } from '@phigital-loyalty/stripe'

@Resolver(() => Customer)
export class CustomerResolver extends customerResolver<Organization>(Organization) {
  constructor(
    @InjectStripe() readonly stripe: Stripe,
    entityService: OrganizationService,
  ) {
    super(stripe, entityService)
  }
}
