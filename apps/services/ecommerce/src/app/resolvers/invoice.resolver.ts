import { Resolver } from '@nestjs/graphql'
import { InjectStripe } from 'nestjs-stripe'
import Stripe from 'stripe'

import { Invoice, invoiceResolver, Organization, OrganizationService } from '@phigital-loyalty/stripe'

@Resolver(() => Invoice)
export class InvoiceResolver extends invoiceResolver<Organization>(Organization) {
  constructor(
    @InjectStripe() readonly stripe: Stripe,
    entityService: OrganizationService,
  ) {
    super(stripe, entityService)
  }
}
