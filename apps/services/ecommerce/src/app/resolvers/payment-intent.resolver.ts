import { Resolver } from '@nestjs/graphql'
import { InjectStripe } from 'nestjs-stripe'
import Stripe from 'stripe'

import { PaymentIntent, paymentIntentResolver, Organization, OrganizationService } from '@phigital-loyalty/stripe'

@Resolver(() => PaymentIntent)
export class PaymentIntentResolver extends paymentIntentResolver<Organization>(Organization) {
  constructor(
    @InjectStripe() readonly stripe: Stripe,
    entityService: OrganizationService,
  ) {
    super(stripe, entityService)
  }
}
