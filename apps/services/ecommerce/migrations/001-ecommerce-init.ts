import Stripe from 'stripe'

import type { Db } from 'mongodb'

const stripe = new Stripe(process.env.STRIPE_API_KEY)

module.exports = {
  up: async (db: Db) => {
    const collection = db.collection('organization')

    const cursor = collection.find({ customerId: { $exists: false } })

    while (await cursor.hasNext()) {
      const org = await cursor.next()

      const customer = await stripe.customers.create({
        name: org.name,
        email: org.billing,
        phone: org.contactPhoneNumber,
        metadata: {
          type: 'organization',
          _id: org._id.toString(),
        },
      })

      await collection.updateOne(
        { _id: org._id },
        {
          $set: {
            customerId: customer.id,
          },
        },
      )
    }
    return
  },
  down: async (_db: Db) => {
    return
  },
}
