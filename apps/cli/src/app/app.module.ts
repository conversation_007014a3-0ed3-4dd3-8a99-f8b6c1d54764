import { join } from 'path'

import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import { CoreModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database'

import { apiProviders } from './api'
import { runScriptsProviders } from './run-script'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [
        ...(process.env.NODE_ENV === 'production' ? [join(process.cwd(), '.env.production')] : []),
        join(process.cwd(), '.env'),
      ],
    }),
    CoreModule.forRoot(),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
  ],
  providers: [...runScriptsProviders, ...apiProviders],
})
export class AppModule {}
