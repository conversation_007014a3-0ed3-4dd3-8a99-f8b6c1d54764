import { Command, CommandRunner, InquirerService } from 'nest-commander'

@Command({ name: 'run-script', arguments: '[script]', options: { isDefault: false } })
export class RunScriptCommand extends CommandRunner {
  constructor(private readonly inquirer: InquirerService) {
    super()
  }

  async run(inputs: string[], _options: Record<string, string>) {
    let script = inputs[0]
    if (!script) {
      script = (await this.inquirer.ask<{ script: string }>('run-script', undefined)).script
    }

    const scriptToRun = await import(`../scripts/${script}`)

    await scriptToRun.main()
  }
}
