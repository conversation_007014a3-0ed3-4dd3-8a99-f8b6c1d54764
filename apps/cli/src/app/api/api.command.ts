import { config } from 'dotenv'
import { Command, CommandRunner, InquirerService } from 'nest-commander'
import {
  discovery,
  Configuration as ClientConfiguration,
  clientCredentialsGrant,
  TokenEndpointResponse,
  TokenEndpointResponseHelpers,
} from 'openid-client'

import { Configuration, createConfiguration, TestApi } from '@phigital-loyalty/generate-api'

config()

@Command({ name: 'api', arguments: '[method]', options: { isDefault: false } })
export class ApiCommand extends CommandRunner {
  private clientConfig: ClientConfiguration
  private token: TokenEndpointResponse & TokenEndpointResponseHelpers
  private configuration: Configuration
  private apiInstance: TestApi

  constructor(private readonly inquirer: InquirerService) {
    super()
  }

  async run(inputs: string[], _options: Record<string, string>) {
    await this.init()

    let method = inputs[0]
    if (!method) {
      method = (await this.inquirer.ask<{ method: string }>('api', undefined)).method
    }

    let result: unknown
    if (method === 'test') {
      result = await this.apiInstance.testTest()
    }

    if (method === 'test/auth') {
      result = await this.apiInstance.testAuth()
    }

    if (method === 'test/permissions') {
      result = await this.apiInstance.testPermissions()
    }

    if (method === 'test/param') {
      result = await this.apiInstance.testParam({
        id: 'test',
      })
    }

    // eslint-disable-next-line no-console
    console.log(result)
  }

  async init() {
    if (!this.clientConfig) {
      this.clientConfig = await discovery(
        new URL(`${process.env.OIDC_ISSUER}/.well-known/openid-configuration`),
        '62ab5b4891eda513294bbd36',
        'RuqQRFBvNudR8F2rYWEE5zxknRoS2Djha2V9ykHyR8eYcsALziZ4U6N5IBGSGSr5vxgWxY4XfA6xXejdiIKb8o',
      )
    }

    if (!this.configuration) {
      this.configuration = createConfiguration({
        authMethods: {
          bearer: {
            tokenProvider: {
              getToken: async () => {
                if (!this.token?.expiresIn()) {
                  this.token = await clientCredentialsGrant(this.clientConfig, {
                    scope: 'openid profile email roles',
                  })
                }

                return this.token.access_token
              },
            },
          },
        },
      })
    }

    if (!this.apiInstance) {
      this.apiInstance = new TestApi(this.configuration)
    }
  }
}
