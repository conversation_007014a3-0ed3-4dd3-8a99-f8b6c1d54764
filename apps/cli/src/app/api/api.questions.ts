import { readdirSync } from 'fs'
import { join } from 'path'

import { ChoicesFor, Question, QuestionSet } from 'nest-commander'

@QuestionSet({ name: 'api' })
export class ApiQuestions {
  @Question({
    type: 'list',
    message: 'What script would you like to execute?',
    name: 'script',
  })
  paseTask(val: string) {
    return val
  }

  @ChoicesFor({ name: 'script' })
  choicesForScript() {
    const choices = []

    for (const filePath of walkSync(join(__dirname, '..', 'scripts'))) {
      choices.push(filePath.replace(`${join(__dirname, '..', 'scripts')}/`, '').replace(/.(ts|js)/, ''))
    }

    return choices
  }
}

function* walkSync(dir) {
  const files = readdirSync(dir, { withFileTypes: true })
  for (const file of files) {
    if (file.isDirectory()) {
      yield* walkSync(join(dir, file.name))
    } else {
      yield join(dir, file.name)
    }
  }
}
