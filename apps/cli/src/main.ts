#!/usr/bin/env node
import minimalist from 'minimist'
import { nanoid } from 'nanoid'
import { CommandFactory } from 'nest-commander'
import { ClsService, CLS_ID } from 'nestjs-cls'

import { createLogger } from '@phigital-loyalty/core'

import pkg from '../package.json'
import { AppModule } from './app/app.module'

process.on('SIGINT', () => {
  process.exit(0)
})

const originalEmitWarning = process.emitWarning
// eslint-disable-next-line @typescript-eslint/no-explicit-any
process.emitWarning = (warning: string | Error, ...args: any) => {
  if (typeof warning === 'string' && warning.includes('NODE_TLS_REJECT_UNAUTHORIZED')) {
    // node will only emit the warning once
    // https://github.com/nodejs/node/blob/82f89ec8c1554964f5029fab1cf0f4fad1fa55a8/lib/_tls_wrap.js#L1378-L1384
    process.emitWarning = originalEmitWarning

    return
  }

  return originalEmitWarning.call(process, warning, ...args)
}
;(async () => {
  const app = await CommandFactory.createWithoutRunning(AppModule, {
    logger: process.argv[1].endsWith('node-with-require-overrides') ? createLogger() : undefined,
    serviceErrorHandler: (err) => {
      console.error(err)
      process.exit(1)
    },
    version: pkg.version,
    completion: {
      cmd: 'DapTap CLI completion script',
      fig: true,
      nativeShell: {
        executablePath: pkg.bin.daptap,
      },
    },
  })

  app.enableShutdownHooks()

  const cls = app.get(ClsService)
  await cls.run(async () => {
    cls.set(CLS_ID, nanoid())

    cls.setIfUndefined('request', minimalist(process.argv.slice(2)))

    await CommandFactory.runApplication(app)
  })
})()
