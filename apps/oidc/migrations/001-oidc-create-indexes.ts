import type { Db } from 'mongodb'
import type { AdapterPayload } from 'oidc-provider'

module.exports = {
  up: async (db: Db) => {
    await db.collection<AdapterPayload>('oidc').createIndex(
      {
        expiresAt: 1,
      },
      { expireAfterSeconds: 0, name: 'expiresAt' },
    )

    await db.collection<AdapterPayload>('oidc').createIndex(
      {
        'payload.userCode': 1,
      },
      { name: 'userCode' },
    )

    await db.collection<AdapterPayload>('oidc').createIndex(
      {
        'payload.uid': 1,
      },
      { name: 'uid' },
    )

    await db.collection<AdapterPayload>('oidc').createIndex(
      {
        'payload.grantId': 1,
      },
      { name: 'grantId' },
    )

    return
  },

  down: async (db: Db) => {
    await db.collection<AdapterPayload>('oidc').dropIndex('expiresAt')
    await db.collection<AdapterPayload>('oidc').dropIndex('userCode')
    await db.collection<AdapterPayload>('oidc').dropIndex('uid')
    await db.collection<AdapterPayload>('oidc').dropIndex('grantId')

    return
  },
}
