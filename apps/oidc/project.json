{"name": "oidc", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/oidc/src", "projectType": "application", "prefix": "oidc", "tags": [], "generators": {}, "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"assets": ["apps/oidc/src/assets", "apps/oidc/src/views", {"glob": "**/*", "input": "dist/apps/webauthn", "output": "assets/webauthn"}], "webpackConfig": "apps/oidc/webpack.config.js"}, "dependsOn": ["webauthn"]}, "serve": {"executor": "@nx/js:node", "dependsOn": ["webauthn"]}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}, "migrate": {"executor": "@reality-connect/nx-migrate:execute"}, "migrate-status": {"executor": "@reality-connect/nx-migrate:status"}, "webauthn": {"executor": "@nx/webpack:webpack", "options": {"target": "web", "outputPath": "dist/apps/webauthn", "main": "apps/oidc/src/webauthn/main.ts", "tsConfig": "apps/oidc/tsconfig.app.json", "buildLibsFromSource": true, "maxWorkers": 1, "memoryLimit": 1024, "compiler": "tsc", "commonChunk": false, "runtimeChunk": false, "vendorChunk": false, "webpackConfig": "apps/oidc/webpack.config.js", "babelUpwardRootMode": true, "sourceMap": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false}}, "outputs": ["{options.outputPath}"]}}}