import { strictEqual } from 'assert'

import { Injectable } from '@nestjs/common'
import { verify } from 'argon2'
import { Request, Response, NextFunction } from 'express'
import { Collection } from 'mongodb'
import { Provider, errors } from 'oidc-provider'
import wildcard from 'wildcard'

import { CacheService } from '@phigital-loyalty/core'
import { InjectCollection } from '@phigital-loyalty/database/mongodb'
import { PassportService, PassportUser } from '@phigital-loyalty/passport'

import { AccountService } from './account.service'
import { renderError, renderErrorExpress } from './helpers/renderError'
import { logoutSource, postLogoutSuccessSource } from './helpers/renderLogout'
import { AdapterPayload, OidcAdapter } from './oidc.adapter'
import { internalDomains } from './oidc.clients'
import { configuration } from './oidc.config'

import type { ErrorOut, KoaContextWithOIDC } from 'oidc-provider'

const { InvalidClientMetadata } = errors

@Injectable()
export class OidcProvider {
  provider: Provider

  constructor(
    @InjectCollection('oidc')
    private readonly collection: Collection<AdapterPayload>,
    @InjectCollection('oidcClient')
    private readonly clientCollection: Collection<AdapterPayload>,
    private readonly accountService: AccountService,
    private passportService: PassportService,
    private cacheService: CacheService,
  ) {
    const adapter = OidcAdapter

    adapter.setCollections({
      default: this.collection,
      client: this.clientCollection,
    })

    adapter.setCache(this.cacheService)

    this.provider = new Provider(process.env.ISSUER, {
      adapter,
      ...configuration,
      findAccount: (_ctx: KoaContextWithOIDC, sub: string) => this.accountService.findAccount(sub),
      renderError: async (ctx: KoaContextWithOIDC, out: ErrorOut, error: errors.OIDCProviderError) => {
        const template = await this.accountService.getOrganizationTemplate(ctx.oidc?.client?.clientId)
        return renderError(ctx, out, error, template)
      },
      features: {
        ...configuration.features,
        rpInitiatedLogout: {
          ...configuration.features.rpInitiatedLogout,
          logoutSource: async (ctx: KoaContextWithOIDC, form: string) => {
            const template = await this.accountService.getOrganizationTemplate(ctx.oidc?.client?.clientId)
            return logoutSource(ctx, form, template)
          },
          postLogoutSuccessSource: async (ctx: KoaContextWithOIDC) => {
            const template = await this.accountService.getOrganizationTemplate(ctx.oidc?.client?.clientId)
            return postLogoutSuccessSource(ctx, template)
          },
        },
      },
      extraClientMetadata: {
        properties: ['redirect_uris', 'post_logout_redirect_uris'],
        validator(_ctx, _key, value: string[], _metadata) {
          for (const redirectUri of value) {
            if (redirectUri.includes('*')) {
              const { host, href } = new URL(redirectUri)

              if (href.split('*').length !== 2) {
                throw new InvalidClientMetadata('redirect_uris with a wildcard may only contain a single one')
              }

              if (!host.includes('*')) {
                throw new InvalidClientMetadata('redirect_uris may only have a wildcard in the host')
              }

              if (!internalDomains.test(host)) {
                throw new InvalidClientMetadata(
                  'redirect_uris with a wildcard must only supported for internal domains',
                )
              }
            }
          }
        },
      },
    })

    this.provider.proxy = true

    // redirectUriAllowed on a client prototype checks whether a redirect_uri is allowed or not
    const { redirectUriAllowed, postLogoutRedirectUriAllowed } = this.provider.Client.prototype

    const hasWildcardHost = (redirectUri: string) => {
      const { hostname } = new URL(redirectUri)
      return hostname.includes('*')
    }

    const wildcardMatches = (redirectUri: string, wildcardUri: string) => !!wildcard(wildcardUri, redirectUri)

    this.provider.Client.prototype.redirectUriAllowed = function (redirectUri) {
      return (
        this.redirectUris.filter(hasWildcardHost).some(wildcardMatches.bind(undefined, redirectUri)) ||
        redirectUriAllowed.call(this, redirectUri)
      )
    }

    this.provider.Client.prototype.postLogoutRedirectUriAllowed = function (redirectUri) {
      return (
        this.postLogoutRedirectUris.filter(hasWildcardHost).some(wildcardMatches.bind(undefined, redirectUri)) ||
        postLogoutRedirectUriAllowed.call(this, redirectUri)
      )
    }

    this.provider.Client.prototype.compareClientSecret = function (actual: string) {
      return verify(this.clientSecret, actual)
    }

    this.provider.use(async (ctx, next) => {
      await next()
      if (ctx.status === 200 && (ctx.oidc.route === 'introspection' || ctx.oidc.route === 'userinfo')) {
        if (
          ctx.oidc.entities.AccessToken &&
          (ctx.oidc.entities.AccessToken.scope.indexOf('roles') !== -1 || ctx.oidc.entities.Client?.clientId === 'api')
        ) {
          const token = ctx.oidc.entities.AccessToken
          if (token) {
            ctx.body.roles = await this.accountService.getAccountRoles(token.accountId)
          }
        } else if (ctx.oidc.entities.ClientCredentials) {
          const credentials = ctx.oidc.entities.ClientCredentials
          if (credentials) {
            ctx.body.roles = [
              {
                organizationId: await this.accountService.getClientOrganizationId(credentials.clientId),
                role: 'Admin',
              },
            ]
          }
        }
      }
    })

    this.passportService.callback = async (
      profile: PassportUser,
      _req: Request,
      res: Response,
      _next: NextFunction,
    ): Promise<void> => {
      const interaction = await this.provider.Interaction.find(profile.sessionID)

      if (!interaction) {
        throw new Error('Interaction not found')
      }

      try {
        strictEqual(interaction.prompt.name, 'login')

        const { accountId } = await this.accountService.findByFederated(
          profile,
          interaction.params.client_id as string,
          interaction.lastSubmission?.extraFederated as PassportUser[],
        )

        interaction.result = {
          select_account: {}, // make sure its skipped by the interaction policy since we just logged in
          two_factor_authentication: {}, // make sure its skipped by the interaction policy since we just logged in with third party provider
          login: {
            accountId,
          },
        }

        await interaction.persist()

        return res.redirect(interaction.returnTo)
      } catch (err) {
        if (err.error === 'access_denied') {
          if (
            (interaction.lastSubmission?.extraFederated as PassportUser[])?.find(
              (item) => item.provider === profile.provider && item.externalId === profile.externalId,
            )
          ) {
            return res.redirect(interaction.returnTo)
          }

          interaction.result = {
            extraFederated: [...((interaction.lastSubmission?.extraFederated as PassportUser[]) || []), profile],
          }

          await interaction.persist()

          return res.redirect(interaction.returnTo)
        }

        interaction.result = err

        await interaction.persist()

        const client = await this.provider.Client.find(interaction.params.client_id as string)
        const template = await this.accountService.getOrganizationTemplate(client.clientId)

        res.send(await renderErrorExpress(interaction, client, err, template))
      }
    }
  }
}
