import { ExecutionContext, MiddlewareConsumer, Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { APP_FILTER } from '@nestjs/core'
import { json, urlencoded } from 'body-parser'
import { CLS_ID, ClsService } from 'nestjs-cls'
import { StripeModule } from 'nestjs-stripe'

import { AccountsModule } from '@phigital-loyalty/accounts'
import {
  CoreModule,
  EnhancedRequest,
  generateFingerPrint,
  getContextParams,
  RuntimeModule,
  TypedClsStore,
} from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database/mongodb'
import { PassportModule, socialControllers } from '@phigital-loyalty/passport'

import { AccountService } from './account.service'
import { InteractionsController } from './interactions.controller'
import { OidcController } from './oidc.controller'
import { AllExceptionsFilter } from './oidc.error-filter'
import { OidcProvider } from './oidc.provider'

import type { Response } from 'express'

@Module({
  imports: [
    RuntimeModule,
    CoreModule.forRoot({
      cls: {
        middleware: {
          mount: true,
          setup: async (cls: ClsService<TypedClsStore>, req: EnhancedRequest, _res: Response) => {
            req.fingerprint = generateFingerPrint(req)

            cls.set(CLS_ID, req.fingerprint.requestId)

            cls.set('fingerprint', req.fingerprint)

            cls.set('request', {
              ...req?.params,
              query: req.query,
              body: req.body,
            })
          },
          saveReq: true,
          saveRes: false,
          useEnterWith: false,
        },
        interceptor: {
          mount: true,
          setup: async (cls: ClsService<TypedClsStore>, context: ExecutionContext) => {
            const req = context.switchToHttp().getRequest<EnhancedRequest>()

            if (!req.fingerprint) {
              req.fingerprint = generateFingerPrint(req)

              cls.set(CLS_ID, req.fingerprint.requestId)
            }

            cls.set('request', getContextParams(context))
          },
        },
      },
    }),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    StripeModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        apiKey: config.get('STRIPE_API_KEY'),
      }),
      inject: [ConfigService],
    }),
    PassportModule,
    AccountsModule.forRoot(),
    MongodbModule.forFeature(['oidc', 'oidcClient']),
  ],
  controllers: [InteractionsController, OidcController, ...socialControllers],
  providers: [
    AccountService,
    OidcProvider,
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(json(), urlencoded({ extended: true })).forRoutes(InteractionsController)
  }
}
