import type { ClientMetadata } from 'oidc-provider'

// WARNING: Do NOT add clients to this list that are not first-party clients as
// they allow you to bypass consent checks.
export const firstPartyClients: ClientMetadata[] = [
  // {
  //   grant_types: ['authorization_code'],
  //   id_token_signed_response_alg: 'RS256',
  //   authorization_signed_response_alg: 'RS256',
  //   response_types: ['code'],
  //   token_endpoint_auth_method: 'client_secret_basic',
  //   application_type: 'web',
  //   post_logout_redirect_uris: ['https://local.rc:3000/logout/cb'],
  //   require_auth_time: false,
  //   subject_type: 'public',
  //   introspection_endpoint_auth_method: 'client_secret_basic',
  //   revocation_endpoint_auth_method: 'client_secret_basic',
  //   backchannel_logout_session_required: false,
  //   frontchannel_logout_session_required: false,
  //   require_signed_request_object: false,
  //   request_uris: [],
  //   require_pushed_authorization_requests: false,
  //   tls_client_certificate_bound_access_tokens: false,
  //   client_id_issued_at: 1602787659,
  //   client_id: 'api',
  //   client_secret_expires_at: 0,
  //   client_secret: '$argon2id$v=19$m=4096,t=3,p=1$QJbrC9HsxmNscZa+kIpClg$PRT+NALNAo76zuhmgKRTouJhbyrM6H1YV1cODV+3A6g', // 'qQwTUbax5lmGW94zk80zWGsUK7QrSAaczHUyrq62PK4VsdtZHtZiAzcqIjFoHPh3Hq5c9b5VRMmIcOi8VQVaoA',
  //   redirect_uris: [
  //     'http://localhost:3876/callback',
  //     'https://local.rc:3000/callback',
  //     'https://oidc.local.rc/callback',
  //     'https://gateway.fullcube.io',
  //     'https://gateway-develop.fullcube.io',
  //     'https://gateway-stage.fullcube.io',
  //     'https://gateway-temp.fullcube.io',
  //   ],
  //   userinfo_signed_response_alg: 'RS256',
  //   registration_client_uri: 'https://local.rc:4000/reg/api',
  //   registration_access_token: '3sBRCwmgmP2bysE32RhuYWUIVFWt1zhmLg0KA5sjEV3',
  // },
]

export const internalDomains = new RegExp(
  `(${['localhost:5173', 'local.rc', 'localrc.net', 'daptapgo.io', 'daptapgo-io.pages.dev'].join('|')})$`,
)

export const firstPartyClientIds = new Set(firstPartyClients.map((client) => client.client_id))
firstPartyClientIds.add('api')
firstPartyClientIds.add('web')
firstPartyClientIds.add('android')
firstPartyClientIds.add('ios')
firstPartyClientIds.add('client_credentials')
