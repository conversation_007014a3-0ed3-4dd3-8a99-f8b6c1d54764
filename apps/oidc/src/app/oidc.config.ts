import { interactionPolicy } from 'oidc-provider'

import { USER_REQUIRED_FIELDS } from './account.service'
import { firstPartyClients } from './oidc.clients'

import type { Configuration, KoaContextWithOIDC } from 'oidc-provider'

// copies the default policy, already has login and consent prompt policies
const { Prompt, base } = interactionPolicy
const interactions = base()

// create a requestable prompt with no implicit checks
const selectAccount = new Prompt({
  name: 'select_account',
  requestable: true,
})

const twoFactorAuthentication = new Prompt(
  {
    name: 'two_factor_authentication',
    requestable: true,
  },
  new interactionPolicy.Check('need_two_factor', 'two factor authentication is required', (_ctx) => {
    // const { oidc } = ctx
    const needsTwoFactor = false
    if (!needsTwoFactor) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (interactionPolicy.Check as any).NO_NEED_TO_PROMPT
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (interactionPolicy.Check as any).REQUEST_PROMPT
  }),
)

const verify = new Prompt(
  {
    name: 'verify',
    requestable: true,
  },
  new interactionPolicy.Check('email_verified', 'email verification is required', async (ctx) => {
    const { oidc } = ctx
    const account = await oidc.provider.Account.findAccount(undefined, oidc.session.accountId)
    const { email_verified } = await account.claims('prompt', 'email_verified', { email_verified: null }, [])

    if (email_verified) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (interactionPolicy.Check as any).NO_NEED_TO_PROMPT
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (interactionPolicy.Check as any).REQUEST_PROMPT
  }),
)

const invitation = new Prompt(
  {
    name: 'invitation',
    requestable: true,
  },
  new interactionPolicy.Check('not_invited', 'invitation is required', async (ctx) => {
    const { oidc } = ctx

    if (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      !(oidc.result?.invitation as any)?.invited &&
      (oidc.params.invitation_code || process.env.ACCOUNTS_REQUIRE_INVITATION)
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (interactionPolicy.Check as any).REQUEST_PROMPT
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (interactionPolicy.Check as any).NO_NEED_TO_PROMPT
  }),
)

const requiredUserFields = new Prompt(
  {
    name: 'requiredUserFields',
    requestable: true,
  },
  new interactionPolicy.Check('missing_fields', 'missing required End-User fields', async (ctx) => {
    if (!USER_REQUIRED_FIELDS?.length) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (interactionPolicy.Check as any).NO_NEED_TO_PROMPT
    }

    const { oidc } = ctx
    const account = await oidc.provider.Account.findAccount(undefined, oidc.session.accountId)

    let missing = false
    for (const userRequiredField of USER_REQUIRED_FIELDS) {
      // Convert the camelCase field name to snake_case (DB is camelCase while OIDC is snake_case)
      const field = userRequiredField.field.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`)

      const claim = await account.claims('prompt', field, { [field]: null }, [])

      if (!claim[field]) {
        missing = true
        break
      }
    }

    if (!missing) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return (interactionPolicy.Check as any).NO_NEED_TO_PROMPT
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (interactionPolicy.Check as any).REQUEST_PROMPT
  }),
)

// add to index 0, order goes select_account > login > two_factor_authentication > verify > consent
interactions.add(selectAccount, 0)
interactions.add(twoFactorAuthentication, 2)
interactions.add(verify, 3)
interactions.add(invitation, 4)
interactions.add(requiredUserFields, 5)

export const configuration: Configuration = {
  clients: firstPartyClients,
  interactions: {
    policy: interactions,
  },
  cookies: {
    long: { signed: true, secure: true, httpOnly: true, sameSite: 'none' },
    short: { signed: true, secure: true, httpOnly: true, sameSite: 'lax' },
    keys: [
      '$yl!OHBeHkZma%7z&H6aXAU$sK504G@*@$02DL9E',
      '3wzP3D95D^%XnXVOBPKArhnysCCoQaC*0a5VYfa^',
      'J&fyets4KYpb7Jx5Q!i$3M3V4CyIll8M2eJn7ejd',
    ],
  },
  issueRefreshToken: (_ctx, client) => {
    return client.grantTypeAllowed('refresh_token')
  },
  claims: {
    address: ['address'],
    email: ['email', 'email_verified'],
    phone: ['phone_number', 'phone_number_verified'],
    profile: [
      'birthdate',
      'family_name',
      'gender',
      'given_name',
      'locale',
      'middle_name',
      'name',
      'nickname',
      'picture',
      'preferred_username',
      'profile',
      'updated_at',
      'website',
      'zoneinfo',
    ],
    roles: ['roles'],
    // organizations: ['organizations']
  },
  features: {
    devInteractions: { enabled: false }, // defaults to true

    encryption: { enabled: true },

    clientCredentials: { enabled: true }, // defaults to false

    introspection: {
      enabled: true,
      allowedPolicy: async (ctx: KoaContextWithOIDC, client, token) => {
        if (client.introspectionEndpointAuthMethod === 'none' && token.clientId !== ctx.oidc.client.clientId) {
          return false
        }
        if (client.clientId === 'api') {
          return true
        }
        return false
      },
    }, // defaults to false

    revocation: { enabled: true }, // defaults to false

    rpInitiatedLogout: {
      enabled: true,
    }, // defaults to false
  },
  jwks: {
    keys: [
      {
        d: 'VEZOsY07JTFzGTqv6cC2Y32vsfChind2I_TTuvV225_-0zrSej3XLRg8iE_u0-3GSgiGi4WImmTwmEgLo4Qp3uEcxCYbt4NMJC7fwT2i3dfRZjtZ4yJwFl0SIj8TgfQ8ptwZbFZUlcHGXZIr4nL8GXyQT0CK8wy4COfmymHrrUoyfZA154ql_OsoiupSUCRcKVvZj2JHL2KILsq_sh_l7g2dqAN8D7jYfJ58MkqlknBMa2-zi5I0-1JUOwztVNml_zGrp27UbEU60RqV3GHjoqwI6m01U7K0a8Q_SQAKYGqgepbAYOA-P4_TLl5KC4-WWBZu_rVfwgSENwWNEhw8oQ',
        dp: 'E1Y-SN4bQqX7kP-bNgZ_gEv-pixJ5F_EGocHKfS56jtzRqQdTurrk4jIVpI-ZITA88lWAHxjD-OaoJUh9Jupd_lwD5Si80PyVxOMI2xaGQiF0lbKJfD38Sh8frRpgelZVaK_gm834B6SLfxKdNsP04DsJqGKktODF_fZeaGFPH0',
        dq: 'F90JPxevQYOlAgEH0TUt1-3_hyxY6cfPRU2HQBaahyWrtCWpaOzenKZnvGFZdg-BuLVKjCchq3G_70OLE-XDP_ol0UTJmDTT-WyuJQdEMpt_WFF9yJGoeIu8yohfeLatU-67ukjghJ0s9CBzNE_LrGEV6Cup3FXywpSYZAV3iqc',
        e: 'AQAB',
        kty: 'RSA',
        n: 'xwQ72P9z9OYshiQ-ntDYaPnnfwG6u9JAdLMZ5o0dmjlcyrvwQRdoFIKPnO65Q8mh6F_LDSxjxa2Yzo_wdjhbPZLjfUJXgCzm54cClXzT5twzo7lzoAfaJlkTsoZc2HFWqmcri0BuzmTFLZx2Q7wYBm0pXHmQKF0V-C1O6NWfd4mfBhbM-I1tHYSpAMgarSm22WDMDx-WWI7TEzy2QhaBVaENW9BKaKkJklocAZCxk18WhR0fckIGiWiSM5FcU1PY2jfGsTmX505Ub7P5Dz75Ygqrutd5tFrcqyPAtPTFDk8X1InxkkUwpP3nFU5o50DGhwQolGYKPGtQ-ZtmbOfcWQ',
        p: '5wC6nY6Ev5FqcLPCqn9fC6R9KUuBej6NaAVOKW7GXiOJAq2WrileGKfMc9kIny20zW3uWkRLm-O-3Yzze1zFpxmqvsvCxZ5ERVZ6leiNXSu3tez71ZZwp0O9gys4knjrI-9w46l_vFuRtjL6XEeFfHEZFaNJpz-lcnb3w0okrbM',
        q: '3I1qeEDslZFB8iNfpKAdWtz_Wzm6-jayT_V6aIvhvMj5mnU-Xpj75zLPQSGa9wunMlOoZW9w1wDO1FVuDhwzeOJaTm-Ds0MezeC4U6nVGyyDHb4CUA3ml2tzt4yLrqGYMT7XbADSvuWYADHw79OFjEi4T3s3tJymhaBvy1ulv8M',
        qi: 'wSbXte9PcPtr788e713KHQ4waE26CzoXx-JNOgN0iqJMN6C4_XJEX-cSvCZDf4rh7xpXN6SGLVd5ibIyDJi7bbi5EQ5AXjazPbLBjRthcGXsIuZ3AtQyR0CEWNSdM7EyM5TRdyZQ9kftfz9nI03guW3iKKASETqX2vh0Z8XRjyU',
        use: 'sig',
      },
      {
        crv: 'P-256',
        x: 'x39BOQQAwUxJ1lHDVpMX1Nqk_3PYicIwc_hF_DuiIpo',
        y: '10IjBWkAhaCK74Mki77ro8uF4wrkCFxK-VIrPfV6SPk',
        d: 'fpmaSdi_xyeo7qMYzScaA__mxYGlLSDZ4lOoiVRrkAw',
        kty: 'EC',
        kid: 'D_as5bJBM7UbLr8slDJLA0ptAsfQPfJabPH5Njhp8ew',
      },
    ],
  },
  extraClientMetadata: {
    properties: ['roles'],
  },
  extraParams: ['verification_code', 'invitation_code'],
  ttl: {
    AccessToken: function AccessTokenTTL(_ctx: KoaContextWithOIDC, token, _client) {
      if (token.resourceServer) {
        return token.resourceServer.accessTokenTTL || 60 * 60 // 1 hour in seconds
      }
      return 60 * 60 // 1 hour in seconds
    },
    AuthorizationCode: 600, // 10 minutes in seconds
    ClientCredentials: function ClientCredentialsTTL(_ctx: KoaContextWithOIDC, token, _client) {
      if (token.resourceServer) {
        return token.resourceServer.accessTokenTTL || 10 * 60 // 10 minutes in seconds
      }
      return 10 * 60 // 10 minutes in seconds
    },
    DeviceCode: 600, // 10 minutes in seconds
    Grant: 1209600, // 14 days in seconds
    IdToken: 3600, // 1 hour in seconds
    Interaction: 3600, // 1 hour in seconds
    RefreshToken: function RefreshTokenTTL(ctx: KoaContextWithOIDC, token, client) {
      if (
        ctx &&
        ctx.oidc.entities.RotatedRefreshToken &&
        client.applicationType === 'web' &&
        client.tokenEndpointAuthMethod === 'none' &&
        !token.isSenderConstrained()
      ) {
        // Non-Sender Constrained SPA RefreshTokens do not have infinite expiration through rotation
        return ctx.oidc.entities.RotatedRefreshToken.remainingTTL
      }

      return 14 * 24 * 60 * 60 // 14 days in seconds
    },
    Session: 1209600, // 14 days in seconds
  },
}
