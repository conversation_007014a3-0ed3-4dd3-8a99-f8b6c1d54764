import { Injectable } from '@nestjs/common'
import { verify } from 'argon2'
import { Collection, Filter, ObjectId } from 'mongodb'
import { nanoid } from 'nanoid'
import { InjectStripe } from 'nestjs-stripe'
import { errors } from 'oidc-provider'
import Stripe from 'stripe'

import {
  FederatedUserService,
  InvitationService,
  OidcClient,
  OrganizationService,
  RoleService,
  User,
  UserService,
} from '@phigital-loyalty/accounts'
import { UserRole } from '@phigital-loyalty/core'
import { InjectCollection } from '@phigital-loyalty/database'
import { PassportUser } from '@phigital-loyalty/passport'

import type { Account, AccountClaims, ClaimsParameterMember } from 'oidc-provider'

export interface ResetToken {
  _id: string
  expiresAt: Date
  payload: {
    userId: ObjectId
    used: boolean
  }
}

export const USER_REQUIRED_FIELDS = process.env.OIDC_USER_REQUIRED_FIELDS
  ? process.env.OIDC_USER_REQUIRED_FIELDS.split(',').map((item) => {
      const [field, placeholder] = item.split(':')
      return {
        field,
        type: 'text',
        placeholder: (placeholder || field)
          .replace(/([A-Z])/g, (match) => ` ${match}`)
          .replace(/^./, (match) => match.toUpperCase())
          .trim(),
        autocomplete: field.replace(/([A-Z])/g, (match) => `-${match.toLocaleLowerCase()}`),
      }
    })
  : null

export const defaultTemplate = {
  frontEndUrl: process.env.FRONTEND_URL,
  tosUri: `${process.env.FRONTEND_URL}/terms_and_conditions`,
  policyUri: `${process.env.FRONTEND_URL}/privacy_policy`,
}

@Injectable()
export class AccountService {
  constructor(
    @InjectCollection('oidc') private readonly collection: Collection<ResetToken>,
    @InjectCollection('oidcClient') private readonly clientCollection: Collection<OidcClient>,
    private userService: UserService,
    private organizationService: OrganizationService,
    private federatedUserService: FederatedUserService,
    private invitationService: InvitationService,
    private roleService: RoleService,
    @InjectStripe() protected readonly stripe: Stripe,
  ) {}

  async findByFederated(profile: PassportUser, clientId: string, extraFederated?: PassportUser[]) {
    // TODO: Better logic. Consider existing user link. Consider updating missing user fields.

    let federatedUser = await this.federatedUserService.findOne({
      provider: profile.provider,
      externalId: profile.externalId,
    })

    if (!federatedUser && typeof profile.email === 'string') {
      let existingUser = await this.userService.findOne({ email: profile.email })

      if (!existingUser) {
        existingUser = await this.signup(
          {
            name: profile.name,
            familyName: profile.familyName,
            givenName: profile.givenName,
            middleName: profile.middleName,
            nickname: profile.nickname,
            picture: profile.picture,
            birthdate: profile.birthdate,
            email: profile.email,
            emailVerified: !!profile.email,
            phoneNumber: profile.phoneNumber,
            phoneNumberVerified: !!profile.phoneNumber,
            gender: profile.gender,
            website: profile.website,
            locale: profile.locale,
          },
          clientId,
          null,
          true,
        )
      }

      federatedUser = await this.registerNewFederatedUser(existingUser._id, profile)
    }

    if (!federatedUser) {
      throw new errors.AccessDenied('Could not find or create federated user')
    }

    let user = await this.userService.findOne({ _id: federatedUser.sub })

    if (!user) {
      user = await this.signup(
        {
          name: profile.name,
          familyName: profile.familyName,
          givenName: profile.givenName,
          middleName: profile.middleName,
          nickname: profile.nickname,
          picture: profile.picture,
          birthdate: profile.birthdate,
          email: profile.email,
          emailVerified: !!profile.email,
          phoneNumber: profile.phoneNumber,
          phoneNumberVerified: !!profile.phoneNumber,
          gender: profile.gender,
          website: profile.website,
          locale: profile.locale,
        },
        clientId,
        null,
        true,
      )
    }

    if (extraFederated && !!extraFederated.length) {
      for (const federated of extraFederated) {
        await this.registerNewFederatedUser(user._id, federated)
      }
    }

    return this.userToAccount(user)
  }

  async findAccount(accountId: string): Promise<Account> {
    const user = await this.userService.findById(new ObjectId(accountId))

    if (!user) {
      return Promise.reject(new Error('Could not find user'))
    }

    return this.userToAccount(user)
  }

  async findAccountFromEmailOrPhone(data: { email?: string; phone?: string }): Promise<Account> {
    const user = await this.userService.findOne(data.email ? { email: data.email } : { phoneNumber: data.phone })

    if (!user) {
      return Promise.reject(new Error('Could not find user'))
    }

    return this.userToAccount(user)
  }

  async authenticate(email: string, password: string, _clientId: string): Promise<Account> {
    const user = await this.userService.findOne({ email })

    if (!user) {
      return Promise.reject(new Error('Invalid Login!'))
    }

    if (await verify(user.password, password)) {
      return this.userToAccount(user)
    } else {
      return Promise.reject(new Error('Invalid Password!'))
    }
  }

  async signup(newUser: Partial<User>, clientId: string, invitationCode?: string, social = false) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    delete (newUser as any).invitationCode

    if (!social) {
      if (process.env.ACCOUNTS_REQUIRE_INVITATION) {
        await this.invitationService.checkCode(invitationCode, {
          email: newUser.email,
          phoneNumber: newUser.phoneNumber,
        })
      } else {
        newUser.invited = true
      }
    }

    for (const key in newUser) {
      if (!newUser[key]) {
        delete newUser[key]
      }
    }

    const user = await this.userService.createOne(newUser)

    // await this.createStripeCustomer(user)

    if (invitationCode) {
      await this.invitationService.consumeCode(invitationCode, user._id, true)
    }

    await this.invitationService.applyExistingInvitations(user)

    try {
      await this.userService.sendWelcomeEmail(user._id, clientId)
    } catch {
      // Do Nothing
    }
    // await this.userService.sendVerificationCodes(user._id, clientId)

    return user
  }

  async createStripeCustomer(user: User) {
    const customer = await this.stripe.customers.create(
      {
        email: user.email,
        metadata: {
          userId: user._id.toString(),
          nickname: user.nickname,
        },
        name: user.name,
        phone: user.phoneNumber,
        preferred_locales: [user.locale],
      },
      {},
    )
    await this.userService.updateOne({ _id: user._id } as never, { customerId: customer.id } as never)
  }

  async verify(accountId: string | ObjectId, code: string): Promise<boolean> {
    let user = await this.userService.findById(accountId)

    if (!user) {
      return Promise.reject(new Error('Invalid Session!'))
    }

    if (
      !(user.emailVerificationToken === code) &&
      !(user.recoveryEmailVerificationToken === code) &&
      !(user.contactEmailVerificationToken === code) &&
      !(user.additionalEmailVerificationToken === code) &&
      !(user.phoneNumberVerificationToken === code)
    ) {
      return Promise.reject(new Error('Invalid validation Code!'))
    }

    if (user.emailVerificationToken === code) {
      user = await this.userService.updateOne({ _id: user._id }, { emailVerificationToken: null, emailVerified: true })
    } else if (user.recoveryEmailVerificationToken === code) {
      user = await this.userService.updateOne(
        { _id: user._id },
        { recoveryEmailVerificationToken: null, recoveryEmailVerified: true },
      )
    } else if (user.contactEmailVerificationToken === code) {
      user = await this.userService.updateOne(
        { _id: user._id },
        { contactEmailVerificationToken: null, contactEmailVerified: true },
      )
    } else if (user.additionalEmailVerificationToken === code) {
      user = await this.userService.updateOne(
        { _id: user._id },
        { additionalEmailVerificationToken: null, additionalEmailVerified: true },
      )
    } else if (user.phoneNumberVerificationToken === code) {
      user = await this.userService.updateOne(
        { _id: user._id },
        { phoneNumberVerificationToken: null, phoneNumberVerified: true },
      )
    }

    if ((user.email && !user.emailVerified) || (user.phoneNumber && !user.phoneNumberVerified)) {
      return false
    }

    return true
  }

  async registerNewFederatedUser(accountId: string | ObjectId, profile: PassportUser) {
    return this.federatedUserService.createOne({
      provider: profile.provider,
      externalId: profile.externalId,
      sub: new ObjectId(accountId),
      ...(profile.accessToken ? { accessToken: profile.accessToken } : {}),
      ...(profile.refreshToken ? { refreshToken: profile.refreshToken } : {}),
      ...(profile.ensName ? { ensName: profile.ensName } : {}),
    })
  }

  async verifyInvitation(accountId: string | ObjectId, invitationCode: string): Promise<boolean> {
    const user = await this.userService.findOne({ _id: new ObjectId(accountId) })

    if (!user) {
      return Promise.reject(new Error('Invalid Session!'))
    }

    if (process.env.ACCOUNTS_REQUIRE_INVITATION) {
      await this.invitationService.checkCode(invitationCode, {
        _id: user._id,
        email: user.email,
        phoneNumber: user.phoneNumber,
      })
    }

    await this.userService.updateOne({ _id: user._id }, { invited: true })

    await this.invitationService.consumeCode(invitationCode, accountId, true)

    return true
  }

  async generateResetToken(emailOrPhone: string): Promise<{ user: User; token: string }> {
    const user = await this.userService.findOne({
      $or: [
        { email: emailOrPhone },
        { recoveryEmail: emailOrPhone },
        { contactEmail: emailOrPhone },
        { additionalEmail: emailOrPhone },
        { phoneNumber: emailOrPhone },
      ],
    })

    if (!user) {
      return
    }

    const token = nanoid()

    await this.collection.insertOne({
      _id: token,
      expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      payload: {
        userId: user._id,
        used: false,
      },
    })

    return { user, token }
  }

  async sendRecoverNotification(uid: string, user: User, token: string, clientId?: string): Promise<void> {
    await this.userService.sendMail(
      user,
      {
        from: '"DapTap Customer Support" <<EMAIL>>',
        subject: '[DapTap] Password Reset Request',
        template: 'reset-password',
        context: {
          clientId,
          name: this.userService.name(user),
          actionUrl: `${process.env.ISSUER}/interaction/${uid}/reset/${token}`,
          fromName: 'DapTap Customer Support',
          fromEmail: '<EMAIL>',
          supportEmail: '<EMAIL>',
        },
      },
      clientId,
    )
  }

  async resetPassword(token: string, newPassword: string) {
    const tokenInfo = await this.collection.findOne({ _id: token, 'payload.used': false })

    if (!tokenInfo) {
      throw new errors.InvalidToken('Invalid or expired token!')
    }

    await this.userService.updateOne({ _id: tokenInfo.payload.userId } as Filter<User>, {
      password: newPassword,
    })

    return this.collection.updateOne({ _id: token }, { $set: { 'payload.used': true } })
  }

  async getAccountRoles(accountId: string | ObjectId): Promise<UserRole[]> {
    return this.roleService.find(
      { userId: new ObjectId(accountId) },
      { projection: { _id: 0, organizationId: 1, role: 1 } },
    ) as unknown as UserRole[]
  }

  async checkRequiredFields(accountId: string | ObjectId) {
    if (!USER_REQUIRED_FIELDS) {
      return []
    }

    const user = await this.userService.findById(accountId)
    const missingFields = []

    for (const field of USER_REQUIRED_FIELDS) {
      // eslint-disable-next-line no-prototype-builtins
      if (!user.hasOwnProperty(field.field) || user[field.field] === null) {
        missingFields.push(field)
      }
    }

    return missingFields
  }

  async updateMissingFields(accountId: ObjectId, data: Partial<User>) {
    if (!USER_REQUIRED_FIELDS) {
      return []
    }

    const update = {}

    for (const field of USER_REQUIRED_FIELDS) {
      // eslint-disable-next-line no-prototype-builtins
      if (data.hasOwnProperty(field.field)) {
        update[field.field] = data[field.field]
      }
    }

    const user = await this.userService.updateOne({ _id: accountId }, update)

    const missingFields = []

    for (const field of USER_REQUIRED_FIELDS) {
      // eslint-disable-next-line no-prototype-builtins
      if (!user.hasOwnProperty(field.field) || user[field.field] === null) {
        missingFields.push({ field })
      }
    }

    return missingFields
  }

  async getOrganizationTemplate(clientId?: string) {
    if (!clientId) {
      return defaultTemplate
    }

    const orgId = await this.getClientOrganizationId(clientId)

    if (!orgId) {
      return defaultTemplate
    }

    const organization = await this.organizationService.findById(orgId)

    return organization
      ? {
          logoUri: organization.logo,
          ...defaultTemplate,
          ...organization.template,
        }
      : defaultTemplate
  }

  async getClientOrganizationId(clientId: string): Promise<ObjectId> {
    const query: Filter<OidcClient> = { $or: [{ client_id: clientId }] }
    if (ObjectId.isValid(clientId)) {
      query.$or.push({ clientId: new ObjectId(clientId) })
    } else {
      query.$or.push({ customDomains: clientId })
    }
    const result = await this.clientCollection.findOne(query)

    if (!result) return undefined
    return result.organizationId
  }

  private userToAccount(user: User): Account {
    return {
      accountId: user._id.toString(),
      /**
       * @param use - can either be "id_token" or "userinfo", depending on
       *   where the specific claims are intended to be put in.
       * @param scope - the intended scope, while oidc-provider will mask
       *   claims depending on the scope automatically you might want to skip
       *   loading some claims from external resources etc. based on this detail
       *   or not return them in id tokens but only userinfo and so on.
       */
      claims: async (
        _use: string,
        scope: string,

        _claims: Record<string, null | ClaimsParameterMember>,

        _rejected: string[],
      ): Promise<AccountClaims> => {
        return {
          sub: user._id.toString(), // it is essential to always return a sub claim

          address: user.address,
          birthdate: user.birthdate,
          email: user.email,
          email_verified: user.emailVerified,
          family_name: user.familyName,
          gender: user.gender,
          given_name: user.givenName,
          locale: user.locale,
          middle_name: user.middleName,
          name: this.userService.name(user),
          nickname: user.nickname,
          phone_number: user.phoneNumber,
          phone_number_verified: user.phoneNumberVerified,
          picture: user.picture,
          // preferred_username: 'johnny',
          // profile: 'https://johnswebsite.com',
          updated_at: user.updatedAt,
          website: user.website,
          zoneinfo: user.zoneinfo,

          invited: user.invited,

          ...(scope.includes('roles') ? { roles: this.getAccountRoles(user._id) } : {}),
        }
      },
    }
  }
}
