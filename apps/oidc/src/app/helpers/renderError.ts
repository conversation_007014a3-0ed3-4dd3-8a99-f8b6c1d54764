import { debug } from 'console'
import { readFileSync } from 'fs'
import { join } from 'path'

import { compile } from 'ejs'

import { defaultTemplate } from '../account.service'

import type { errors, ErrorOut, KoaContextWithOIDC } from 'oidc-provider'

const viewsPath = join(__dirname, 'views')
const renderErr = compile(readFileSync(join(viewsPath, 'error.ejs'), 'utf8'), {
  root: viewsPath,
  filename: join(viewsPath, 'error.ejs'),
})

function htmlSafe(input: number | string | boolean) {
  if (typeof input === 'number' && Number.isFinite(input)) {
    return `${input}`
  }

  if (typeof input === 'string') {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }

  if (typeof input === 'boolean') {
    return input.toString()
  }

  return ''
}

const errorCodes = {
  invalid_token: {
    en: 'Invalid Token',
  },
  invalid_scope: {
    en: 'Invalid Scope',
  },
  insufficient_scope: {
    en: 'Insufficient Scope',
  },
  invalid_request: {
    en: 'Invalid Request',
  },
  invalid_client: {
    en: 'Invalid Client',
  },
  invalid_grant: {
    en: 'Invalid Grant',
  },
  invalid_redirect_uri: {
    en: 'Invalid Redirect URI',
  },
  web_message_uri_mismatch: {
    en: 'Web Message URI Mismatch',
  },
}

function translateErrorCode(error: string, lang = 'en') {
  return errorCodes[error]?.[lang] || errorCodes[error]?.en || error
}

export async function renderError(
  ctx: KoaContextWithOIDC,
  out: ErrorOut,
  error: errors.OIDCProviderError,
  template: object = defaultTemplate,
): Promise<void | undefined> {
  if (error.error === 'invalid_request' && !ctx.session) {
    ctx.body = renderErr({
      oidcClient: ctx.oidc.client,
      template,
      clientId: ctx.oidc.client?.id,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      uid: (ctx.oidc as any).uid,
      prompt: ctx.prompt || {},
      params: ctx.params,
      title: 'Invalid Session',
      session: ctx.session ? debug(ctx.session) : undefined,
      error: 'Make sure you use the same browser instance for the whole authentication process.',
    })
  } else {
    ctx.body = renderErr({
      oidcClient: ctx.oidc.client,
      template,
      clientId: ctx.oidc.client?.id,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      uid: (ctx.oidc as any).uid,
      prompt: ctx.prompt || {},
      params: ctx.params,
      title: translateErrorCode(error.message),
      session: ctx.session ? debug(ctx.session) : undefined,
      error: Object.entries(out)
        .map(([_, value]) => `${htmlSafe(value)}<br />`)
        .join(''),
    })
  }
}

export async function renderErrorExpress(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  interaction: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  client: any,
  error: errors.OIDCProviderError,
  template: object = defaultTemplate,
): Promise<string> {
  const { uid, prompt, params, session } = interaction

  return renderErr({
    oidcClient: client,
    template,
    clientId: params?.client_id,
    uid,
    prompt: prompt || {},
    params,
    title: translateErrorCode(error.message),
    session: session ? debug(session) : undefined,
    error: error.error_description || error.message,
  })
}
