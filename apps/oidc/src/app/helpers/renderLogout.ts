import { debug } from 'console'
import { readFileSync } from 'fs'
import { join } from 'path'

import { compile } from 'ejs'

import { defaultTemplate } from '../account.service'

import type { KoaContextWithOIDC } from 'oidc-provider'

const viewsPath = join(__dirname, 'views')
const logout = compile(readFileSync(join(viewsPath, 'logout.ejs'), 'utf8'), {
  root: viewsPath,
  filename: join(viewsPath, 'logout.ejs'),
})
const logoutSuccess = compile(readFileSync(join(viewsPath, 'logout-success.ejs'), 'utf8'), {
  root: viewsPath,
  filename: join(viewsPath, 'logout-success.ejs'),
})

export async function logoutSource(ctx: KoaContextWithOIDC, form: string, template: object = defaultTemplate) {
  // @param ctx - koa request context
  // @param form - form source (id="op.logoutForm") to be embedded in the page and submitted by
  //   the End-User

  ctx.type = 'html'
  ctx.body = logout({
    oidcClient: ctx.oidc.client,
    template,
    clientId: ctx.oidc.client?.id,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    uid: (ctx.oidc as any).uid,
    prompt: ctx.prompt || {},
    params: ctx.params,
    host: ctx.host,
    form,
    title: 'Logout',
    session: ctx.session ? debug(ctx.session) : undefined,
    dbg: {
      params: debug(ctx.params),
      prompt: debug(ctx.params.prompt),
    },
  })
}

export async function postLogoutSuccessSource(ctx: KoaContextWithOIDC, template: object = defaultTemplate) {
  // @param ctx - koa request context
  const { clientId, clientName } = ctx.oidc.client || {} // client is defined if the user chose to stay logged in with the OP
  const display = clientName || clientId

  ctx.type = 'html'
  ctx.body = logoutSuccess({
    oidcClient: ctx.oidc.client,
    template,
    clientId: ctx.oidc.client?.id,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    uid: (ctx.oidc as any).uid,
    prompt: ctx.prompt || {},
    params: ctx.params,
    display,
    title: 'Logout Success',
    session: ctx.session ? debug(ctx.session) : undefined,
    dbg: {
      params: debug(ctx.params),
      prompt: debug(ctx.params.prompt),
    },
  })
}
