import { hash } from 'argon2'
import { ObjectId } from 'mongodb'

import type { CacheService } from '@phigital-loyalty/core'
import type { Collection, Filter } from 'mongodb'
import type { AdapterPayload as OidcAdapterPayload, Adapter } from 'oidc-provider'

export interface AdapterPayload extends OidcAdapterPayload {
  _id: string | ObjectId
}
export interface Collections {
  default: Collection<AdapterPayload>
  client: Collection<AdapterPayload>
}

let collections: Collections

const baseClient = {
  grant_types: ['refresh_token', 'authorization_code'],
  response_types: ['code'],
  userinfo_signed_response_alg: 'RS256',
}

let cacheService: CacheService

let defaultClientSecret: string
async function getDefaultClientSecret(): Promise<string> {
  if (defaultClientSecret === undefined && process.env.OIDC_DEFAULT_SECRET) {
    defaultClientSecret = await hash(process.env.OIDC_DEFAULT_SECRET)
  }
  return defaultClientSecret
}

function rebuildRedirectUris(uris?: string[]): string[] {
  if (!uris?.length) {
    return []
  }

  const roots = [
    ...new Set(
      uris.map((uri) => {
        const urn = new URL(uri)
        return `${urn.protocol}//${urn.host}/`
      }),
    ),
  ]

  return [...uris, ...roots]
}

export class OidcAdapter implements Adapter {
  name: string

  constructor(name: string) {
    this.name = name
  }

  // NOTE: the payload for Session model may contain client_id as keys, make sure you do not use
  //   dots (".") in your client_id value charset.
  async upsert(_id: string, payload: AdapterPayload, expiresIn: number): Promise<void> {
    let expiresAt: Date

    if (expiresIn) {
      expiresAt = new Date(Date.now() + expiresIn * 1000)
    }

    await this.getCollection().updateOne(
      { _id: ObjectId.isValid(_id) ? new ObjectId(_id) : _id },
      { $set: { payload, ...(expiresAt ? { expiresAt } : undefined) } },
      { upsert: true },
    )
  }

  async find(_id: string | ObjectId): Promise<AdapterPayload> {
    if (this.name === 'Client') {
      const key = cacheService.generateKey(this.getCollectionName(), `find${this.getCollectionName()}:${_id}`)
      const fromCache = await cacheService.getValue(key)
      if (fromCache) {
        return JSON.parse(fromCache)
      }

      const query: Filter<AdapterPayload> = { $or: [{ client_id: _id.toString() }] }
      if (ObjectId.isValid(_id)) {
        ;(query.$or as Filter<AdapterPayload>[]).push({ _id })
      } else {
        ;(query.$or as Filter<AdapterPayload>[]).push({ customDomains: _id })
      }

      const result = await this.getCollection().findOne(query)

      // Hack to make sure the client_id is always the _id (for custom domains templates)
      if (result.client_id !== _id.toString()) {
        result.client_id = _id.toString()
      }

      if (!result) return undefined

      const redirect_uris = rebuildRedirectUris(result.redirect_uris)

      const doc = {
        ...baseClient,
        ...JSON.parse(JSON.stringify(result)),
        ...(redirect_uris?.length && {
          redirect_uris,
          post_logout_redirect_uris: redirect_uris,
        }),
        token_endpoint_auth_method: result.token_endpoint_auth_method ?? 'client_secret_basic',
        client_secret: result.client_secret ?? (await getDefaultClientSecret()),
      }

      cacheService.setValue(key, JSON.stringify(doc), 15)
      return doc
    }

    const result = await this.getCollection().findOne(
      { _id: ObjectId.isValid(_id) ? new ObjectId(_id) : _id },
      { projection: { payload: 1 } },
    )

    if (!result) return undefined
    return result.payload as AdapterPayload
  }

  async findByUserCode(userCode: string): Promise<AdapterPayload> {
    const result = await this.getCollection().findOne({ 'payload.userCode': userCode }, { projection: { payload: 1 } })

    if (!result) return undefined
    return result.payload as AdapterPayload
  }

  async findByUid(uid: string): Promise<AdapterPayload> {
    const result = await this.getCollection().findOne({ 'payload.uid': uid }, { projection: { payload: 1 } })

    if (!result) return undefined
    return result.payload as AdapterPayload
  }

  async destroy(_id: string): Promise<void> {
    await this.getCollection().deleteOne({ _id: ObjectId.isValid(_id) ? new ObjectId(_id) : _id })
  }

  async revokeByGrantId(grantId: string): Promise<void> {
    await this.getCollection().deleteMany({ 'payload.grantId': grantId })
  }

  async consume(_id: string): Promise<void> {
    await this.getCollection().findOneAndUpdate(
      { _id: ObjectId.isValid(_id) ? new ObjectId(_id) : _id },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      { $set: { 'payload.consumed': Math.floor(Date.now() / 1000) } as any },
    )
  }

  getCollection(): Collection<AdapterPayload> {
    if (this.name === 'Client') {
      return collections.client
    }

    return collections.default
  }

  getCollectionName(): string {
    if (this.name === 'Client') {
      return 'oidcClient'
    }

    return 'oidc'
  }

  static setCollections(cols: Collections): void {
    collections = cols
  }

  static setCache(cache: CacheService): void {
    cacheService = cache
  }
}
