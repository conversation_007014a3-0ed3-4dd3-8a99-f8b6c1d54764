import { ServerResponse, IncomingMessage } from 'http'
import { Http2ServerRequest, Http2ServerResponse } from 'http2'

import { Controller, All, Req, Res } from '@nestjs/common'
import { Request, Response } from 'express'

import { OidcProvider } from './oidc.provider'

@Controller()
export class OidcController {
  callback: (req: IncomingMessage | Http2ServerRequest, res: ServerResponse | Http2ServerResponse) => void

  constructor(private oidcProvider: OidcProvider) {
    this.callback = this.oidcProvider.provider.callback()
  }

  @All([
    '/auth/*path',
    '/auth',
    '/device/*path',
    '/device',
    '/session/*path',
    '/session',
    '/jwks/*path',
    '/jwks',
    '/token/*path',
    '/token',
    '/me',
    '/request/*path',
    '/request',
    '/reg/*path',
    '/reg',
    '/.well-known/openid-configuration',
  ])
  mountedOidc(@Req() req: Request, @Res() res: Response): void {
    req.url = req.originalUrl
    req.baseUrl = ''
    return this.callback(req, res)
  }
}
