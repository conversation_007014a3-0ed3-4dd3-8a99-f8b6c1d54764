import { strictEqual } from 'assert'
import { debug } from 'console'

import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Get, Post, Header, Next, UseGuards, Body, Query, Param } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'
import { hash, verify } from 'argon2'
import { Request, Response, NextFunction } from 'express'
import { ObjectId } from 'mongodb'

import { User } from '@phigital-loyalty/accounts'
import { MagicLinkStrategy, PassportProvider, PassportUser } from '@phigital-loyalty/passport'

import { AccountService } from './account.service'
import { firstPartyClientIds, internalDomains } from './oidc.clients'
import { OidcProvider } from './oidc.provider'

import type { Provider, InteractionResults } from 'oidc-provider'

function isApp(clientId: string): boolean {
  return clientId.includes('ios') || clientId.includes('android')
}

const authProviders = process.env.AUTH_PROVIDERS
  ? process.env.AUTH_PROVIDERS.split(',').reduce((acc, cur) => ({ ...acc, [cur]: true }), {})
  : {}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isEmailPassword(body: any): body is { email: string; password: string } {
  return body.email && body.password
}

@Controller('interaction')
export class InteractionsController {
  provider: Provider

  constructor(
    oidcProvider: OidcProvider,
    private readonly accountService: AccountService,
    readonly magicLinkStrategy: MagicLinkStrategy,
  ) {
    this.provider = oidcProvider.provider
  }

  @Get(':uid')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async userDetails(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const { uid, prompt, params, session, lastSubmission, grantId } = await this.provider.interactionDetails(req, res)

      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      switch (prompt.name) {
        case 'select_account': {
          if (!session) {
            return this.provider.interactionFinished(
              req,
              res,
              { select_account: {} },
              { mergeWithLastSubmission: false },
            )
          }

          const account = await this.provider.Account.findAccount(undefined, session.accountId)
          const { email } = await account.claims('prompt', 'email', { email: null }, [])

          return res.render('select_account', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            email,
            prompt,
            params,
            options: {},
            title: 'Select Account',
            isApp: isApp(params.client_id as string),
            session: session ? debug(session) : undefined,
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }
        case 'login': {
          return res.render('login', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            prompt,
            params,
            options: {
              ACCOUNTS_REQUIRE_INVITATION: process.env.ACCOUNTS_REQUIRE_INVITATION || false,
            },
            lastSubmission,
            title: 'Sign in',
            isApp: isApp(params.client_id as string),
            session: session ? debug(session) : undefined,
            providers: authProviders,
            values: {},
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }
        case 'two_factor_authentication': {
          return res.render('two-factor-authentication', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            prompt,
            params,
            options: {},
            title: 'Two Factor Authentication',
            session: session ? debug(session) : undefined,
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }
        case 'verify': {
          if (params.verification_code) {
            const verified = await this.accountService.verify(session.accountId, params.verification_code as string)

            if (verified) {
              return this.provider.interactionFinished(req, res, { verify: {} }, { mergeWithLastSubmission: true })
              // return res.render('verify-success', {
              //   oidcClient: client,
              //   template,
              //   uid,
              //   prompt,
              //   params,
              //   title: 'Success',
              //   session: session ? debug(session) : undefined,
              //   dbg: {
              //     params: debug(params),
              //     prompt: debug(prompt),
              //   },
              // })
            }
          }

          return res.render('verify', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            prompt,
            params,
            options: {},
            title: 'Verify Account',
            session: session ? debug(session) : undefined,
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }
        case 'invitation': {
          if (params.invitation_code) {
            const verified = await this.accountService.verifyInvitation(
              new ObjectId(session.accountId),
              params.invitation_code as string,
            )

            if (verified) {
              return this.provider.interactionFinished(
                req,
                res,
                { invitation: { invited: true } },
                { mergeWithLastSubmission: true },
              )
            }
          }

          if (process.env.ACCOUNTS_REQUIRE_INVITATION) {
            return res.render('invitation', {
              oidcClient: client,
              template,
              clientId: params.client_id,
              uid,
              prompt,
              params,
              options: {},
              title: 'Invitation',
              session: session ? debug(session) : undefined,
              dbg: {
                params: debug(params),
                prompt: debug(prompt),
              },
            })
          }

          return this.provider.interactionFinished(
            req,
            res,
            { invitation: { invited: true } },
            { mergeWithLastSubmission: true },
          )
        }
        case 'requiredUserFields': {
          const missingFields = await this.accountService.checkRequiredFields(session.accountId)

          return res.render('required-user-fields', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            prompt: {
              ...prompt,
              details: { ...prompt.details, missingFields },
            },
            params,
            options: {},
            title: 'We need some extra information.',
            isApp: isApp(params.client_id as string),
            session: session ? debug(session) : undefined,
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }
        case 'consent': {
          const { host } = new URL(params.redirect_uri as string)

          if (firstPartyClientIds.has(params.client_id as string) || internalDomains.test(host)) {
            const grant = grantId
              ? await this.provider.Grant.find(grantId)
              : new this.provider.Grant({
                  accountId: session.accountId,
                  clientId: params.client_id as string,
                })

            if (prompt.details.missingOIDCScope) {
              grant.addOIDCScope((prompt.details.missingOIDCScope as string[]).join(' '))
              // use grant.rejectOIDCScope to reject a subset or the whole thing
            }
            if (prompt.details.missingOIDClaims) {
              grant.addOIDCClaims(prompt.details.missingOIDClaims as string[])
              // use grant.rejectOIDCClaims to reject a subset or the whole thing
            }
            if (prompt.details.missingResourceScopes) {
              for (const [indicator, scopes] of Object.entries(prompt.details.missingResourceScopes)) {
                grant.addResourceScope(indicator, (scopes as string[]).join(' '))
                // use grant.rejectResourceScope to reject a subset or the whole thing
              }
            }

            const result: InteractionResults = {}
            if (!grantId) {
              // we don't have to pass grantId to consent, we're just modifying existing one
              result.consent = { grantId: await grant.save() }
            }

            return this.provider.interactionFinished(req, res, result, { mergeWithLastSubmission: true })
          }

          return res.render('interaction', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            prompt,
            params,
            options: {},
            title: 'Authorize',
            session: session ? debug(session) : undefined,
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }
        default:
          return undefined
      }
    } catch (err) {
      return next(err)
    }
  }

  @Post(':uid/login')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async login(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { email: string; password: string } | { code: string; destination: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const { prompt, params, lastSubmission, uid } = await this.provider.interactionDetails(req, res)

      strictEqual(prompt.name, 'login')

      if (isEmailPassword(body)) {
        const result = await this.accountService.authenticate(body.email, body.password, params.client_id as string)

        await this.provider.interactionFinished(
          req,
          res,
          {
            select_account: {}, // make sure its skipped by the interaction policy since we just logged in
            login: {
              accountId: result.accountId,
            },
          },
          {
            mergeWithLastSubmission: false,
          },
        )
      } else {
        if (!params.verification_code) {
          throw new Error('Missing verification code')
        }
        if (!(await verify(params.verification_code as string, body.code))) {
          throw new Error('Invalid code')
        }

        const { accountId } = await this.accountService.findByFederated(
          {
            sessionID: uid,
            provider: PassportProvider.MagicLink,
            externalId: body.destination,

            email: body.destination,
          },
          params.client_id as string,
          lastSubmission?.extraFederated as PassportUser[],
        )

        await this.provider.interactionFinished(
          req,
          res,
          {
            select_account: {}, // make sure its skipped by the interaction policy since we just logged in
            two_factor_authentication: {}, // make sure its skipped by the interaction policy since we just logged in with third party provider
            login: {
              accountId,
            },
          },
          {
            mergeWithLastSubmission: false,
          },
        )
      }
    } catch (err) {
      if (err.name !== 'SessionNotFound') {
        const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

        const client = await this.provider.Client.find(params.client_id as string)
        const template = await this.accountService.getOrganizationTemplate(client.clientId)

        if (isEmailPassword(body)) {
          return res.render('login', {
            oidcClient: client,
            template,
            clientId: params.client_id,
            uid,
            prompt,
            params,
            options: {
              ACCOUNTS_REQUIRE_INVITATION: process.env.ACCOUNTS_REQUIRE_INVITATION || false,
            },
            title: 'Sign in',
            isApp: isApp(params.client_id as string),
            error: err.message,
            session: session ? debug(session) : undefined,
            providers: authProviders,
            values: body,
            dbg: {
              params: debug(params),
              prompt: debug(prompt),
            },
          })
        }

        return res.render('magic-link-sent', {
          destination: body.destination,
          oidcClient: client,
          template,
          clientId: params.client_id,
          uid,
          prompt,
          params,
          options: {},
          title: `Check your inbox!`,
          session: session ? debug(session) : undefined,
          dbg: {
            params: debug(params),
            prompt: debug(prompt),
          },
        })
      }
      return next(err)
    }
  }

  @Post(':uid/2fa')
  @UseGuards(AuthGuard('totp'))
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async twoFactorAuthentication(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const {
        prompt: { name },
      } = await this.provider.interactionDetails(req, res)
      strictEqual(name, 'two_factor_authentication')
      // TODO: validate that 2fa was performed
      // console.log(req)

      const result = {
        two_factor_authentication: {},
      }

      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: true,
      })
    } catch (err) {
      next(err)
    }
  }

  @Post(':uid/verify')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async verify(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { code: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const {
        prompt: { name },
        session,
      } = await this.provider.interactionDetails(req, res)
      strictEqual(name, 'verify')

      const verified = await this.accountService.verify(session.accountId, body.code)

      if (!verified) {
        next(new Error('Missing another verification!'))
      }

      const result = {
        verify: {},
      }

      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: true,
      })
    } catch (err) {
      next(err)
    }
  }

  @Post(':uid/invitation')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async invitation(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { invitationCode: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const {
        prompt: { name },
        session,
      } = await this.provider.interactionDetails(req, res)
      strictEqual(name, 'invitation')

      await this.accountService.verifyInvitation(new ObjectId(session.accountId), body.invitationCode)

      const result = {
        invitation: {},
      }

      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: true,
      })
    } catch (err) {
      next(err)
    }
  }

  @Post(':uid/continue')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async continue(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { switch: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const interaction = await this.provider.interactionDetails(req, res)
      const {
        prompt: { name },
      } = interaction
      strictEqual(name, 'select_account')

      if (body.switch) {
        if (interaction.params.prompt) {
          const prompts = new Set((interaction.params.prompt as string).split(' '))
          prompts.add('login')
          prompts.values()
          interaction.params.prompt = [...prompts].join(' ')
        } else {
          interaction.params.prompt = 'logout'
        }
        await interaction.persist()
      }

      const result = { select_account: {} }
      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: false,
      })
    } catch (err) {
      next(err)
    }
  }

  @Post(':uid/confirm')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async confirm(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const interactionDetails = await this.provider.interactionDetails(req, res)
      const {
        prompt: { name, details },
        params,
        session: { accountId },
      } = interactionDetails
      strictEqual(name, 'consent')

      let { grantId } = interactionDetails
      const grant = grantId
        ? await this.provider.Grant.find(grantId)
        : new this.provider.Grant({
            accountId,
            clientId: params.client_id as string,
          })

      if (details.missingOIDCScope) {
        grant.addOIDCScope((details.missingOIDCScope as string[]).join(' '))
        // use grant.rejectOIDCScope to reject a subset or the whole thing
      }
      if (details.missingOIDClaims) {
        grant.addOIDCClaims(details.missingOIDClaims as string[])
        // use grant.rejectOIDCClaims to reject a subset or the whole thing
      }
      if (details.missingResourceScopes) {
        for (const [indicator, scopes] of Object.entries(details.missingResourceScopes)) {
          grant.addResourceScope(indicator, (scopes as string[]).join(' '))
          // use grant.rejectResourceScope to reject a subset or the whole thing
        }
      }

      grantId = await grant.save()

      const result: InteractionResults = {}
      if (!interactionDetails.grantId) {
        // we don't have to pass grantId to consent, we're just modifying existing one
        result.consent = { grantId }
      }

      await this.provider.interactionFinished(req, res, result, { mergeWithLastSubmission: true })
    } catch (err) {
      next(err)
    }
  }

  @Get(':uid/signup')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async getSignup(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)
      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return res.render('signup', {
        oidcClient: client,
        template,
        clientId: params.client_id,
        uid,
        prompt,
        params,
        options: {
          ACCOUNTS_REQUIRE_INVITATION: process.env.ACCOUNTS_REQUIRE_INVITATION || false,
        },
        title: 'Sign up',
        isApp: isApp(params.client_id as string),
        session: session ? debug(session) : undefined,
        providers: authProviders,
        values: {},
        dbg: {
          params: debug(params),
          prompt: debug(prompt),
        },
      })
    } catch (err) {
      return next(err)
    }
  }

  @Get(':uid/recover')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async getRecover(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return res.render('recover-account', {
        oidcClient: client,
        template,
        clientId: params.client_id,
        uid,
        prompt,
        params,
        options: {},
        title: 'Forgot Password',
        session: session ? debug(session) : undefined,
        dbg: {
          params: debug(params),
          prompt: debug(prompt),
        },
      })
    } catch (err) {
      return next(err)
    }
  }

  @Get(':uid/recover-success')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async getRecoverSuccess(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return res.render('recover-account-success', {
        oidcClient: client,
        template,
        clientId: params.client_id,
        uid,
        prompt,
        params,
        options: {},
        title: 'Check your inbox',
        session: session ? debug(session) : undefined,
        dbg: {
          params: debug(params),
          prompt: debug(prompt),
        },
      })
    } catch (err) {
      return next(err)
    }
  }

  @Get(':uid/reset/:token')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async getReset(
    @Param('token') token: string,
    @Req() req: Request,
    @Res() res: Response,
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return res.render('reset-password', {
        token,
        oidcClient: client,
        template,
        clientId: params.client_id,
        uid,
        prompt,
        params,
        options: {},
        title: 'Reset Password',
        session: session ? debug(session) : undefined,
        dbg: {
          params: debug(params),
          prompt: debug(prompt),
        },
      })
    } catch (err) {
      return next(err)
    }
  }

  @Get(':uid/reset-success')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async getResetSuccess(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return res.render('reset-password-success', {
        oidcClient: client,
        template,
        clientId: params.client_id,
        uid,
        prompt,
        params,
        options: {},
        title: 'Password Reset Success',
        session: session ? debug(session) : undefined,
        close: params,
        dbg: {
          params: debug(params),
          prompt: debug(prompt),
        },
      })
    } catch (err) {
      return next(err)
    }
  }

  @Get(':uid/loginMagicLink')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async renderLoginMagicLink(
    @Req() req: Request,
    @Res() res: Response,
    @Query() query: { email?: string; phone?: string },
  ): Promise<void> {
    const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

    const client = await this.provider.Client.find(params.client_id as string)
    const template = await this.accountService.getOrganizationTemplate(client.clientId)

    return res.render('magic-link-sent', {
      destination: query.email ?? query.phone,
      oidcClient: client,
      template,
      clientId: params.client_id,
      uid,
      prompt,
      params,
      options: {},
      title: `Check your ${!query.email ? 'phone' : 'inbox'}!`,
      session: session ? debug(session) : undefined,
      dbg: {
        params: debug(params),
        prompt: debug(prompt),
      },
    })
  }

  @Post(':uid/loginMagicLink')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async loginMagicLink(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { email?: string; phone?: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      if (!body.email && !body.phone) {
        throw new Error('Missing email or phone')
      }

      const interaction = await this.provider.interactionDetails(req, res)
      const { uid, params } = interaction

      const client = await this.provider.Client.find(params.client_id as string)

      const currentUser = await this.accountService.findAccountFromEmailOrPhone(body).catch(() => null)

      const type = !body.email ? 'SMS' : 'Email'

      const verificationCode = Math.floor(Math.random() * 900000) + 100000 + ''

      interaction.params.verification_code = await hash(verificationCode)

      if (currentUser) {
        interaction.params.accountId = currentUser.accountId
      }

      await interaction.persist()

      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      await this.magicLinkStrategy.send(req, res, type, verificationCode, template)

      return res.redirect(`/interaction/${uid}/loginMagicLink?${new URLSearchParams({ ...body }).toString()}`)
    } catch (err) {
      if (err.name !== 'SessionNotFound') {
        const { uid, prompt, params, session, lastSubmission } = await this.provider.interactionDetails(req, res)

        const client = await this.provider.Client.find(params.client_id as string)
        const template = await this.accountService.getOrganizationTemplate(client.clientId)

        return res.render('login', {
          oidcClient: client,
          template,
          clientId: params.client_id,
          uid,
          prompt,
          params,
          options: {
            ACCOUNTS_REQUIRE_INVITATION: process.env.ACCOUNTS_REQUIRE_INVITATION || false,
          },
          lastSubmission,
          error: err.message,
          title: 'Sign in',
          isApp: isApp(params.client_id as string),
          session: session ? debug(session) : undefined,
          providers: authProviders,
          values: {},
          dbg: {
            params: debug(params),
            prompt: debug(prompt),
          },
        })
      }

      next(err)
    }
  }

  @Post(':uid/signup')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async signup(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: Partial<User> & { invitationCode: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const {
        params,
        prompt: { name },
        lastSubmission,
      } = await this.provider.interactionDetails(req, res)
      strictEqual(name, 'login')

      const account = await this.accountService.signup(
        {
          nickname: body.nickname,
          name: `${body.givenName} ${body.familyName}`,
          givenName: body.givenName,
          familyName: body.familyName,
          email: body.email,
          password: body.password,
        },
        params.client_id as string,
        body.invitationCode,
      )

      if ((lastSubmission?.extraFederated as PassportUser[])?.length) {
        for (const federated of lastSubmission.extraFederated as PassportUser[]) {
          await this.accountService.registerNewFederatedUser(account._id, federated)
        }
      }

      const result = {
        select_account: {}, // make sure its skipped by the interaction policy since we just logged in
        login: {
          accountId: account._id.toString(),
        },
      }

      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: false,
      })
    } catch (err) {
      if (err.name !== 'SessionNotFound') {
        const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

        const client = await this.provider.Client.find(params.client_id as string)
        const template = await this.accountService.getOrganizationTemplate(client.clientId)

        return res.render('signup', {
          oidcClient: client,
          template,
          clientId: params.client_id,
          uid,
          prompt,
          params,
          options: {
            ACCOUNTS_REQUIRE_INVITATION: process.env.ACCOUNTS_REQUIRE_INVITATION || false,
          },
          title: 'Sign up',
          isApp: isApp(params.client_id as string),
          error: err.message,
          session: session ? debug(session) : undefined,
          providers: authProviders,
          values: body,
          dbg: {
            params: debug(params),
            prompt: debug(prompt),
          },
        })
      }

      next(err)
    }
  }

  @Post(':uid/missingFields')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async missingFields(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: Partial<User>,
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const {
        prompt: { name },
        session,
      } = await this.provider.interactionDetails(req, res)
      strictEqual(name, 'requiredUserFields')

      const missingFields = await this.accountService.updateMissingFields(new ObjectId(session.accountId), body)

      if (!missingFields.length) {
        return this.provider.interactionFinished(
          req,
          res,
          { requiredUserFields: {} },
          { mergeWithLastSubmission: true },
        )
      }

      return this.provider.interactionFinished(req, res, {}, { mergeWithLastSubmission: true })
    } catch (err) {
      return next(err)
    }
  }

  @Post(':uid/recover')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async recover(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { email: string },
    @Next() next: NextFunction,
  ): Promise<void> {
    try {
      const {
        uid,
        params,
        prompt: { name },
      } = await this.provider.interactionDetails(req, res)
      strictEqual(name, 'login')

      const result = await this.accountService.generateResetToken(body.email)

      if (!result?.token) {
        return res.redirect(`/interaction/${uid}/recover-success`)
      }

      const client = await this.provider.Client.find(params.client_id as string)
      await this.accountService.sendRecoverNotification(uid, result.user, result.token, client.clientId)

      res.redirect(`/interaction/${uid}/recover-success`)
    } catch (err) {
      next(err)
    }
  }

  @Post(':uid/reset/:token')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async reset(
    @Param('uid') uid: string,
    @Param('token') token: string,
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: { password: string },
  ): Promise<void> {
    try {
      await this.accountService.resetPassword(token, body.password)

      return res.redirect(`/interaction/${uid}/reset-success`)
    } catch (err) {
      const { uid, prompt, params, session } = await this.provider.interactionDetails(req, res)

      const client = await this.provider.Client.find(params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return res.render('reset-password', {
        token,
        oidcClient: client,
        template,
        clientId: params.client_id,
        uid,
        prompt,
        params,
        options: {},
        error: err.message,
        title: 'Reset Password',
        session: session ? debug(session) : undefined,
        dbg: {
          params: debug(params),
          prompt: debug(prompt),
        },
      })
    }
  }

  @Get(':uid/abort')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async abort(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const result = {
        error: 'access_denied',
        error_description: 'End-User aborted interaction',
      }
      await this.provider.interactionFinished(req, res, result, {
        mergeWithLastSubmission: false,
      })
    } catch (err) {
      next(err)
    }
  }

  @Get(':uid/close')
  @Header('Pragma', 'no-cache')
  @Header('Cache-Control', 'no-cache, no-store')
  async close(@Req() req: Request, @Res() res: Response, @Next() next: NextFunction): Promise<void> {
    try {
      const { uid } = await this.provider.interactionDetails(req, res)
      // res.redirect(params.redirect_uri as string)
      return res.redirect(`/interaction/${uid}`)
    } catch (err) {
      next(err)
    }
  }
}
