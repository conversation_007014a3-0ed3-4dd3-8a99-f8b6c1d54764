import { ExceptionFilter, Catch, HttpException, ArgumentsHost, HttpStatus } from '@nestjs/common'

import { AccountService } from './account.service'
import { renderErrorExpress } from './helpers/renderError'
import { OidcProvider } from './oidc.provider'

import type { Provider } from 'oidc-provider'

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  provider: Provider

  constructor(
    oidcProvider: OidcProvider,
    readonly accountService: AccountService,
  ) {
    this.provider = oidcProvider.provider
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async catch(error: any, host: ArgumentsHost) {
    console.error(error)
    const ctx = host.switchToHttp()
    const response = ctx.getResponse()
    const request = ctx.getRequest()
    const status = error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR

    try {
      const interaction = await this.provider.interactionDetails(request, response)
      const client = await this.provider.Client.find(interaction.params.client_id as string)
      const template = await this.accountService.getOrganizationTemplate(client.clientId)

      return response.status(status).send(await renderErrorExpress(interaction, client, error, template))
    } catch (error) {
      return response.status(status).send(await renderErrorExpress({}, {}, error))
    }
  }
}
