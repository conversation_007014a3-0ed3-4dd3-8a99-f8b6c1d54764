:root {
  --default-background-color: linear-gradient(180deg, rgba(122, 172, 210, 1) 0%, rgba(29, 49, 82, 1) 100%);

  --default-text-color: rgb(255, 255, 255);

  --default-primary-color: #5c86ac;
  --default-primary-text-color: rgb(255, 255, 255);

  --default-secondary-color: #163154;
  --default-secondary-text-color: rgb(255, 255, 255);

  --default-tertiary-color: #7badd3;
  --default-tertiary-text-color: rgb(255, 255, 255);
}

html,
body {
  min-height: 100dvh;
}

body {
  background: var(--background-color, var(--default-background-color, rgb(48, 48, 48)));
  background-size: cover;
  color: var(--text-color, var(--default-text-color, rgb(255, 255, 255)));
}

.btn {
  display: block;
  font-size: 1rem;
  line-height: 1.5rem;
  white-space: nowrap;
  text-align: center;
}

.btn > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.btn.variant-primary {
  background: var(--primary-color, var(--default-primary-color, #5c86ac));
  color: var(--primary-text-color, var(--default-primary-text-color, rgb(255, 255, 255)));
}
.btn.variant-secondary {
  background: var(--secondary-color, var(--default-secondary-color, #163154));
  color: var(--secondary-text-color, var(--default-secondary-text-color, rgb(255, 255, 255)));
}
.btn.variant-tertiary {
  background: var(--tertiary-color, var(--default-tertiary-color, #7badd3));
  color: var(--tertiary-text-color, var(--default-tertiary-text-color, rgb(255, 255, 255)));
}

.btn.variant-ghost-primary {
  border-width: 1px;
  border-color: var(--primary-color, var(--default-primary-color, #5c86ac));
  color: var(--primary-color, var(--default-primary-color, #5c86ac));
}
.btn.variant-ghost-secondary {
  border-width: 1px;
  border-color: var(--secondary-color, var(--default-secondary-color, #163154));
  color: var(--secondary-color, var(--default-secondary-color, #163154));
}
.btn.variant-ghost-tertiary {
  border-width: 1px;
  border-color: var(--tertiary-color, var(--default-tertiary-color, #7badd3));
  color: var(--tertiary-color, var(--default-tertiary-color, #7badd3));
}
.btn.variant-anchor {
  background: none;
  color: var(--tertiary-color, var(--default-tertiary-color, #163154));
  text-decoration: underline;
  padding: 0;
}

btn:hover,
btn:active {
  opacity: 0.8;
}

.anchor {
  text-decoration: underline;
}

.anchor:hover,
.anchor:active {
  opacity: 0.8;
}

.text-primary {
  color: var(--primary-color, var(--default-primary-color, #5c86ac));
}
.text-secondary {
  color: var(--secondary-color, var(--default-secondary-color, #163154));
}
.text-tertiary {
  color: var(--tertiary-color, var(--default-tertiary-color, #7badd3));
}

form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

form > div {
  display: flex;
  width: 100%;
  justify-content: space-between;
  flex-wrap: wrap;
}

/* .checkboxes {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  min-width: fit-content;
  margin-top: 3rem;
}

.checkboxes > div {
  margin-bottom: 0.5rem;
  text-align: left;
  width: 100%;
}

.checkboxes > div:last-child {
  margin-bottom: 0;
}

.checkboxes input {
  accent-color: var(--primary-color, var(--default-primary-color, #5c86ac));
  height: 2.2rem;
  width: 2rem;
  margin-right: 1.4rem;
}

.checkboxes label {
  vertical-align: super;
}

.checkboxes label > a {
  text-decoration: underline;
} */

main {
  color: var(--text-color, var(--default-text-color, rgb(255, 255, 255)));
}

main h1 span {
  color: var(--primary-color, var(--default-primary-color, #5c86ac));
}

.social-button {
  width: 40%;
  margin-left: auto;
  margin-right: auto;
  background-color: var(--primary-color, var(--default-primary-color, #5c86ac));
  font-size: 1.4rem;
}

.apple {
  background-color: black !important;
}
.discord {
  background-color: #7289da !important;
}
.facebook {
  background-color: #5890ff !important;
}
.github {
  background-color: black !important;
}
.google {
  background-color: #4285f4 !important;
}
.stripe {
  background-color: #5433ff !important;
}
.twitch {
  background-color: rgb(145, 71, 255) !important;
}

button[name='submit']:disabled::after {
  content: '';
  display: block;
  width: 1.25em;
  height: 1.25em;
  position: relative;
  left: calc(50% - 0.75em);
  /* top: calc(50% - 0.75em); */
  margin-top: -1.25em;
  border: 0.2em solid transparent;
  border-right-color: var(--primary-text-color, var(--default-primary-text-color, rgb(255, 255, 255)));
  border-radius: 50%;
  animation: loader-animation 0.7s linear infinite;
  opacity: 0;
}

@keyframes loader-animation {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

button[name='submit']:disabled {
  color: transparent;
}

button[name='submit']:disabled::after {
  opacity: 1;
}

input#pin-code,
input#pin-code:focus {
  --otp-digits: 6;
  --otp-ls: 2ch;
  --otp-gap: 1.25;

  /* private consts */
  --_otp-bgsz: calc(var(--otp-ls) + 1ch);
  --_otp-digit: 0;

  all: unset;
  background:
    linear-gradient(90deg, var(--otp-bg, #ccc) calc(var(--otp-gap) * var(--otp-ls)), transparent 0),
    linear-gradient(90deg, var(--otp-bg, #fff) calc(var(--otp-gap) * var(--otp-ls)), transparent 0);
  background-position:
    calc(var(--_otp-digit) * var(--_otp-bgsz)) 0,
    0 0;
  background-repeat: no-repeat, repeat-x;
  background-size: var(--_otp-bgsz) 100%;
  color: var(--otp-cc, #000);
  caret-color: var(--otp-cc, #000);
  caret-shape: block;
  clip-path: inset(0% calc(var(--otp-ls) / 2) 0% 0%);
  font-family: ui-monospace, monospace;
  font-size: var(--otp-fz, 2em);
  inline-size: calc(var(--otp-digits) * var(--_otp-bgsz));
  letter-spacing: var(--otp-ls);
  padding-block: var(--otp-pb, 0.8rem);
  padding-inline-start: calc(((var(--otp-ls) - 1ch) / 2) * var(--otp-gap));
}
