import { join } from 'path'

import expressEjsLayouts from 'express-ejs-layouts'

import { bootstrap } from '@phigital-loyalty/core'

import { AppModule } from './app/app.module'

bootstrap(AppModule, { bodyParser: false }).then(({ app }) => {
  app.useStaticAssets(join(__dirname, 'assets'))
  app.setBaseViewsDir(join(__dirname, 'views'))
  // app.setViewEngine('hbs')
  app.use(expressEjsLayouts)
  app.setViewEngine('ejs')
})
