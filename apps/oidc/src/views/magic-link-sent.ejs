<form id="resend" autocomplete="off" action="/interaction/<%= uid %>/loginMagicLink?uid=<%= uid %>&clientId=<%= clientId %>" method="post" onsubmit="submit.disabled = true; return true;">
  <input type="hidden" name="email" value="<%= destination %>">
</form>

<form class="grow flex flex-col justify-between" action="/interaction/<%= uid %>/login" method="post" onsubmit="submit.disabled = true; return true;">
  <input type="hidden" name="destination" value="<%= destination %>">

  <div class="space-y-6 px-1">
    <p>We've sent a magic link to <%= destination %>.</p>
    <p>Visit the link to complete your login.</p>
    <p>Or enter the code below.</p>
  </div>

  <div class="mt-8 space-y-6">
    <div class="mx-auto">
      <input id="pin-code" type="text" name="code" required autocomplete="one-time-code" autofocus="on" inputmode="numeric" maxlength="6" pattern="\d{6}"/>
    </div>
  </div>

  <div class="mt-8 space-y-6">
    <%- include('components/button', {
      type: "submit",
      name: "submit",
      label: "Submit Code",
      class: "variant-primary",
    }); %>

    <%- include('components/button', {
      type: "submit",
      label: "Resend Code",
      form: "resend",
      class: "w-full variant-anchor !text-base",
    }); %>
  </div>
</form>

<script>
  const input = document.querySelector('input[name="code"]')
  input.addEventListener('input', () => {
    input.style.setProperty('--_otp-digit', input.selectionStart)

    if (input.value.length === 6) {
      input.blur()
      // input.form.submit.focus()
      input.form.requestSubmit()
    }
  })
</script>
