

<h1><strong><%= oidcClient.clientName || oidcClient.clientId %></strong> would like access to your:</h1>

<ul>
  <% if ([prompt.details.missingOIDCScope, prompt.details.missingOIDClaims, prompt.details.missingResourceScopes].filter(Boolean).length === 0) { %>
    <li>the client is asking you to confirm previously given authorization</li>
  <% } %>

  <% missingOIDCScope = new Set(prompt.details.missingOIDCScope); missingOIDCScope.delete('openid'); missingOIDCScope.delete('offline_access') %>
  <% if (missingOIDCScope.size) { %>
    <!-- <li class="first-letter:uppercase">scopes:</li> -->
    <ul>
      <% missingOIDCScope.forEach((scope) => { %>
        <li class="first-letter:uppercase"><%= scope %></li>
      <% }) %>
    </ul>
  <% } %>

  <% missingOIDClaims = new Set(prompt.details.missingOIDClaims); ['sub', 'sid', 'auth_time', 'acr', 'amr', 'iss'].forEach(Set.prototype.delete.bind(missingOIDClaims)) %>
  <% if (missingOIDClaims.size) { %>
    <!-- <li class="first-letter:uppercase">claims:</li> -->
    <ul>
      <% missingOIDClaims.forEach((claim) => { %>
        <li class="first-letter:uppercase"><%= claim %></li>
      <% }) %>
    </ul>
  <% } %>

  <% missingResourceScopes = prompt.details.missingResourceScopes %>
  <% if (missingResourceScopes) { %>
    <% for (const [indicator, scopes] of Object.entries(prompt.details.missingResourceScopes)) { %>
      <!-- <li class="first-letter:uppercase"><%= indicator %>:</li> -->
      <ul>
        <% scopes.forEach((scope) => { %>
          <li class="first-letter:uppercase"><%= scope %></li>
        <% }) %>
      </ul>
    <% } %>
  <% } %>

  <% if (params.scope && params.scope.includes('offline_access')) { %>
    <li>
    the client is asking to have offline access to this authorization
      <% if ((!prompt.details.missingOIDCScope) || !prompt.details.missingOIDCScope.includes('offline_access')) { %>
        (which you've previously granted)
      <% } %>
    </li>
  <% } %>
</ul>

<br />
<br />

<div class="mt-8 space-y-6">
  <form class="space-y-6" autocomplete="off" action="/interaction/<%= uid %>/confirm" method="post" onsubmit="submit.disabled = true; return true;">
    <%- include('components/button', {
      type: "submit",
      name: "submit",
      label: "Agree and Continue",
      class: "w-full variant-primary",
      autofocus: true,
    }); %>
  </form>

  <a class="btn w-full rounded-full text-xl px-4 py-2 variant-secondary " href="/interaction/<%= uid %>/abort">Cancel</a>
</div>
