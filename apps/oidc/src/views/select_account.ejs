<form class="space-y-6" autocomplete="off" action="/interaction/<%= uid %>/continue" method="post" onsubmit="submit.disabled = true; return true;">
  <%- include('components/button', {
    type: "submit",
    name: "submit",
    label: "Continue as " + email,
    class: "variant-primary",
    autofocus: true,
  }); %>
</form>

<form class="space-y-6" autocomplete="off" action="/interaction/<%= uid %>/continue" method="post" onsubmit="submit.disabled = true; return true;">
  <input type="hidden" name="switch" value="true">

  <%- include('components/button', {
    type: "submit",
    name: "submit",
    label: "Switch account",
    class: "variant-primary",
  }); %>
</form>
