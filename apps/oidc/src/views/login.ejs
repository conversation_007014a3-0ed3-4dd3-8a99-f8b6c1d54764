<% if (!providers || providers.password) { %>
<form class="grow flex flex-col justify-between" autocomplete="off" action="/interaction/<%= uid %>/login" method="post" onsubmit="submit.disabled = true; return true;">
<% } else if (providers && (providers.email || providers.sms)) { %>
  <form class="grow flex flex-col justify-between" autocomplete="off" action="/interaction/<%= uid %>/loginMagicLink?uid=<%= uid %>&clientId=<%= clientId %>" method="post" onsubmit="submit.disabled = true; return true;">
<% } %>

  <div class="space-y-6">
    <% if (typeof lastSubmission !== 'undefined' && typeof lastSubmission.extraFederated !== 'undefined' && !!lastSubmission.extraFederated.length) { %>
      <div class="py-2 pl-4 mb-2 leading-normal text-yellow-700 bg-yellow-100 rounded-lg" role="alert">
        <p class="leading-normal text-sm"><strong>No user was found for this account.</strong></p>
        <p class="leading-normal text-xs">Login with an existing user to link your account.</p>
      </div>
    <% } %>

    <% if (providers) { %>
      <% if (providers.apple) { %>
        <%- include('components/sso-button', {
          icon: '/logos/apple.png',
          provider: 'apple',
          label: 'Apple',
        }); %>
      <% } %>

      <% if (providers.discord) { %>
        <%- include('components/sso-button', {
          icon: '/logos/discord.png',
          provider: 'discord',
          label: 'Discord',
        }); %>
      <% } %>

      <% if (providers.facebook) { %>
        <%- include('components/sso-button', {
          icon: '/logos/facebook.png',
          provider: 'facebook',
          label: 'Facebook',
        }); %>
      <% } %>

      <% if (providers.github) { %>
        <%- include('components/sso-button', {
          icon: '/logos/github.png',
          provider: 'github',
          label: 'GitHub',
        }); %>
      <% } %>
      <% if (providers.google) { %>
        <%- include('components/sso-button', {
          icon: '/logos/google.png',
          provider: 'google',
          label: 'Google',
        }); %>
      <% } %>

      <% if (providers.siwe && (typeof lastSubmission === 'undefined' || typeof lastSubmission.extraFederated === 'undefined' || !lastSubmission.extraFederated.length || !lastSubmission.extraFederated.find(item => item.provider === 'siwe'))) { %>
        <%- include('components/sso-button', {
          icon: '/logos/ethereum.png',
          provider: 'siwe',
          label: 'Ethereum',
        }); %>
        <script>
          global = globalThis
        </script>
        <script type="text/javascript" async src="/siwe/main.js"></script>
      <% } %>

      <% if (providers.stripe) { %>
        <%- include('components/sso-button', {
          icon: '/logos/stripe.png',
          provider: 'stripe',
          label: 'Stripe',
        }); %>
      <% } %>

      <% if (providers.twitch) { %>
        <%- include('components/sso-button', {
          icon: '/logos/twitch.png',
          provider: 'twitch',
          label: 'Twitch',
        }); %>
      <% } %>
    <% } %>

    <%- include('components/error'); %>
  </div>

  <div class="space-y-6">
    <% if (!providers || providers.password || providers.email) { %>
      <%- include('components/input', {
        required: !providers.sms,
        type: "email",
        name: "email",
        placeholder: "Email Address",
        autocomplete: "webauthn email",
        autofocus: !values.email,
        value: values.email,
      }); %>
    <% } %>

    <% if (providers && providers.sms) { %>
      <%- include('components/input', {
        required: !providers.email,
        type: "phone",
        name: "phoneNumber",
        placeholder: "Phone Number",
        autocomplete: "webauthn phone",
        autofocus: !values.phoneNumber,
        value: values.phoneNumber,
      }); %>
    <% } %>

    <% if (!providers || providers.password) { %>
      <%- include('components/input', {
        required: true,
        type: "password",
        name: "password",
        placeholder: "Password",
        autocomplete: "webauthn current-password",
        autofocus: values.email,
      }); %>
    <% } %>

    <% if (providers && (providers.email || providers.sms)) { %>
      <%- include('components/terms') %>
    <% } %>
  </div>

  <div class="mt-8 space-y-6">
    <%- include('components/button', {
      type: "submit",
      name: "submit",
      label: providers && (providers.email || providers.sms) ? "Submit" : "Login",
      class: "variant-primary py-3",
    }); %>

    <% if (!providers || providers.password) { %>
    <div class="w-full text-center">
      Don't have an account? <a class="anchor text-secondary" href="/interaction/<%= uid %>/signup">Create one</a>
    </div>
    <% } %>

    <% if (isApp) { %>
      <%- include('components/anchor', {
        href: "/interaction/" + uid + "/abort",
        label: "Return to App",
        class: "variant-tertiary",
      }); %>
    <% } %>

    <% if (!providers || providers.password) { %>
    <div class="w-full text-center">
      Can't remember your password? <a class="anchor" href="/interaction/<%= uid %>/recover">Recover it</a>
    </div>
    <% } %>
  </div>
</form>

<script type="text/javascript" async src="/webauthn/main.js"></script>
