<form class="grow flex flex-col justify-between" autocomplete="off" action="/interaction/<%= uid %>/missingFields" method="post" onsubmit="submit.disabled = true; return true;">
  <div class="space-y-6">
    <%- include('components/error'); %>
  </div>

  <div class="space-y-6">
    <% prompt.details.missingFields.forEach((field) => { %>
      <%- include('components/input', {
        required: true,
        type: field.type || 'text',
        name: field.field,
        placeholder: field.placeholder,
        autocomplete: field.autocomplete,
      }); %>
    <% }) %>
  </div>

  <div class="mt-8 space-y-6">
    <%- include('components/button', {
      type: "submit",
      name: "submit",
      label: "Submit",
      class: "variant-primary",
    }); %>

    <% if (isApp) { %>
      <%- include('components/anchor', {
        href: "/interaction/" + uid + "/abort",
        label: "Return to App",
        class: "variant-tertiary",
      }); %>
    <% } %>
  </div>
</form>