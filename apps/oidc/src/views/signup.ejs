<% if (!providers || providers.password) { %>
<form class="grow flex flex-col justify-between" autocomplete="off" action="/interaction/<%= uid %>/signup" method="post" onsubmit="submit.disabled = true; return true;">
<% } else if (providers && (providers.email || providers.sms)) { %>
  <form class="grow flex flex-col justify-between" autocomplete="off" action="/interaction/<%= uid %>/loginMagicLink?uid=<%= uid %>&clientId=<%= clientId %>" method="post" onsubmit="submit.disabled = true; return true;">
<% } %>

  <div class="space-y-6">
    <% if (providers) { %>
      <% if (providers.apple) { %>
        <%- include('components/sso-button', {
          icon: '/logos/apple.png',
          provider: 'apple',
          label: 'Apple',
        }); %>
      <% } %>

      <% if (providers.discord) { %>
        <%- include('components/sso-button', {
          icon: '/logos/discord.png',
          provider: 'discord',
          label: 'Discord',
        }); %>
      <% } %>

      <% if (providers.facebook) { %>
        <%- include('components/sso-button', {
          icon: '/logos/facebook.png',
          provider: 'facebook',
          label: 'Facebook',
        }); %>
      <% } %>

      <% if (providers.github) { %>
        <%- include('components/sso-button', {
          icon: '/logos/github.png',
          provider: 'github',
          label: 'GitHub',
        }); %>
      <% } %>

      <% if (providers.google) { %>
        <%- include('components/sso-button', {
          icon: '/logos/google.png',
          provider: 'google',
          label: 'Google',
        }); %>
      <% } %>

      <% if (providers.stripe) { %>
        <%- include('components/sso-button', {
          icon: '/logos/stripe.png',
          provider: 'stripe',
          label: 'Stripe',
        }); %>
      <% } %>

      <% if (providers.twitch) { %>
        <%- include('components/sso-button', {
          icon: '/logos/twitch.png',
          provider: 'twitch',
          label: 'Twitch',
        }); %>
      <% } %>
    <% } %>

    <%- include('components/error'); %>
  </div>

  <div class="space-y-6">
    <% if (!providers || providers.password || providers.email) { %>
      <%- include('components/input', {
        required: true,
        type: "email",
        name: "email",
        placeholder: "Email Address",
        autocomplete: "email",
        value: values.email,
      }); %>
    <% } %>

    <% if (providers && providers.sms) { %>
      <%- include('components/input', {
        required: true,
        type: "phone",
        name: "phoneNumber",
        placeholder: "Phone Number",
        autocomplete: "phone",
        value: values.phoneNumber,
      }); %>
    <% } %>

    <% if (!providers || providers.password) { %>
      <%- include('components/input', {
        required: true,
        type: "text",
        name: "nickname",
        placeholder: "Username",
        autocomplete: "nickname",
        value: values.values,
      }); %>

      <%- include('components/input', {
        required: true,
        type: "password",
        name: "password",
        placeholder: "Password",
        autocomplete: "new-password",
      }); %>
    <% } %>

    <% if (options.ACCOUNTS_REQUIRE_INVITATION) { %>
      <%- include('components/input', {
        type: "text",
        name: "invitationCode",
        placeholder: "Invitation Code",
        value: values.invitationCode,
      }); %>
    <% } %>

    <!-- <div class="checkboxes">
      <% if (oidcClient.policyUri || oidcClient.tosUri) { %>
      <div>
        <input required type="checkbox" id="tos" name="tos" />
        <label for="tos">I have read the <a target="_blank" href="<%= oidcClient.policyUri %>">Privacy Policy</a> and <a target="_blank" href="<%= oidcClient.tosUri %>">Terms of Service</a></label>
      </div>
      <% } %>

      <% if (template.ageLimit) { %>
      <div>
        <input required type="checkbox" id="age" name="age" />
        <label for="age">I am over <%= template.ageLimit %> years of age</label>
      </div>
      <% } %>

      <% if (oidcClient.clientName) { %>
      <div>
        <input type="checkbox" id="marketing" name="marketing" />
        <label for="marketing">Yes, send me info about <%= oidcClient.clientName %></label>
      </div>
      <% } %>
    </div> -->

    <%- include('components/terms') %>
  </div>

  <div class="mt-8 space-y-6">
    <%- include('components/button', {
      type: "submit",
      name: "submit",
      label: "Register",
      class: "variant-primary",
    }); %>

    <div class="w-full text-center">
    Already have an account? <a class="anchor text-secondary" href="/interaction/<%= uid %>">Log in</a>
    </div>

    <% if (isApp) { %>
      <%- include('components/anchor', {
        href: "/interaction/" + uid + "/abort",
        label: "Return to App",
        class: "variant-tertiary",
      }); %>
    <% } %>
  </div>
</form>
