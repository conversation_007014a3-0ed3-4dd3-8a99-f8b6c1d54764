<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title><%= title %></title>
    <link rel="stylesheet" href="/styles.css" />

    <link rel="apple-touch-icon" sizes="256x256" href="/images/icon.png" />
    <link rel="icon" type="image/png" sizes="256x256" href="/images/icon.png" />
		<link rel="icon" href="/images/icon.svg" />

    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta name="msapplication-TileImage" content="/images/icon.png" />

    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp,container-queries"></script>

    <% if (template?.customCSS) { %>
    <style type="text/tailwindcss">
      <%- template.customCSS %>
    </style>
    <% } %>

    <style type="text/tailwindcss">
      :root {
        <% if (template.backgroundColor) { %>
        --background-color: <%= template.backgroundColor %>;
        <% } %>

        <% if (template.textColor) { %>
        --text-color: <%= template.textColor %>;
        <% } %>

        <% if (template.primaryColor) { %>
        --primary-color: <%= template.primaryColor %>;
        <% } %>
        <% if (template.primaryTextColor) { %>
        --primary-text-color: <%= template.primaryTextColor %>;
        <% } %>

        <% if (template.secondaryColor) { %>
        --secondary-color: <%= template.secondaryColor %>;
        <% } %>
        <% if (template.secondaryTextColor) { %>
        --secondary-text-color: <%= template.secondaryTextColor %>;
        <% } %>

        <% if (template.tertiaryColor) { %>
        --tertiary-color: <%= template.tertiaryColor %>;
        <% } %>
        <% if (template.tertiaryTextColor) { %>
        --tertiary-text-color: <%= template.tertiaryTextColor %>;
        <% } %>

        <% if (template.borderColor) { %>
        --border-color: <%= template.borderColor %>;
        <% } %>
      }

      <% if (template.backgroundImageUrl) { %>
        body {
          background-image: url('<%= template.backgroundImageUrl %>');
        }
      <% } %>
    </style>

    <% if (template?.zendeskKey) { %>
    <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=<%= template.zendeskKey %>"></script>
    <% } %>
  </head>

  <body class="flex flex-col justify-center">
    <div class="flex min-h-dvh sm:min-h-full flex-col justify-between px-6 py-12 lg:px-8 <%= prompt.name %>">
      <header class="sm:mx-auto sm:w-full sm:max-w-sm pt-20 sm:pt-0">
        <%- include('logo') %>

        <% if (title !== 'Sign in' && title !== 'Sign up') { %>
        <h2 class="mt-10 text-center text-2xl font-bold leading-9 tracking-tight"><%= title %></h2>
        <% } %>
      </header>

      <main class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm grow flex flex-col justify-between">

