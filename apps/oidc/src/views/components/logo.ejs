<% if (oidcClient?.logoUri || template?.logoUri) { %>
  <img
    class="mx-auto h-20 w-auto"
    src="<%= oidcClient?.logoUri ?? template?.logoUri %>"
    onerror="this.onerror=null;this.src='/images/logo.svg';"
  />
  <% } else { %>
    <svg
      class="mx-auto h-20 w-auto"
      id="Layer_1"
      data-name="Layer 1"
      version="1.1"
      viewBox="0 0 114.05 83.940002"
      sodipodi:docname="logo-square-framed.svg"
      width="114.05"
      height="83.940002"
      inkscape:version="1.2.1 (9c6d41e, 2022-07-14)"
      xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
      xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:svg="http://www.w3.org/2000/svg">
      <sodipodi:namedview
        id="namedview1477"
        pagecolor="#ffffff"
        bordercolor="#ffffff"
        borderopacity="0.25"
        inkscape:showpageshadow="2"
        inkscape:pageopacity="0.0"
        inkscape:pagecheckerboard="0"
        inkscape:deskcolor="#ffffff"
        showgrid="false"
        inkscape:zoom="4.7546296"
        inkscape:cx="52.264849"
        inkscape:cy="34.91334"
        inkscape:window-width="1390"
        inkscape:window-height="1198"
        inkscape:window-x="0"
        inkscape:window-y="1121"
        inkscape:window-maximized="0"
        inkscape:current-layer="g1466" />
      <defs
        id="defs1442">
        <style
          id="style1440">
          .cls-1 {
            fill: #ffffff;
          }

          .cls-1, .cls-2 {
            stroke-width: 0px;
          }

          .cls-2 {
            fill: #ffffff;
          }
        </style>
      </defs>
      <path
        class="cls-1"
        d="M 102.12,83.94 H 11.94 C 5.36,83.94 0,78.58 0,72 V 11.94 C 0,5.36 5.36,0 11.94,0 h 90.17 c 6.58,0 11.94,5.36 11.94,11.94 V 72 c 0,6.58 -5.36,11.94 -11.94,11.94 z M 11.94,2.16 c -5.39,0 -9.78,4.39 -9.78,9.78 V 72 c 0,5.39 4.39,9.78 9.78,9.78 h 90.17 c 5.39,0 9.78,-4.39 9.78,-9.78 V 11.94 c 0,-5.39 -4.39,-9.78 -9.78,-9.78 z"
        id="path1444" />
      <g
        id="g1474"
        transform="translate(-50.97,-73.22)">
        <g
          id="g1458">
          <g
            id="g1452">
            <path
              class="cls-1"
              d="m 62.35,88.11 -0.4,-0.2 v 0.2 z"
              id="path1448" />
            <path
              class="cls-1"
              d="m 74.84,81.03 h -12.9 v 6.15 0 1.44 h 19.51 c 0.33,-0.05 1.98,-0.17 2.2,1.43 0.2,1.49 0.59,5.17 0.85,7.61 l 0.17,1.58 c 0.11,1.01 0.1,1.03 -0.27,1.46 -0.88,1.04 -1.78,1.46 -3.55,0.34 -0.65,-0.41 -1.26,-0.8 -1.8,-1.16 -0.12,-0.08 -0.2,-0.13 -0.24,-0.16 -2.31,-1.53 -3.58,-2.52 -3.6,-2.53 l -0.05,-0.04 c 0,0 -0.41,-0.28 -0.47,-0.74 -0.04,-0.33 0.11,-0.73 0.42,-1.15 0.5,0.17 1.05,0.28 1.64,0.25 0.73,-0.03 1.41,-0.24 2.03,-0.64 0.19,2.18 1.43,2.98 1.49,3.02 l 0.78,-1.21 c 0,0 -1.05,-0.75 -0.82,-3.01 0.04,-0.37 -0.17,-0.71 -0.5,-0.86 -0.34,-0.15 -0.73,-0.07 -0.98,0.21 -0.62,0.69 -1.29,1.03 -2.05,1.06 -1.46,0.06 -2.78,-1.16 -2.8,-1.17 l -0.99,1.04 c 0,0 0.36,0.34 0.88,0.68 -0.44,0.68 -0.62,1.35 -0.53,2 0.14,1.03 0.89,1.6 1.08,1.73 0.19,0.15 1.54,1.18 3.91,2.74 0.39,0.29 1.3,1.08 1.3,2.35 0,0.53 -0.79,1.15 -1.31,1.16 -0.02,0 -0.03,0 -0.05,0 -0.74,0 -1.33,-0.43 -2.07,-0.98 l -0.14,-0.1 c -0.87,-0.64 -2.58,-1.75 -4.22,-2.04 -1.85,-0.33 -2.45,-0.83 -2.56,-0.94 l -0.02,-0.06 -0.15,-0.12 c 0,0 -0.97,-0.81 -2.28,-2.63 L 66.7,97.68 C 65.11,95.9 63.26,95.96 63.2,95.96 l -1.27,0.02 0.02,1.1 v 0 11.83 h 12.9 c 9.12,0 14.73,-6.77 14.73,-13.93 0,-7.16 -5.61,-13.93 -14.73,-13.93 z"
              id="path1450" />
          </g>
          <path
            class="cls-1"
            d="m 90.31,108.9 11.86,-28.06 h 7.44 l 11.86,28.06 h -8.28 l -2.03,-4.97 h -10.75 l -1.99,4.97 H 90.3 Z m 12.38,-10.98 h 6.25 l -3.1,-7.92 -3.14,7.92 z"
            id="path1454" />
          <path
            class="cls-1"
            d="M 126.23,108.9 V 81.04 h 16.25 c 7.05,0 11.58,3.62 11.58,9.79 v 0.08 c 0,6.57 -5.06,10.03 -11.98,10.03 h -8.13 v 7.96 z m 7.72,-14 h 7.97 c 2.75,0 4.42,-1.43 4.42,-3.62 V 91.2 c 0,-2.39 -1.67,-3.66 -4.46,-3.66 h -7.93 z"
            id="path1456" />
        </g>
        <g
          id="g1466">
          <path
            id="path1446"
            class="cls-1"
            d="M 78.229766 115.29031 C 69.249775 115.29031 61.940703 122.59938 61.940703 131.57937 C 61.940703 140.55937 69.249775 147.87039 78.229766 147.87039 L 124.47 147.87039 C 133.44999 147.87039 140.75906 140.55937 140.75906 131.57937 C 140.75906 122.59938 133.44999 115.29031 124.47 115.29031 L 78.229766 115.29031 z M 98.479766 123.72 L 102.64969 123.72 L 109.30008 139.45047 L 104.65945 139.45047 L 103.52078 136.65945 L 97.489531 136.65945 L 96.370391 139.45047 L 91.829375 139.45047 L 98.479766 123.72 z M 77.180937 123.72977 L 92.579375 123.72977 L 92.579375 127.39969 L 87.259062 127.39969 L 87.259062 139.32937 L 82.989531 139.32937 L 82.989531 127.39969 L 77.180937 127.39969 L 77.180937 123.72977 z M 111.96023 123.72977 L 121.06961 123.72977 C 125.01961 123.72977 127.55984 125.76 127.55984 129.22 L 127.55984 129.25906 C 127.55984 132.93906 124.72914 134.88016 120.83914 134.88016 L 116.28055 134.88016 L 116.28055 139.33914 L 111.96023 139.33914 L 111.96023 139.35086 L 111.95047 139.33914 L 111.96023 139.33914 L 111.96023 123.72977 z M 116.29031 127.37039 L 116.29031 131.48953 L 120.75906 131.48953 C 122.29906 131.48953 123.23953 130.69023 123.23953 129.46023 L 123.23953 129.41922 C 123.23953 128.07922 122.29953 127.37039 120.73953 127.37039 L 116.29031 127.37039 z M 100.53055 128.85086 L 98.770781 133.29031 L 102.27078 133.29031 L 100.53055 128.85086 z " />
        </g>
        <g
          id="g1472">
          <path
            class="cls-1"
            d="m 147.32,148.91 c 3.78,-4.37 6.17,-9.98 6.46,-16.13 0.02,-0.43 0.03,-0.85 0.03,-1.28 0,-0.43 0,-0.86 -0.03,-1.28 -0.29,-6.15 -2.68,-11.76 -6.46,-16.13 -0.69,-0.8 -1.93,-0.83 -2.67,-0.08 -0.36,0.36 -0.53,0.82 -0.53,1.29 0,0.43 0.15,0.86 0.45,1.21 3.47,4.03 5.57,9.27 5.57,15 0,5.73 -2.1,10.97 -5.57,15 -0.63,0.73 -0.6,1.82 0.08,2.5 0.36,0.36 0.83,0.54 1.3,0.54 0.51,0 1.01,-0.21 1.37,-0.62 z"
            id="path1468" />
          <path
            class="cls-1"
            d="m 143.41,143.63 c 0,0 0,0 0.01,-0.02 0,-0.01 0.02,-0.02 0.02,-0.03 0.05,-0.06 0.1,-0.12 0.14,-0.18 0.04,-0.05 0.09,-0.11 0.13,-0.16 0.16,-0.21 0.32,-0.43 0.47,-0.64 0.03,-0.04 0.06,-0.08 0.09,-0.13 0.04,-0.06 0.09,-0.13 0.13,-0.19 0.29,-0.43 0.56,-0.86 0.81,-1.31 0.03,-0.05 0.06,-0.11 0.09,-0.16 0,-0.01 0.01,-0.02 0.02,-0.04 0.03,-0.05 0.06,-0.1 0.08,-0.16 0.02,-0.04 0.05,-0.09 0.07,-0.13 0.02,-0.04 0.05,-0.09 0.07,-0.13 0.11,-0.21 0.21,-0.42 0.31,-0.63 0.03,-0.05 0.05,-0.11 0.08,-0.16 0.03,-0.07 0.06,-0.14 0.09,-0.2 0.03,-0.06 0.05,-0.12 0.08,-0.18 0.16,-0.38 0.32,-0.76 0.46,-1.15 0.02,-0.06 0.04,-0.11 0.06,-0.17 0.06,-0.18 0.12,-0.36 0.18,-0.54 0.03,-0.08 0.05,-0.16 0.08,-0.25 0.02,-0.06 0.04,-0.12 0.05,-0.18 0.02,-0.06 0.03,-0.11 0.05,-0.17 0.02,-0.08 0.05,-0.17 0.07,-0.25 0.02,-0.06 0.03,-0.12 0.04,-0.17 0.05,-0.2 0.1,-0.4 0.14,-0.6 0.02,-0.07 0.03,-0.13 0.04,-0.2 0.01,-0.05 0.02,-0.1 0.03,-0.15 0,-0.04 0.02,-0.08 0.02,-0.11 0,-0.05 0.02,-0.09 0.03,-0.14 0.06,-0.31 0.11,-0.61 0.15,-0.93 0,-0.06 0.02,-0.13 0.03,-0.19 0,-0.07 0.02,-0.14 0.03,-0.22 0.01,-0.09 0.02,-0.18 0.03,-0.27 0,-0.08 0.02,-0.16 0.02,-0.24 0.01,-0.15 0.03,-0.3 0.03,-0.45 0,-0.06 0,-0.11 0.01,-0.17 0,-0.13 0.01,-0.26 0.02,-0.39 0,-0.06 0,-0.13 0,-0.19 0,-0.09 0,-0.19 0,-0.28 0,-0.08 0,-0.17 0,-0.25 0,-0.08 0,-0.17 0,-0.25 0,-0.09 0,-0.19 0,-0.28 0,-0.07 0,-0.13 0,-0.19 0,-0.13 -0.01,-0.27 -0.02,-0.4 0,-0.05 0,-0.11 0,-0.16 0,-0.15 -0.02,-0.3 -0.03,-0.45 0,-0.08 -0.01,-0.16 -0.02,-0.24 0,-0.09 -0.02,-0.18 -0.03,-0.27 0,-0.07 -0.02,-0.14 -0.03,-0.22 0,-0.07 -0.02,-0.13 -0.03,-0.2 -0.04,-0.31 -0.09,-0.62 -0.15,-0.92 0,-0.05 -0.02,-0.09 -0.03,-0.14 0,-0.04 -0.02,-0.08 -0.02,-0.11 0,-0.05 -0.02,-0.1 -0.03,-0.15 -0.01,-0.07 -0.03,-0.13 -0.04,-0.2 -0.04,-0.2 -0.09,-0.4 -0.14,-0.6 -0.01,-0.06 -0.03,-0.12 -0.04,-0.17 -0.02,-0.08 -0.04,-0.16 -0.06,-0.24 -0.02,-0.06 -0.03,-0.12 -0.05,-0.19 -0.02,-0.06 -0.03,-0.12 -0.05,-0.18 -0.03,-0.09 -0.05,-0.17 -0.08,-0.26 -0.04,-0.11 -0.07,-0.23 -0.11,-0.34 -0.02,-0.05 -0.04,-0.11 -0.05,-0.16 -0.16,-0.46 -0.34,-0.91 -0.53,-1.36 -0.03,-0.06 -0.05,-0.12 -0.08,-0.18 -0.03,-0.07 -0.06,-0.14 -0.09,-0.2 -0.03,-0.05 -0.05,-0.11 -0.08,-0.16 -0.1,-0.21 -0.21,-0.43 -0.31,-0.63 -0.02,-0.04 -0.05,-0.09 -0.07,-0.13 -0.02,-0.05 -0.05,-0.09 -0.07,-0.13 -0.03,-0.05 -0.06,-0.1 -0.08,-0.16 0,-0.01 -0.01,-0.02 -0.02,-0.04 -0.03,-0.05 -0.06,-0.11 -0.09,-0.16 -0.16,-0.29 -0.34,-0.58 -0.52,-0.86 -0.04,-0.06 -0.07,-0.11 -0.11,-0.17 -0.04,-0.06 -0.07,-0.11 -0.11,-0.17 -0.07,-0.1 -0.14,-0.2 -0.21,-0.31 -0.03,-0.04 -0.06,-0.08 -0.09,-0.13 -0.05,-0.08 -0.11,-0.15 -0.16,-0.23 -0.04,-0.05 -0.08,-0.11 -0.12,-0.16 -0.04,-0.05 -0.08,-0.11 -0.12,-0.16 -0.04,-0.05 -0.07,-0.1 -0.11,-0.14 -0.08,-0.11 -0.17,-0.22 -0.26,-0.33 0,0 0,0 0,0 -0.69,-0.84 -1.94,-0.92 -2.72,-0.17 0,0 -0.01,0.01 -0.02,0.02 0,0 -0.01,0.01 -0.02,0.02 -0.35,0.36 -0.52,0.82 -0.52,1.29 0,0.41 0.13,0.81 0.41,1.15 2.18,2.69 3.49,6.11 3.49,9.83 0,3.72 -1.31,7.14 -3.49,9.83 -0.59,0.72 -0.53,1.77 0.12,2.43 l 0.02,0.02 c 0,0 0.01,0.01 0.02,0.02 0.36,0.35 0.82,0.52 1.28,0.52 0.53,0 1.06,-0.23 1.43,-0.67 z"
            id="path1470" />
        </g>
      </g>
    </svg>
  <% } %>