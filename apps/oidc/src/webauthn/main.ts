// import {
//   platformAuthenticatorIsAvailable,
//   browserSupportsWebAuthn,
//   browserSupportsWebAuthnAutofill,
// } from '@simplewebauthn/browser'

// console.log('browserSupportsWebAuthn', browserSupportsWebAuthn())
// console.log('browserSupportsWebAuthnAutofill', browserSupportsWebAuthnAutofill())
// console.log('platformAuthenticatorIsAvailable()', await platformAuthenticatorIsAvailable())
