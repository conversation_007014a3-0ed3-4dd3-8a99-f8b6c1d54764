{"name": "@phigital-loyalty/oidc", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/oidc"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"mongodb": "^6.9.0", "oidc-provider": "^8.5.1", "@nestjs/common": "^11.0.7", "@phigital-loyalty/passport": "0.0.0", "ejs": "^3.1.10", "express": "4.21.2", "@phigital-loyalty/core": "0.0.0", "@phigital-loyalty/database": "0.0.0", "@nx/webpack": "20.0.6", "@phigital-loyalty/accounts": "0.0.0", "argon2": "^0.43.0", "nanoid": "^5.0.9", "nestjs-stripe": "^1.0.0", "stripe": "17.7.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "11.1.6", "body-parser": "1.20.3", "nestjs-cls": "^6.0.1", "@nestjs/passport": "^11.0.5", "wildcard": "^2.0.1", "express-ejs-layouts": "^2.5.1", "webpack": "5.99.9"}}