{"name": "gateway", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/gateway/src", "projectType": "application", "prefix": "gateway", "tags": [], "implicitDependencies": ["services-*"], "generators": {}, "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/gateway/webpack.config.js"}}, "serve": {"executor": "@nx/js:node"}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}}}