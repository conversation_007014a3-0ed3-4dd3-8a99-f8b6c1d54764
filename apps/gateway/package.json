{"name": "@phigital-loyalty/gateway", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/gateway"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@phigital-loyalty/core": "0.0.0", "@nx/webpack": "20.0.6", "@apollo/gateway": "^2.9.3", "@apollo/server": "4.12.2", "@apollo/utils.keyvadapter": "4.0.1", "@keyv/redis": "^4.0.2", "@nestjs/apollo": "13.1.0", "@nestjs/common": "^11.0.7", "@nestjs/config": "^4.0.0", "@nestjs/graphql": "13.1.0", "keyv": "5.5.0", "webpack": "5.99.9"}}