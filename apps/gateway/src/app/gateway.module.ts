import { IntrospectAndCompose } from '@apollo/gateway'
import { ApolloServerPluginCacheControl } from '@apollo/server/plugin/cacheControl'
import { ApolloServerPluginUsageReportingDisabled } from '@apollo/server/plugin/disabled'
import { ApolloServerPluginInlineTrace } from '@apollo/server/plugin/inlineTrace'
import {
  ApolloServerPluginLandingPageLocalDefault,
  ApolloServerPluginLandingPageProductionDefault,
} from '@apollo/server/plugin/landingPage/default'
import { ApolloServerPluginUsageReporting } from '@apollo/server/plugin/usageReporting'
import { KeyvAdapter } from '@apollo/utils.keyvadapter'
import KeyvRedis from '@keyv/redis'
import { ApolloGatewayDriver, ApolloGatewayDriverConfig } from '@nestjs/apollo'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { GraphQLModule } from '@nestjs/graphql'
import { Keyv } from 'keyv'

import {
  RuntimeModule,
  AuthModule,
  BuildServiceModule,
  OidcService,
  getPort,
  AuthenticatedDataSource,
  formatError,
} from '@phigital-loyalty/core'

@Module({
  imports: [
    RuntimeModule,
    GraphQLModule.forRootAsync<ApolloGatewayDriverConfig>({
      driver: ApolloGatewayDriver,
      imports: [ConfigModule, AuthModule, BuildServiceModule],
      useFactory: async (config: ConfigService, oidc: OidcService) => {
        const graphqlConfig: ApolloGatewayDriverConfig = {
          gateway: {
            ...(process.env.SERVICES
              ? {
                  supergraphSdl: new IntrospectAndCompose({
                    subgraphs: process.env.SERVICES.split(',').map((name) => ({
                      name,
                      url: `http://${
                        process.env.NODE_ENV === 'development' ? `localhost:${getPort(name)}` : name.replace(/\//g, '-')
                      }/graphql`,
                    })),
                    pollIntervalInMs: 10000,
                    subgraphHealthCheck: true,
                  }),
                  buildService: ({ url }) => new AuthenticatedDataSource({ url }),
                  __exposeQueryPlanExperimental: true,
                }
              : {
                  buildService: ({ url }) => new AuthenticatedDataSource({ url }),
                }),
          },
          server: {
            context: async ({ req }) => {
              return oidc.validate(req, true)
            },
            // autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
            // autoSchemaFile: true,
            introspection: process.env.NODE_ENV === 'development',
            sortSchema: true,
            persistedQueries: config.get(`REDIS_NAMESPACE`)
              ? {
                  cache: new KeyvAdapter(
                    new Keyv(
                      new KeyvRedis(
                        {
                          url: `redis://${config.get(`REDIS_HOSTNAME`)}:${config.get('REDIS_PORT') ?? 6379}`,
                          password: config.get('REDIS_PASSWORD') ?? undefined,
                        },
                        {
                          namespace: config.get(`REDIS_NAMESPACE`),
                        },
                      ),
                    ),
                  ),
                  ttl: Number(config.get('CACHE_TTL')),
                }
              : undefined,
            playground: false,
            plugins: [
              ApolloServerPluginCacheControl(),
              ...(process.env.NODE_ENV === 'production'
                ? [
                    ApolloServerPluginUsageReporting(),
                    ApolloServerPluginLandingPageProductionDefault({
                      graphRef: process.env.APOLLO_GRAPH_REF,
                    }),
                  ]
                : [
                    ApolloServerPluginUsageReportingDisabled(),
                    ApolloServerPluginInlineTrace(),
                    ApolloServerPluginLandingPageLocalDefault({
                      embed: {
                        initialState: {
                          sharedHeaders: {
                            Authorization: 'Bearer {{token}}',
                          },
                        },
                      },
                      footer: false,
                    }),
                  ]),
            ],
            formatError,
          },
        }

        return graphqlConfig
      },
      inject: [ConfigService, OidcService],
    }),
  ],
})
export class GatewayModule {}
