/* eslint-disable @nx/enforce-module-boundaries */
import { Injectable } from '@nestjs/common'

import { User } from '@phigital-loyalty/accounts'
import { BaseAbilityFactory, Can, Cannot, IntrospectionResponse } from '@phigital-loyalty/core'

import { AbilityFactory as CoreAbilityFactory } from '../../../services/core/src/app/ability.factory'
// import { AbilityFactory as EcommercesAbilityFactory } from '../../../services/ecommerce/src/app/ability.factory'
import { AbilityFactory as TagsAbilityFactory } from '../../../services/tags/src/app/ability.factory'

export const externalAbilityFactories = [
  CoreAbilityFactory,
  // EcommercesAbilityFactory,
  TagsAbilityFactory,
]

@Injectable()
export class AbilityFactory extends BaseAbilityFactory {
  constructor(
    readonly coreAbilityFactory: CoreAbilityFactory,
    // readonly ecommerceAbilityFactory: EcommercesAbilityFactory,
    readonly TagsAbilityFactory: TagsAbilityFactory, // readonly socialAbilityFactory: SocialAbilityFactory
  ) {
    super()
  }

  async rules(auth: IntrospectionResponse, can: Can, cannot: Cannot) {
    await this.coreAbilityFactory.rules(auth, can, cannot)
    // await this.ecommerceAbilityFactory.rules(auth, can, cannot)
    await this.TagsAbilityFactory.rules(auth, can, cannot)

    if (!auth) {
      return
    }

    can<User>('Avatar', User, { _id: auth.sub })

    if (this.hasRoles(['Support', 'Developer'], auth.roles)) {
      can<User>('Avatar', User)
    }
  }
}
