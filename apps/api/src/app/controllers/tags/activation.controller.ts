import { Body, Controller, Param, Post } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { ApiBearerAuth, ApiBody, ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'

import {
  CheckPolicies,
  NotifyUserInput,
  SendEmailInput,
  SendNotificationInput,
  SendSmsInput,
} from '@phigital-loyalty/core'
import { BaseController } from '@phigital-loyalty/database'
import { ActivationService, Activation } from '@phigital-loyalty/tags'

@ApiTags('activations')
@ApiBearerAuth()
@ApiOAuth2(['openid'])
@Controller({ path: 'activations', version: '1' })
export class ActivationController extends BaseController(Activation) {
  constructor(
    readonly entityService: ActivationService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }

  @Post(':id/notifyParticipants')
  @CheckPolicies('Notify', Activation, { id: 'id' })
  @ApiOkResponse()
  @ApiBody({ type: NotifyUserInput })
  async notifyParticipants(
    @Param('id') id: string,
    @Body('email') email: SendEmailInput,
    @Body('notification') notification: SendNotificationInput,
    @Body('sms') sms: SendSmsInput,
  ) {
    return this.entityService.notifyParticipants(id, {
      email,
      notification,
      sms,
    })
  }
}
