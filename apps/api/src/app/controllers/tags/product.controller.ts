import { Controller } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { ApiBearerAuth, ApiOAuth2, ApiTags } from '@nestjs/swagger'

import { BaseController } from '@phigital-loyalty/database'
import { ProductService, Product } from '@phigital-loyalty/tags'

@ApiTags('products')
@ApiBearerAuth()
@ApiOAuth2(['openid'])
@Controller({ path: 'products', version: '1' })
export class ProductController extends BaseController(Product) {
  constructor(
    readonly entityService: ProductService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }
}
