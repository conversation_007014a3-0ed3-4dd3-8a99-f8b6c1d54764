import { Body, Controller, ForbiddenException, Param, Post, Put, Query } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiForbiddenResponse,
  ApiOAuth2,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger'

import {
  CheckPolicies,
  Action,
  CurrentUserId,
  NotifyUserInput,
  SendEmailInput,
  SendNotificationInput,
  SendSmsInput,
} from '@phigital-loyalty/core'
import { BaseController, FilterArgs, getPagingParametersForMongoDB, WebhookService } from '@phigital-loyalty/database'
import {
  CreateTag,
  CreateTags,
  EM4425ValidationArguments,
  NXPValidationArguments,
  OrganizationService,
  Tag,
  TagService,
  TagWebhookEvent,
  Tap,
} from '@phigital-loyalty/tags'
import {
  GIAI96Components,
  GID96<PERSON>omponents,
  GRAI96Components,
  ISO15963Com<PERSON>,
  SGLN96Components,
  SGTIN96Components,
  SSCC96Components,
} from '@phigital-loyalty/tags/lib/chip/epc/epc.entities'

@ApiTags('tags')
@ApiBearerAuth()
@ApiOAuth2(['openid'])
@Controller({ path: 'tags', version: '1' })
@ApiExtraModels(SGTIN96Components)
@ApiExtraModels(SSCC96Components)
@ApiExtraModels(GID96Components)
@ApiExtraModels(SGLN96Components)
@ApiExtraModels(GRAI96Components)
@ApiExtraModels(GIAI96Components)
@ApiExtraModels(ISO15963Components)
export class TagController extends BaseController(Tag, ['entity', 'entities']) {
  constructor(
    readonly entityService: TagService,
    readonly organizationService: OrganizationService,
    readonly moduleRef: ModuleRef,
    readonly webhookService: WebhookService,
  ) {
    super(entityService, moduleRef)
  }

  @Post('tap/:id')
  @ApiExtraModels(EM4425ValidationArguments, NXPValidationArguments)
  @CheckPolicies(Action.Read, Tag, { id: 'id' })
  @ApiBody({
    schema: {
      oneOf: [{ $ref: getSchemaPath(EM4425ValidationArguments) }, { $ref: getSchemaPath(NXPValidationArguments) }],
    },
  })
  @ApiOkResponse({
    type: Tap,
  })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async tap(@Param('id') id: string, @Body() args: EM4425ValidationArguments | NXPValidationArguments) {
    return await this.authenticate(id, args).then(async (tap) => {
      await this.webhookService.emit(TagWebhookEvent.tap, tap.tag)

      return tap
    })
  }

  @Post('authenticate/:id')
  @CheckPolicies(Action.Read, Tag, { id: 'id' })
  @ApiBody({
    schema: {
      oneOf: [{ $ref: getSchemaPath(EM4425ValidationArguments) }, { $ref: getSchemaPath(NXPValidationArguments) }],
    },
  })
  @ApiOkResponse({
    type: Tap,
  })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async authenticate(@Param('id') id: string, @Body() args: EM4425ValidationArguments | NXPValidationArguments) {
    try {
      return await this.entityService.authenticate(id, args)
    } catch (error) {
      throw new ForbiddenException(error)
    }
  }

  @Post('/')
  @CheckPolicies(Action.Create, Tag)
  @ApiCreatedResponse({ description: 'The created Tag', type: Tag, isArray: true })
  @ApiBody({ type: CreateTag })
  async create(@Body('data') data: CreateTag) {
    const organization = await this.organizationService.findById(data.organizationId)

    if (!organization) {
      throw new Error(`Organization with id ${data.organizationId} not found`)
    }

    return this.entityService.createTag(data)
  }

  @Post('batch')
  @CheckPolicies(Action.Create, Tag)
  @ApiCreatedResponse({ description: 'The number of tags created', type: Number })
  @ApiBody({ type: CreateTags })
  async createMany(@Body('data') data: CreateTags) {
    const organization = await this.organizationService.findById(data.organizationId)

    if (!organization) {
      throw new Error(`Organization with id ${data.organizationId} not found`)
    }

    if (!data.csv && !data.chips) {
      throw new Error('Either csv or chips must be provided')
    }

    return this.entityService.createTags(data)
  }

  @Put(':id/register')
  @CheckPolicies('Claim', Tag, { id: 'id' })
  @ApiOkResponse({ description: 'The tag was registered successfully.', type: Tag })
  async register(@CurrentUserId() currentUserId: string, @Param('id') id: string) {
    return this.entityService.claim(id, currentUserId)
  }

  @Put(':id/release')
  @CheckPolicies(Action.Update, Tag, { id: 'id' })
  @ApiOkResponse({ description: 'The tag was released successfully.', type: Tag })
  async release(@CurrentUserId() currentUserId: string, @Param('id') id: string) {
    return this.entityService.release(id, currentUserId)
  }

  @Put(':id/transfer')
  @CheckPolicies(Action.Update, Tag, { id: 'id' })
  @ApiOkResponse({ description: 'The tag was successfully transferred.', type: Tag })
  async transfer(@CurrentUserId() currentUserId: string, @Param('id') id: string, @Param('userId') userId: string) {
    return this.entityService.transfer(id, currentUserId, userId)
  }

  @Post('notify')
  @CheckPolicies('Notify', Tag, { filter: 'query' })
  @ApiOkResponse()
  @ApiQuery({ name: 'filter', type: FilterArgs<Tag> })
  @ApiBody({ type: NotifyUserInput })
  async notify(
    @Query() filter: FilterArgs<Tag>,
    @Body('email') email: SendEmailInput,
    @Body('notification') notification: SendNotificationInput,
    @Body('sms') sms: SendSmsInput,
  ) {
    const { query } = getPagingParametersForMongoDB(filter)

    return this.entityService.notify(query, {
      email,
      notification,
      sms,
    })
  }

  @Post(':id/notify')
  @CheckPolicies('Notify', Tag, { id: 'id' })
  @ApiOkResponse()
  @ApiBody({ type: NotifyUserInput })
  async notifyById(
    @Param('id') id: string,
    @Body('email') email: SendEmailInput,
    @Body('notification') notification: SendNotificationInput,
    @Body('sms') sms: SendSmsInput,
  ) {
    return this.entityService.notify(
      { _id: id },
      {
        email,
        notification,
        sms,
      },
    )
  }
}
