import { Controller } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { ApiBearerAuth, ApiOAuth2, ApiTags } from '@nestjs/swagger'

import { BaseController } from '@phigital-loyalty/database'
import { ProgramService, Program } from '@phigital-loyalty/tags'

@ApiTags('programs')
@ApiBearerAuth()
@ApiOAuth2(['openid'])
@Controller({ path: 'programs', version: '1' })
export class ProgramController extends BaseController(Program) {
  constructor(
    readonly entityService: ProgramService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }
}
