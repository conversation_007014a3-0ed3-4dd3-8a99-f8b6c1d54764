import { Controller, Delete, Get, Param, Post, Query, UseGuards } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { ApiBearerAuth, ApiOAuth2, ApiOkResponse, ApiQuery, ApiTags, OmitType } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import {
  FederatedUser,
  FederatedUserService,
  Organization,
  OrganizationService,
  OrganizationWithRole,
  Role,
  RoleService,
  User,
  UserService,
} from '@phigital-loyalty/accounts'
import { Action, CheckPolicies, CurrentUserId, IsAuthenticated } from '@phigital-loyalty/core'
import { BaseController, FilterArgs, getPagingParametersForMongoDB } from '@phigital-loyalty/database'

export enum UserUserStatus {
  Offline = 'Offline',
  Online = 'Online',
}

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
class EntityCreateInput extends OmitType(User, ['_id', 'createdAt', 'updatedAt', 'deletedAt'] as (keyof User)[]) {}

Object.defineProperty(EntityCreateInput, 'name', { value: `create${User.name}Dto` })

@ApiTags('users')
@ApiBearerAuth()
@ApiOAuth2(['openid'])
@Controller({ path: 'users', version: '1' })
export class UserController extends BaseController(User, ['entity', 'entities']) {
  constructor(
    readonly entityService: UserService,
    readonly moduleRef: ModuleRef,
    readonly organizationService: OrganizationService,
    readonly roleService: RoleService,
    readonly federatedUserService: FederatedUserService,
  ) {
    super(entityService, moduleRef)
  }

  @Get('me')
  @UseGuards(IsAuthenticated)
  @ApiOkResponse({ description: 'The current user data.', type: User })
  async me(@CurrentUserId() userId: string) {
    return this.entityService.findById(userId)
  }

  @Get(':id/organizations')
  @CheckPolicies(Action.Read, Organization, { parentIdField: 'organizationId' })
  @ApiOkResponse({
    type: OrganizationWithRole,
    isArray: true,
  })
  @ApiQuery({ name: 'filter', type: FilterArgs<Organization>, required: false })
  async organizations(@Param('id') _id: string, @Query() filter?: FilterArgs<Organization>) {
    const roles = await this.roleService.find({ userId: new ObjectId(_id), organizationId: { $exists: true } })
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), _id: { $in: roles.map((item) => item.organizationId) } },
    })
    const organizations = await this.organizationService.find(query, options)

    return organizations.map((org) => ({
      ...org,
      role: roles.find((item) => item.organizationId.toString() === org._id.toString()).role,
    }))
  }

  @Get(':id/roles')
  @CheckPolicies(Action.Read, Role, { parentIdField: 'id', childReferenceField: 'userId' })
  @ApiOkResponse({
    type: Role,
    isArray: true,
  })
  @ApiQuery({ name: 'filter', type: FilterArgs<Role>, required: false })
  async roles(@Param('id') _id: string, @Query() filter?: FilterArgs<Role>) {
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), userId: new ObjectId(_id) },
    })
    return this.roleService.find(query, options)
  }

  @Get(':id/identities')
  @CheckPolicies(Action.Read, FederatedUser, { parentIdField: 'id', childReferenceField: 'sub' })
  @ApiOkResponse({
    type: FederatedUser,
    isArray: true,
  })
  @ApiQuery({ name: 'filter', type: FilterArgs<FederatedUser>, required: false })
  async identities(@Param('id') _id: string, @Query() filter?: FilterArgs<FederatedUser>) {
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), sub: new ObjectId(_id) },
    })
    return this.federatedUserService.find(query, options)
  }

  @Post('avatar/:url')
  @CheckPolicies('Avatar', User)
  @ApiOkResponse({ description: 'The current user data.', type: User })
  async addAvatarUrl(@CurrentUserId() userId: string, @Param('url') url: string) {
    await this.entityService.updateMany({ _id: new ObjectId(userId) }, { $addToSet: { avatarUrls: url } })

    return this.entityService.findById(userId)
  }

  @Delete('avatar/:url')
  @CheckPolicies('Avatar', User)
  @ApiOkResponse({ description: 'The current user data.', type: User })
  async removeAvatarUrl(@CurrentUserId() userId: string, @Param('url') url: string) {
    await this.entityService.updateMany({ _id: new ObjectId(userId) }, { $pull: { avatarUrls: url } })

    return this.entityService.findById(userId)
  }
}
