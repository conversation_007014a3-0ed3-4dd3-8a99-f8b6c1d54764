import { Controller, Get, Param, Query } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { ApiBearerAuth, ApiOAuth2, ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import {
  Organization,
  OrganizationService,
  RoleService,
  User,
  UserService,
  UserWithRole,
} from '@phigital-loyalty/accounts'
import { CheckPolicies, Action } from '@phigital-loyalty/core'
import { BaseController, FilterArgs, getPagingParametersForMongoDB } from '@phigital-loyalty/database'

@ApiTags('organizations')
@ApiBearerAuth()
@ApiOAuth2(['openid'])
@Controller({ path: 'organizations', version: '1' })
export class OrganizationController extends BaseController(Organization, ['entity', 'entities']) {
  constructor(
    readonly entityService: OrganizationService,
    readonly userService: UserService,
    readonly roleService: RoleService,
    readonly moduleRef: ModuleRef,
  ) {
    super(entityService, moduleRef)
  }

  @Get(':id/users')
  @CheckPolicies(Action.Manage, Organization)
  @ApiOkResponse({
    type: UserWithRole,
    isArray: true,
  })
  @ApiQuery({ name: 'filter', type: FilterArgs<Organization>, required: false })
  async organizations(@Param('id') _id: string, @Query() filter?: FilterArgs<User>) {
    const usersInOrg = await this.roleService.find({ organizationId: new ObjectId(_id) })
    const { query, options } = getPagingParametersForMongoDB({
      ...filter,
      where: { ...(filter?.where || {}), _id: { $in: usersInOrg.map((item) => item.userId) } },
    })
    const users = await this.userService.find(query, options)

    return users.map((user: UserWithRole) => {
      user.role = usersInOrg.find((item) => item.userId.toString() === user._id.toString()).role

      return user
    })
  }
}
