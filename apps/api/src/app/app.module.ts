import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { APP_INTERCEPTOR } from '@nestjs/core'

import { AccountsModule } from '@phigital-loyalty/accounts'
import { AuthModule, ClassSerializerAuthInterceptor, CoreModule, RuntimeModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database/mongodb'
import { TagsModule } from '@phigital-loyalty/tags'

import { AbilityFactory, externalAbilityFactories } from './ability.factory'
import { controllers } from './controllers'

@Module({
  imports: [
    RuntimeModule,
    CoreModule.forRoot(),
    AuthModule.forRoot(AbilityFactory, [], [...externalAbilityFactories]),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    AccountsModule.forRoot(),
    TagsModule,
  ],
  controllers: [...controllers],
  providers: [
    ConfigService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ClassSerializerAuthInterceptor,
    },
  ],
})
export class AppModule {}
