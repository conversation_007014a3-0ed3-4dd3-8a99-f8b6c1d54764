{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "prefix": "api", "tags": [], "generators": {}, "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/api/webpack.config.js"}}, "serve": {"executor": "@nx/js:node"}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}}}