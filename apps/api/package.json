{"name": "@phigital-loyalty/api", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/api"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@phigital-loyalty/database": "0.0.0", "@phigital-loyalty/core": "0.0.0", "@nx/webpack": "20.0.6", "@phigital-loyalty/accounts": "0.0.0", "@phigital-loyalty/services-core": "0.0.0", "@nestjs/common": "^11.0.7", "@phigital-loyalty/services-tags": "0.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "11.1.6", "@nestjs/swagger": "11.2.0", "mongodb": "^6.11.0", "@phigital-loyalty/tags": "0.0.0", "webpack": "5.99.9"}}