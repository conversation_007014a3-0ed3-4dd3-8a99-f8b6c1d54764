import { BullModule } from '@nestjs/bullmq'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import { CoreModule, RuntimeModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database'

import { WebhooksMicroservice } from './webhooks.microservice'
import { WebhooksQueueConsumer } from './webhooks.queue.consumer'
import { WebhooksQueueEvents } from './webhooks.queue.events'

@Module({
  imports: [
    RuntimeModule,
    CoreModule.forRoot(),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    BullModule.forRootAsync('webhooks', {
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        prefix: '{webhooks}',
        connection: {
          host: config.get('REDIS_HOSTNAME'),
          port: config.get('REDIS_PORT') || 6379,
          password: config.get('REDIS_PASSWORD') || undefined,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      configKey: 'webhooks',
      name: 'webhooks',
      defaultJobOptions: {
        attempts: 4,
        backoff: {
          type: '20', // seconds
        },
        removeOnComplete: true,
        removeOnFail: true,
      },
    }),
  ],
  controllers: [WebhooksMicroservice],
  providers: [WebhooksQueueConsumer, WebhooksQueueEvents],
})
export class AppModule {}
