import { Processor, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

import { WebhookPayload, type WebhookQueueEvent } from '@phigital-loyalty/core'
import { WebhookService } from '@phigital-loyalty/database'

@Processor('webhooks', {
  settings: {
    backoffStrategy: (attemptsMade: number, type: string, _err: Error, _job: Job) => {
      return 4 ** attemptsMade * parseInt(type) * 1000
    },
  },
})
export class WebhooksQueueConsumer extends WorkerHost {
  constructor(readonly webhookService: WebhookService) {
    super()
  }

  async process({ id, data, timestamp }: Job<WebhookQueueEvent, Promise<never>, string>) {
    const webhook = await this.webhookService.findById(data.webhookId)

    if (!webhook?.active) {
      return
    }

    if (
      !webhook.subscribeTo.some((pattern) => {
        try {
          const regex = new RegExp(pattern, 'i')
          return regex.test(data.action)
        } catch (e) {
          console.error(`Invalid regex pattern: ${pattern}`, e)
          return false
        }
      })
    ) {
      return
    }

    const payload = new WebhookPayload({
      action: data.action,
      entityIds: data.entityIds,
      relations: data.relations,
      timestamp,
      id,
    })

    const payloadString = JSON.stringify(payload)
    const signature = webhook.secret ? await this.webhookService.signPayload(webhook.secret, payloadString) : undefined

    const response = await fetch(webhook.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(signature && { 'X-Signature': signature }),
      },
      body: payloadString,
    })

    if (!response.ok) {
      throw new Error(`${response.status}: ${response.statusText}`)
    }
  }
}
