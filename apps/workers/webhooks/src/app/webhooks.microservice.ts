import { InjectQueue } from '@nestjs/bullmq'
import { Controller } from '@nestjs/common'
import { EventPattern } from '@nestjs/microservices'
import { Queue } from 'bullmq'

import { EntityEvent, hash, WebhookEvent, type WebhookQueueEvent } from '@phigital-loyalty/core'
import { WebhookService } from '@phigital-loyalty/database'

@Controller()
export class WebhooksMicroservice {
  constructor(
    @InjectQueue('webhooks') private webhooksQueue: Queue<WebhookQueueEvent>,
    readonly webhookService: WebhookService,
  ) {
    // this.webhooksQueue.count().then(console.log)
    // this.webhooksQueue.retryJobs().then(() => console.log(this.webhooksQueue.count()))
  }

  @EventPattern('webhook')
  async handleWebhookEvent(data: WebhookEvent) {
    for (const owner of data.owners) {
      const webhooks = await this.webhookService.find({
        active: true,
        parent: owner.type,
        parentId: owner.id,
        $expr: {
          $anyElementTrue: {
            $map: {
              input: '$subscribeTo',
              in: {
                $regexMatch: {
                  input: data.action,
                  regex: '$$this',
                  options: 'i',
                },
              },
            },
          },
        },
      })

      if (webhooks?.length) {
        await this.webhooksQueue.addBulk(
          webhooks.map((webhook) => ({
            name: 'entity-events',
            data: {
              webhookId: webhook._id,
              action: data.action,
              entityIds: owner.entityIds,
              relations: data.relations
                ?.filter((relation) => owner.entityIds.some((id) => relation.entityIds.includes(id)))
                .map((relation) => ({
                  type: relation.type,
                  id: relation.id,
                  entityIds: relation.entityIds.filter((id) => owner.entityIds.includes(id)),
                })),
            },
            opts: {
              jobId: `${webhook._id.toString()}:${data.action}:${hash(data.entityIds)}`,
            },
          })),
        )
      }
    }
  }

  @EventPattern('entity-event.*')
  async handleEntityEvent(data: EntityEvent) {
    const action = `${data.entity.toLowerCase()}.${String(data.type)}`

    await this.handleWebhookEvent({
      action,
      entityIds: data.entityIds,
      relations: data.relations,
      owners: data.owners,
    })
  }
}
