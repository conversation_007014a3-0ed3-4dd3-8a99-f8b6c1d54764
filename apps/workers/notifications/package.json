{"name": "@phigital-loyalty/workers-notifications", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/workers/notifications"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@nx/webpack": "20.0.10", "@phigital-loyalty/core": "0.0.0", "@nestjs/common": "^11.0.7", "webpack": "5.99.9"}}