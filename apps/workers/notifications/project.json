{"name": "workers-notifications", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/workers/notifications/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"assets": ["apps/workers/notifications/src/assets", {"glob": "**/*", "input": "libs/accounts/templates/", "output": "./templates/"}], "webpackConfig": "apps/workers/notifications/webpack.config.js"}}, "serve": {"executor": "@nx/js:node"}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}}}