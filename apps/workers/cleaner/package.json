{"name": "@phigital-loyalty/workers-cleaner", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/reality-connect/seed-backend.git", "directory": "apps/workers/cleaner"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@nx/webpack": "20.0.10", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.7", "@nestjs/config": "^4.0.0", "@nestjs/microservices": "^11.0.7", "@phigital-loyalty/accounts": "0.0.0", "@phigital-loyalty/core": "0.0.0", "@phigital-loyalty/database": "0.0.0", "bullmq": "^5.33.0", "@nestjs/event-emitter": "^3.0.0", "webpack": "5.99.9"}}