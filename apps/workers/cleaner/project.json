{"name": "workers-cleaner", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/workers/cleaner/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/workers/cleaner/webpack.config.js"}}, "serve": {"executor": "@nx/js:node"}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}}}