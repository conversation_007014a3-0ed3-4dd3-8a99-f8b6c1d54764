import { Processor, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

import { AuditEvent } from '@phigital-loyalty/core'

import { CleanerService } from './cleaner.service'

@Processor('cleaner')
export class CleanerQueueConsumer extends WorkerHost {
  constructor(readonly cleanerService: CleanerService) {
    super()
  }

  async process({ data }: Job<AuditEvent, Promise<never>, string>) {
    if (!data.entityIds?.length) return

    await this.cleanerService.clean(data)
  }
}
