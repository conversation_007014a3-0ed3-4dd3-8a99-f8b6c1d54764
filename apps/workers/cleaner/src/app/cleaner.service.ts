import { Injectable, OnModuleDestroy } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'

import {
  isParentTypeConfig,
  EntityClass,
  ServiceMap,
  BaseEntityService,
  Entity,
  AuditEvent,
} from '@phigital-loyalty/core'

@Injectable()
export class CleanerService<
  DTO extends Entity & {
    _id?: unknown
    id?: unknown
  } = Entity,
> implements OnModuleDestroy
{
  private eventListeners = []

  constructor(
    readonly eventEmitter: EventEmitter2,
    readonly serviceMap: ServiceMap,
  ) {}

  onModuleInit() {
    const services = this.serviceMap.all()

    for (const service of services) {
      service.auditEnabled = false

      this.setupListenersFor(service)
    }
  }

  onModuleDestroy() {
    this.eventListeners.forEach((e) => e.off())
  }

  async clean(event: AuditEvent) {
    await this.eventEmitter.emitAsync(
      `${event.entity}.${event.type}`,
      event.entityIds.map((_id) => ({ _id })),
    )
  }

  private setupListenersFor(service: BaseEntityService) {
    const dto = service.dto

    if (dto.options?.owner) {
      this.eventListeners.push(
        this.eventEmitter.on(
          `User.deleted`,
          async (records: DTO[]) => {
            if (records?.length) {
              await service.deleteMany({ [dto.options?.owner]: { $in: records.map((e) => e._id ?? e.id) } })
            }
          },
          { objectify: true },
        ),
      )

      if (dto.options?.softDelete) {
        this.eventListeners.push(
          this.eventEmitter.on(
            `User.restored`,
            async (records: DTO[]) => {
              if (records?.length) {
                await service.restore({ [dto.options?.owner]: { $in: records.map((e) => e._id ?? e.id) } })
              }
            },
            { objectify: true },
          ),
        )
      }
    }

    if (dto.options?.parent) {
      if (isParentTypeConfig(dto.options.parent)) {
        this.eventListeners.push(
          this.eventEmitter.on(
            `${dto.options.parent.type}.deleted`,
            async (records: DTO[]) => {
              if (records?.length) {
                await service.deleteMany({ [dto.options.parent.id]: { $in: records.map((e) => e._id ?? e.id) } })
              }
            },
            { objectify: true },
          ),
        )

        if (dto.options?.softDelete) {
          this.eventListeners.push(
            this.eventEmitter.on(
              `${dto.options.parent.type}.restored`,
              async (records: DTO[]) => {
                if (records?.length) {
                  await service.restore({ [dto.options.parent.id]: { $in: records.map((e) => e._id ?? e.id) } })
                }
              },
              { objectify: true },
            ),
          )
        }
      } else {
        this.eventListeners.push(
          this.eventEmitter.on(
            `*.deleted`,
            async (records: DTO[]) => {
              if (records?.length) {
                const ctor = records[0].constructor as EntityClass<Entity>
                await service.deleteMany({
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  [(dto.options.parent as any).typeField]: ctor.name,
                  [dto.options.parent.id]: { $in: records.map((e) => e._id ?? e.id) },
                })
              }
            },
            { objectify: true },
          ),
        )

        if (dto.options?.softDelete) {
          this.eventListeners.push(
            this.eventEmitter.on(
              `*.restored`,
              async (records: DTO[]) => {
                if (records?.length) {
                  const ctor = records[0].constructor as EntityClass<Entity>
                  await service.restore({
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    [(dto.options.parent as any).typeField]: ctor.name,
                    [dto.options.parent.id]: { $in: records.map((e) => e._id ?? e.id) },
                  })
                }
              },
              { objectify: true },
            ),
          )
        }
      }
    }
  }
}
