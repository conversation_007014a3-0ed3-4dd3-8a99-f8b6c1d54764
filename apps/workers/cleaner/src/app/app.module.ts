import { BullModule } from '@nestjs/bullmq'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { ClientsModule, Transport } from '@nestjs/microservices'

import { AccountsModule } from '@phigital-loyalty/accounts'
import { CoreModule, RuntimeModule } from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database'

import { CLEANER_SERVICE } from './cleaner.constants'
import { CleanerQueueConsumer } from './cleaner.queue.consumer'
import { CleanerQueueEvents } from './cleaner.queue.events'
import { CleanerService } from './cleaner.service'
import { EntityEventsMicroservice } from './entity-events.microservice'

@Module({
  imports: [
    RuntimeModule,
    CoreModule.forRoot(),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    ClientsModule.registerAsync([
      {
        imports: [ConfigModule],
        name: CLEANER_SERVICE,
        useFactory: async (config: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: config.get('REDIS_HOSTNAME'),
            port: config.get('REDIS_PORT') || 6379,
            password: config.get('REDIS_PASSWORD') || undefined,
          },
        }),
        inject: [ConfigService],
      },
    ]),
    BullModule.forRootAsync('cleaner', {
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        prefix: '{cleaner}',
        connection: {
          host: config.get('REDIS_HOSTNAME'),
          port: config.get('REDIS_PORT') || 6379,
          password: config.get('REDIS_PASSWORD') || undefined,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      configKey: 'cleaner',
      name: 'cleaner',
      defaultJobOptions: {
        attempts: 20,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: true,
      },
    }),
    AccountsModule.forRoot(),
  ],
  controllers: [EntityEventsMicroservice],
  providers: [CleanerService, CleanerQueueConsumer, CleanerQueueEvents],
})
export class AppModule {}
