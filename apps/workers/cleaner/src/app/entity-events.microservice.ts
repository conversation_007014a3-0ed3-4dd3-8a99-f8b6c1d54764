import { InjectQueue } from '@nestjs/bullmq'
import { Controller } from '@nestjs/common'
import { EventPattern } from '@nestjs/microservices'
import { Queue } from 'bullmq'

import { EntityDeleted, EntityRestored, EntityEnabled, EntityDisabled, AuditEvent, hash } from '@phigital-loyalty/core'

@Controller()
export class EntityEventsMicroservice {
  constructor(@InjectQueue('cleaner') private cleanerQueue: Queue<AuditEvent>) {}

  @EventPattern('entity-event.deleted')
  async handleEntityDeleted(data: EntityDeleted) {
    return this.handleEntityEvent(data)
  }

  @EventPattern('entity-event.restored')
  async handleEntityRestored(data: EntityRestored) {
    return this.handleEntityEvent(data)
  }

  @EventPattern('entity-event.enabled')
  async handleEntityEnabled(data: EntityEnabled) {
    return this.handleEntityEvent(data)
  }

  @EventPattern('entity-event.disabled')
  async handleEntityDisabled(data: EntityDisabled) {
    return this.handleEntityEvent(data)
  }

  private async handleEntityEvent(data: AuditEvent) {
    await this.cleanerQueue.add('entity-events', data, {
      jobId: `${`${data.entity.toLocaleLowerCase()}.${String(data.type)}`}:${hash(data.entityIds)}`,
    })
  }
}
