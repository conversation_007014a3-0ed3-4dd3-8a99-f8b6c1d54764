{"name": "@phigital-loyalty/subscriptions", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "apps/subscriptions"}, "bin": {"seed": "main.js"}, "publishConfig": {"access": "restricted"}, "dependencies": {"@nx/webpack": "20.0.10", "@phigital-loyalty/database": "0.0.0", "@phigital-loyalty/core": "0.0.0", "@phigital-loyalty/services-core": "0.0.0", "@phigital-loyalty/accounts": "0.0.0", "@nestjs/common": "^11.0.7", "@nestjs/apollo": "13.1.0", "@nestjs/config": "^4.0.0", "@nestjs/graphql": "13.1.0", "webpack": "5.99.9"}}