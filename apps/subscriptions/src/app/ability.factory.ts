/* eslint-disable @nx/enforce-module-boundaries */
import { Injectable } from '@nestjs/common'

import { BaseAbilityFactory, Can, Cannot, IntrospectionResponse } from '@phigital-loyalty/core'

import { AbilityFactory as CoreAbilityFactory } from '../../../services/core/src/app/ability.factory'
// import { AbilityFactory as EcommercesAbilityFactory } from '../../../services/ecommerce/src/app/ability.factory'

export const externalAbilityFactories = [
  CoreAbilityFactory,
  // EcommercesAbilityFactory,
]

@Injectable()
export class AbilityFactory extends BaseAbilityFactory {
  constructor(
    readonly coreAbilityFactory: CoreAbilityFactory,
    // readonly ecommerceAbilityFactory: EcommercesAbilityFactory,
  ) {
    super()
  }

  async rules(auth: IntrospectionResponse, can: Can, cannot: Cannot) {
    await this.coreAbilityFactory.rules(auth, can, cannot)
    // await this.ecommerceAbilityFactory.rules(auth, can, cannot)
  }
}
