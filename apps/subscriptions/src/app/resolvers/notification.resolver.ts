import { ObjectType, PartialType, Resolver } from '@nestjs/graphql'

import { Notification } from '@phigital-loyalty/core'
import { NotificationService, SubscriptionResolver } from '@phigital-loyalty/database/mongodb'

@ObjectType(`NotificationSubscriptionResult`)
class SubscriptionResult extends PartialType(Notification, ObjectType) {}

@Resolver(() => Notification)
export class NotificationResolver extends SubscriptionResolver(Notification, SubscriptionResult) {
  constructor(entityService: NotificationService) {
    super(entityService)
  }
}
