import { ObjectType, PartialType, Resolver } from '@nestjs/graphql'

import { User, UserService } from '@phigital-loyalty/accounts'
import { SubscriptionResolver } from '@phigital-loyalty/database/mongodb'

@ObjectType(`UserSubscriptionResult`)
class SubscriptionResult extends PartialType(User, ObjectType) {}

@Resolver(() => User)
export class UserResolver extends SubscriptionResolver(User, SubscriptionResult) {
  constructor(entityService: UserService) {
    super(entityService)
  }
}
