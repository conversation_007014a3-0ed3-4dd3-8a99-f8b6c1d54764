import { ObjectType, PartialType, Resolver } from '@nestjs/graphql'

import { Organization, OrganizationService } from '@phigital-loyalty/accounts'
import { SubscriptionResolver } from '@phigital-loyalty/database/mongodb'

@ObjectType(`OrganizationSubscriptionResult`)
class SubscriptionResult extends PartialType(Organization, ObjectType) {}

@Resolver(() => Organization)
export class OrganizationResolver extends SubscriptionResolver(Organization, SubscriptionResult) {
  constructor(entityService: OrganizationService) {
    super(entityService)
  }
}
