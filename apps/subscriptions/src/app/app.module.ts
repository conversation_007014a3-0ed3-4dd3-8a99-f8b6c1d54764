import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { GraphQLModule } from '@nestjs/graphql'

import { AccountsModule } from '@phigital-loyalty/accounts'
import {
  AuthModule,
  BuildServiceModule,
  CoreModule,
  EmailAddressScalar,
  IPScalar,
  JSONObjectScalar,
  JSONScalar,
  OidcService,
  PhoneNumberScalar,
  RuntimeModule,
  TimeZoneScalar,
} from '@phigital-loyalty/core'
import { MongodbModule } from '@phigital-loyalty/database/mongodb'

import { AbilityFactory, externalAbilityFactories } from './ability.factory'
import { resolvers } from './resolvers'

@Module({
  imports: [
    RuntimeModule,
    CoreModule.forRoot(),
    AuthModule.forRoot(AbilityFactory, [], [...externalAbilityFactories]),
    MongodbModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        uri: config.get('MONGODB_URI'),
        dbName: config.get('MONGODB_DATABASE'),
      }),
      inject: [ConfigService],
    }),
    MongodbModule.registerSharedProviders(),
    AccountsModule.forRoot(),
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [ConfigModule, AuthModule, BuildServiceModule],
      useFactory: async (oidc: OidcService) => {
        const graphqlConfig: ApolloDriverConfig = {
          introspection: process.env.NODE_ENV === 'development',
          installSubscriptionHandlers: true,
          context: async ({ req }) => {
            return oidc.validate(req, true)
          },
          subscriptions: {
            'graphql-ws': {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              async onConnect(ctx: any) {
                ctx.req = ctx.extra.request
                ctx.req.headers = {
                  ...ctx.req.headers,
                  ...ctx.connectionParams,
                }
              },
            },
          },
          // autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
          autoSchemaFile: true,
        }
        return graphqlConfig
      },
      inject: [OidcService],
    }),
  ],
  providers: [
    JSONScalar,
    JSONObjectScalar,
    EmailAddressScalar,
    PhoneNumberScalar,
    TimeZoneScalar,
    IPScalar,
    ...resolvers,
  ],
})
export class AppModule {}
