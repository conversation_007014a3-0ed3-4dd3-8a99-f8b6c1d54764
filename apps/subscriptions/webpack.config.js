const { composePlugins, withNx } = require('@nx/webpack')
const { SourceMapDevToolPlugin } = require('webpack')

// Nx plugins for webpack.
module.exports = composePlugins(withNx(), (config) => {
  // Note: This was added by an Nx migration. Webpack builds are required to have a corresponding Webpack config file.
  // See: https://nx.dev/recipes/webpack/webpack-config-setup

  config.plugins = [...(config.plugins || []), new SourceMapDevToolPlugin({})]

  return config
})
