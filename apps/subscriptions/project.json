{"name": "subscriptions", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/subscriptions/src", "projectType": "application", "prefix": "subscriptions", "tags": [], "generators": {}, "targets": {"build": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/subscriptions/webpack.config.js"}}, "serve": {"executor": "@nx/js:node"}, "docker-build": {"executor": "@reality-connect/nx-docker:build"}, "apollo-check": {"executor": "@reality-connect/nx-apollo:check", "options": {"graphqlName": "subscriptions"}}, "apollo-publish": {"executor": "@reality-connect/nx-apollo:publish", "options": {"graphqlName": "subscriptions"}}}}