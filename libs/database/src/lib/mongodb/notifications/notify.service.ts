import { Injectable } from '@nestjs/common'
import { compile } from 'ejs'

import { NotifyUserInput, SendEmailInput, SendNotificationInput, SendSmsInput } from '@phigital-loyalty/core'

import { InstallationService } from './installation.service'
import { NotificationService } from './notification.service'

export interface Contact {
  _id?: unknown
  id?: unknown

  name?: string
  familyName?: string
  givenName?: string

  email?: string
  contactEmail?: string
  billing?: string

  phoneNumber?: string
}

@Injectable()
export class NotifyService {
  constructor(
    readonly notification: NotificationService,
    readonly installation: InstallationService,
  ) {}

  private emailRecipient(contact: Contact): string {
    return `"${this.name(contact)}" <${this.email(contact)}>`
  }

  private name(contact: Contact): string {
    // TODO: consider locale
    return contact.name || `${contact.givenName} ${contact.familyName}`
  }

  private email(contact: Contact): string {
    return contact.contactEmail || contact.billing || contact.email
  }

  async send(contacts: Contact[], input: NotifyUserInput) {
    if (!input.email) {
      await this.sendMail(contacts, input.email)
    }

    if (!input.notification) {
      await this.sendPushNotification(contacts, input.notification)
    }

    if (!input.sms) {
      await this.sendSms(contacts, input.sms)
    }
  }

  async sendMail(contacts: Contact[], sendMailOptions: SendEmailInput) {
    const subject = sendMailOptions.subject && compile(sendMailOptions.subject)
    const html = sendMailOptions.html && compile(sendMailOptions.html as string)
    const text = sendMailOptions.text && compile(sendMailOptions.text as string)

    for (const contact of contacts) {
      const context = {
        name: this.name(contact),
      }

      await this.notification.email.send({
        ...sendMailOptions,
        to: this.emailRecipient(contact),
        ...(subject && { subject: subject(context) }),
        ...(html && { html: html(context) }),
        ...(text && { text: text(context) }),
      })
    }
  }

  async sendPushNotification(contacts: Contact[], notification: SendNotificationInput) {
    const title = notification.title && compile(notification.title)
    const body = notification.body && compile(notification.body)

    for (const contact of contacts) {
      const context = {
        name: this.name(contact),
      }
      const installations = await this.installation.find({
        userId: contact._id,
        token: { $exists: true },
      })
      await this.notification.push.sendMulticast({
        tokens: installations.map((installation) => installation.token),
        notification: {
          ...notification,
          ...(title && { title: title(context) }),
          ...(body && { body: body(context) }),
        },
      })
    }
  }

  async sendSms(contacts: Contact[], message: SendSmsInput) {
    const template = message.body && compile(message.body)

    for (const contact of contacts) {
      if (!contact.phoneNumber) {
        continue
      }

      const context = {
        name: this.name(contact),
      }

      await this.notification.sms.send({
        ...message,
        to: contact.phoneNumber,
        ...(template && { body: template(context) }),
      })
    }
  }
}
