// Mock problematic ESM modules first
jest.mock(
  'openid-client',
  () => ({
    discovery: jest.fn(),
    tokenIntrospection: jest.fn(),
  }),
  { virtual: true },
)

jest.mock(
  'nanoid',
  () => ({
    nanoid: jest.fn().mockReturnValue('mock-nanoid-1234'),
  }),
  { virtual: true },
)

import { NotFoundException, type INestApplication } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { ObjectType } from '@nestjs/graphql'
import { Test } from '@nestjs/testing'
import { ObjectId, type Collection, type WithId } from 'mongodb'

import { CacheService, ServiceMap } from '@phigital-loyalty/core'

import { MongoDBEntity } from './base.entity'
import { BaseMongoDBEntityService } from './entity.service.base'
import { InjectCollection, InjectDataloader } from './mongodb.decorators'

import type { MongoDBLoader } from './mongodb.service'
import type { TestingModule } from '@nestjs/testing'

/**
 * Test entity class for service testing
 */
@ObjectType()
class TestEntity extends MongoDBEntity {
  name: string
  age: number
  email?: string
  tags?: string[]
  status?: string

  static get options() {
    return {
      collection: 'test_entities',
      softDelete: true,
      softDeleteOptions: {
        remove: ['email'],
      },
    }
  }

  constructor(data?: Partial<TestEntity>) {
    super(data)
    Object.assign(this, data)
  }
}

/**
 * Test implementation of BaseMongoDBEntityService
 */
class TestEntityService extends BaseMongoDBEntityService<TestEntity> {
  constructor(
    @InjectCollection('test_entities') collection: Collection<TestEntity>,
    @InjectDataloader('test_entities') dataloader: MongoDBLoader<TestEntity>,
    moduleRef: ModuleRef,
  ) {
    super(TestEntity, collection, dataloader, moduleRef)
  }

  /**
   * Exposes the protected toDto method for testing purposes
   */
  public exposedToDto(data: TestEntity | WithId<TestEntity>): TestEntity {
    return this.toDto(data)
  }
}

describe('BaseMongoDBEntityService', () => {
  let app: INestApplication
  let service: TestEntityService
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let cacheService: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockCollection: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockDataloader: any

  beforeEach(async () => {
    mockCollection = {
      collectionName: 'test_entities',
      findOne: jest.fn(),
      find: jest.fn(() => ({ toArray: jest.fn() })),
      insertOne: jest.fn(),
      updateOne: jest.fn(),
      updateMany: jest.fn(),
      deleteOne: jest.fn(),
      deleteMany: jest.fn(),
      findOneAndUpdate: jest.fn(),
      countDocuments: jest.fn(),
      distinct: jest.fn(),
      watch: jest.fn(),
      aggregate: jest.fn(() => ({ toArray: jest.fn() })),
      bulkWrite: jest.fn(),
    }

    mockDataloader = {
      generateDataLoader: jest.fn().mockReturnValue({
        load: jest.fn(),
        loadMany: jest.fn().mockResolvedValue([]),
      }),
    }

    const mockCacheService = {
      getValue: jest.fn(),
      setValue: jest.fn(),
      clear: jest.fn(),
      generateKey: jest.fn().mockImplementation((prefix, suffix) => `${prefix}:${suffix}`),
    }

    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        ServiceMap,
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: 'test_entitiesCollection',
          useValue: mockCollection,
        },
        {
          provide: 'test_entitiesDataloader',
          useValue: mockDataloader,
        },
        TestEntityService,
      ],
    }).compile()

    service = moduleRef.get<TestEntityService>(TestEntityService)
    cacheService = moduleRef.get<CacheService>(CacheService)

    app = moduleRef.createNestApplication()
    await app.init()
  })

  afterEach(async () => {
    await app.close()
  })

  describe('CRUD Operations', () => {
    it('should create an entity and return it', async () => {
      const record = { name: 'Test', age: 30 } as TestEntity
      const id = new ObjectId()

      mockCollection.insertOne.mockResolvedValue({ insertedId: id, acknowledged: true })
      mockCollection.findOne.mockResolvedValue({
        _id: id,
        name: 'Test',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      const result = await service.createOne(record)

      expect(mockCollection.insertOne).toHaveBeenCalled()
      expect(mockCollection.findOne).toHaveBeenCalled()
      expect(result).toBeInstanceOf(TestEntity)
      expect(result).toEqual(
        expect.objectContaining({
          _id: id,
          name: 'Test',
          age: 30,
        }),
      )
    })

    it('should find an entity by ID', async () => {
      const id = new ObjectId()
      const mockData = {
        _id: id,
        name: 'Test',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockCollection.findOne.mockResolvedValue(mockData)

      const result = await service.findById(id)

      expect(mockCollection.findOne).toHaveBeenCalledWith(expect.objectContaining({ _id: id }), expect.anything())
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('Test')
      expect(result.age).toBe(30)
    })

    it('should update an entity and return it', async () => {
      const query = { name: 'Test' }
      const update = { $set: { age: 31 } }
      const mockUpdatedData = {
        _id: new ObjectId(),
        name: 'Test',
        age: 31,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockCollection.findOneAndUpdate.mockResolvedValue(mockUpdatedData)

      const result = await service.updateOne(query, update)

      expect(mockCollection.findOneAndUpdate).toHaveBeenCalled()
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.age).toBe(31)
    })

    it('should throw NotFoundException when updating non-existent entity', async () => {
      const query = { name: 'NonExistent' }
      const update = { $set: { age: 31 } }

      mockCollection.findOneAndUpdate.mockResolvedValue(null)

      await expect(service.updateOne(query, update)).rejects.toThrow(NotFoundException)
    })
  })

  describe('Soft Delete & Restore', () => {
    it('should soft delete an entity when softDelete is enabled', async () => {
      const query = { name: 'ToDelete' }
      const mockEntity = {
        _id: new ObjectId(),
        name: 'ToDelete',
        email: '<EMAIL>',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockCollection.findOne.mockResolvedValueOnce(mockEntity)
      mockCollection.findOneAndUpdate.mockResolvedValueOnce({
        ...mockEntity,
        deletedAt: new Date(),
      })

      const result = await service.deleteOne(query)

      expect(mockCollection.findOne).toHaveBeenCalled()
      expect(mockCollection.findOneAndUpdate).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          $set: expect.objectContaining({
            deletedAt: expect.any(Date),
          }),
          $unset: expect.objectContaining({
            email: '', // Should unset email as configured in options
          }),
        }),
        expect.anything(),
      )
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('ToDelete')
    })

    it('should restore soft-deleted entities', async () => {
      const query = { name: 'ToRestore' }
      const mockIds = [new ObjectId(), new ObjectId()]
      const mockEntities = mockIds.map((id) => ({
        _id: id,
        name: 'ToRestore',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      })) as TestEntity[]

      // Mock collection find to handle both the initial ID fetch and the final entity fetch
      mockCollection.find.mockImplementation((q, opts) => {
        if (opts?.projection?._id === 1) {
          return {
            toArray: jest.fn().mockResolvedValue(mockIds.map((id) => ({ _id: id }))),
          }
        } else {
          return {
            toArray: jest.fn().mockResolvedValue(mockEntities),
          }
        }
      })

      mockCollection.updateMany.mockResolvedValue({
        modifiedCount: 2,
        matchedCount: 2,
        upsertedCount: 0,
        upsertedId: null,
        acknowledged: true,
      })

      const result = await service.restore(query)

      expect(mockCollection.updateMany).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          $set: expect.objectContaining({
            updatedAt: expect.any(Date),
          }),
          $unset: expect.objectContaining({
            deletedAt: '',
          }),
        }),
        expect.anything(),
      )
      expect(result).toHaveLength(2)
    })
  })

  describe('Bulk Operations', () => {
    it('should perform bulk write operations with timestamps', async () => {
      const operations = [
        {
          insertOne: {
            document: {
              name: 'Bulk1',
              age: 30,
            } as unknown as TestEntity,
          },
        },
        {
          insertOne: {
            document: {
              name: 'Bulk2',
              age: 35,
            } as unknown as TestEntity,
          },
        },
      ]

      const mockIds = [new ObjectId(), new ObjectId()]
      const mockEntities = [
        { _id: mockIds[0], name: 'Bulk1', age: 30, createdAt: new Date(), updatedAt: new Date() },
        { _id: mockIds[1], name: 'Bulk2', age: 35, createdAt: new Date(), updatedAt: new Date() },
      ] as TestEntity[]

      mockCollection.bulkWrite.mockResolvedValue({
        insertedCount: 2,
        matchedCount: 0,
        modifiedCount: 0,
        deletedCount: 0,
        upsertedCount: 0,
        insertedIds: { '0': mockIds[0], '1': mockIds[1] },
        upsertedIds: {},
        acknowledged: true,
      })

      mockCollection.find.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockEntities),
      })

      const result = await service.bulkWrite(operations, true)

      expect(mockCollection.bulkWrite).toHaveBeenCalled()

      // Verify timestamps were added
      const bulkWriteArgs = mockCollection.bulkWrite.mock.calls[0][0]
      expect(bulkWriteArgs[0].insertOne.document.createdAt).toBeDefined()
      expect(bulkWriteArgs[0].insertOne.document.updatedAt).toBeDefined()

      expect(result).toHaveLength(2)
      expect(result[0].name).toBe('Bulk1')
      expect(result[1].name).toBe('Bulk2')
    })

    it('should handle update operations in bulk write', async () => {
      const mockId = new ObjectId()
      const operations = [
        {
          updateOne: {
            filter: { _id: mockId },
            update: {
              $set: {
                name: 'Updated',
                age: 40,
              },
            },
          },
        },
      ]

      mockCollection.bulkWrite.mockResolvedValue({
        insertedCount: 0,
        matchedCount: 1,
        modifiedCount: 1,
        deletedCount: 0,
        upsertedCount: 0,
        insertedIds: {},
        upsertedIds: {},
        acknowledged: true,
      })

      await service.bulkWrite(operations, false)

      const bulkWriteArgs = mockCollection.bulkWrite.mock.calls[0][0]
      expect(bulkWriteArgs[0].updateOne.update.$set.updatedAt).toBeDefined()
      expect(bulkWriteArgs[0].updateOne.update.$setOnInsert.createdAt).toBeDefined()
    })
  })

  describe('Utility Methods', () => {
    it('should properly convert data to DTO instances', () => {
      const data = {
        _id: new ObjectId(),
        name: 'Test',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const result = service.exposedToDto(data)

      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('Test')
      expect(result.age).toBe(30)
    })

    it('should use interfaceResolver if provided', () => {
      const data = {
        _id: new ObjectId(),
        name: 'Original',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const resolver = jest.fn().mockReturnValue({
        ...data,
        name: 'Transformed',
      })

      // Set both properties needed by the resolver mechanism
      Object.defineProperty(service, 'options', {
        value: { interfaceResolver: resolver },
      })
      service.interfaceResolver = resolver

      const result = service.exposedToDto(data)

      expect(resolver).toHaveBeenCalledWith(data)
      expect(result.name).toBe('Transformed')
    })
  })

  describe('Cache behavior', () => {
    it('should use cache when available', async () => {
      const query = { name: 'Test' }
      const cachedResult = JSON.stringify({ _id: new ObjectId().toString(), name: 'Test', age: 30 })
      cacheService.getValue.mockResolvedValue(cachedResult)

      await service.findOne(query)

      expect(cacheService.getValue).toHaveBeenCalled()
      expect(mockCollection.findOne).not.toHaveBeenCalled()
    })

    it('should invalidate cache on updates', async () => {
      const query = { name: 'Test' }
      const update = { $set: { age: 31 } }
      const updatedEntity = {
        _id: new ObjectId(),
        name: 'Test',
        age: 31,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockCollection.findOneAndUpdate.mockResolvedValue(updatedEntity)

      await service.updateOne(query, update)

      expect(cacheService.clear).toHaveBeenCalled()
      expect(mockCollection.findOneAndUpdate).toHaveBeenCalled()
    })
  })

  describe('Instance Conversion', () => {
    it('should return entity instances from deleteMany method', async () => {
      const query = { age: { $lt: 30 } }
      const mockEntities = [
        { _id: new ObjectId(), name: 'Delete1', age: 25, createdAt: new Date(), updatedAt: new Date() },
        { _id: new ObjectId(), name: 'Delete2', age: 28, createdAt: new Date(), updatedAt: new Date() },
      ]

      mockCollection.find.mockImplementation(() => ({
        toArray: jest.fn().mockResolvedValue(mockEntities),
      }))

      mockCollection.updateMany.mockResolvedValue({
        modifiedCount: 2,
        matchedCount: 2,
        upsertedCount: 0,
        upsertedId: null,
        acknowledged: true,
      })

      const results = await service.deleteMany(query)

      expect(results).toHaveLength(2)
      expect(results[0]).toBeInstanceOf(TestEntity)
      expect(results[1]).toBeInstanceOf(TestEntity)
      expect(results[0].name).toBe('Delete1')
      expect(results[1].name).toBe('Delete2')
    })

    it('should return entity instances from all exposed methods', async () => {
      const id = new ObjectId()
      const mockEntity = {
        _id: id,
        name: 'TestEntity',
        age: 30,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      const mockEntities = [
        { _id: new ObjectId(), name: 'Multi1', age: 30, createdAt: new Date(), updatedAt: new Date() },
        { _id: new ObjectId(), name: 'Multi2', age: 35, createdAt: new Date(), updatedAt: new Date() },
      ]

      // Mock findOne/findById
      mockCollection.findOne.mockImplementation((query) => {
        if (query && query._id && (query._id === id || (query._id.equals && query._id.equals(id)))) {
          return mockEntity
        }
        return null
      })

      // Mock find
      mockCollection.find.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockEntities),
      })

      // Mock createOne
      const newId = new ObjectId()
      const newEntity = {
        _id: newId,
        name: 'New',
        age: 25,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockCollection.insertOne.mockResolvedValue({
        insertedId: newId,
        acknowledged: true,
      })

      // Add newEntity to findOne responses
      const originalFindOne = mockCollection.findOne.getMockImplementation()
      mockCollection.findOne.mockImplementation((query) => {
        if (query && query._id && (query._id === newId || (query._id.equals && query._id.equals(newId)))) {
          return newEntity
        }
        return originalFindOne(query)
      })

      // Mock updateOne
      const updatedEntity = {
        _id: id,
        name: 'Updated',
        age: 40,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      mockCollection.findOneAndUpdate.mockResolvedValue(updatedEntity)

      // Mock deleteOne
      const entityToDelete = {
        _id: new ObjectId(),
        name: 'ToDelete',
        age: 35,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      const deleteQuery = { _id: entityToDelete._id }

      // Add entity to delete to findOne responses
      mockCollection.findOne.mockImplementation((query) => {
        if (query && query._id && query._id.equals && query._id.equals(entityToDelete._id)) {
          return entityToDelete
        }
        if (query && query._id && (query._id === id || (query._id.equals && query._id.equals(id)))) {
          return mockEntity
        }
        if (query && query._id && (query._id === newId || (query._id.equals && query._id.equals(newId)))) {
          return newEntity
        }
        return null
      })

      const deletedEntity = {
        ...entityToDelete,
        deletedAt: new Date(),
      }

      // Handle soft delete in findOneAndUpdate
      mockCollection.findOneAndUpdate.mockImplementation((query, update) => {
        if (
          update &&
          update.$set &&
          update.$set.deletedAt &&
          query &&
          query._id &&
          query._id.equals &&
          query._id.equals(entityToDelete._id)
        ) {
          return deletedEntity
        }
        return updatedEntity
      })

      // Test each method for proper instance conversion
      let result = await service.findOne({ _id: id })
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('TestEntity')

      result = await service.findById(id)
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('TestEntity')

      const findResults = await service.find()
      expect(findResults).toHaveLength(2)
      expect(findResults[0]).toBeInstanceOf(TestEntity)
      expect(findResults[1]).toBeInstanceOf(TestEntity)

      result = await service.createOne({ name: 'New', age: 25 })
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('New')

      result = await service.updateOne({ _id: id }, { $set: { age: 40 } })
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('Updated')

      result = await service.deleteOne(deleteQuery)
      expect(result).toBeInstanceOf(TestEntity)
      expect(result.name).toBe('ToDelete')
    })
  })
})
