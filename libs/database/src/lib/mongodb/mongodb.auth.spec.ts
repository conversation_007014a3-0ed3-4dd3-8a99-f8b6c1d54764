// Mock problematic ESM modules first
jest.mock(
  'openid-client',
  () => ({
    discovery: jest.fn(),
    tokenIntrospection: jest.fn(),
  }),
  { virtual: true },
)

jest.mock(
  'nanoid',
  () => ({
    nanoid: jest.fn().mockReturnValue('mock-nanoid-1234'),
  }),
  { virtual: true },
)

// Only mock what's absolutely necessary
jest.mock('@phigital-loyalty/core', () => {
  const actual = jest.requireActual('@phigital-loyalty/core')

  return {
    ...actual,
    isResolvingGraphQLField: jest.fn().mockReturnValue(false),
    getContextParams: jest.fn().mockReturnValue({}),
    can: jest.fn().mockReturnValue(true),
    warnWhenNoRulesFound: jest.fn(),
    isPolicyHandlerOptionsParent: jest.fn().mockReturnValue(false),
    isPolicyHandlerOptionsBase: jest.fn().mockReturnValue(false),
    accessibleBy: actual.accessibleBy || jest.fn(),
    accessibleFieldsBy: actual.accessibleFieldsBy || jest.fn(),
  }
})

import { createMongoAbility } from '@casl/ability'
import { createMock } from '@golevelup/ts-jest'
import { GqlExecutionContext } from '@nestjs/graphql'

import { Action, can, getContextParams, isResolvingGraphQLField } from '@phigital-loyalty/core'

import { MongoDBEntity } from './base.entity'
import { abilityToMongoPipeline, abilityToMongoQuery, authGuardProvider, mergeProjections } from './mongodb.auth'

import type { FilterArgsWithProjection } from './base.input'
import type { ExecutionContext } from '@nestjs/common'

// Test entity with soft delete support
class TestEntity extends MongoDBEntity {
  name?: string
  email?: string
  status?: string
  organizationId?: string
  meta?: {
    createdBy?: string
    tags?: string[]
  }

  static get options() {
    return {
      collection: 'test_entity',
      softDelete: true,
    }
  }

  constructor(data?: Partial<TestEntity>) {
    super(data)
    Object.assign(this, data)
  }
}

describe('MongoDB Auth', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(getContextParams as jest.Mock).mockReturnValue({})
    ;(isResolvingGraphQLField as jest.Mock).mockReturnValue(false)
  })

  describe('authGuardProvider', () => {
    const createTestAbility = () =>
      createMongoAbility([
        { action: Action.Read, subject: TestEntity, conditions: { status: 'active' } },
        { action: Action.Update, subject: TestEntity, conditions: { organizationId: 'org123' } },
      ])

    const createHandler = (options = {}) => ({
      action: Action.Read,
      dto: TestEntity,
      options,
      type: 'test_entity',
    })

    it('should check permissions for REST context', () => {
      const handler = createHandler()
      const context = createMock<ExecutionContext>()
      const ability = createTestAbility()
      context.getType.mockReturnValue('http')

      const result = authGuardProvider(handler, ability, context)

      expect(result).toBe(true)
      expect(can).toHaveBeenCalledWith(ability, Action.Read, TestEntity)
    })

    it('should handle GraphQL field resolvers', () => {
      const handler = createHandler()
      const context = createMock<ExecutionContext>()
      const ability = createTestAbility()
      ;(isResolvingGraphQLField as jest.Mock).mockReturnValue(true)
      const parent = { _id: 'test123' }
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getArgByIndex: jest.fn().mockReturnValue(parent),
      })

      const result = authGuardProvider(handler, ability, context)

      expect(result).toBe(true)
      expect(can).toHaveBeenCalled()
    })

    it('should handle array of IDs in GraphQL field resolvers', () => {
      const handler = createHandler()
      const context = createMock<ExecutionContext>()
      const ability = createTestAbility()
      ;(isResolvingGraphQLField as jest.Mock).mockReturnValue(true)
      const parent = { _id: ['id1', 'id2', 'id3'] }
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getArgByIndex: jest.fn().mockReturnValue(parent),
      })

      const result = authGuardProvider(handler, ability, context)
      expect(result).toBe(true)
      expect(can).toHaveBeenCalledTimes(3)
    })
  })

  describe('abilityToMongoQuery', () => {
    const handler = {
      action: Action.Read,
      dto: TestEntity,
      type: 'test_entity',
    }

    it('should generate proper queries for org-scoped rules', () => {
      const ability = createMongoAbility([
        { action: Action.Read, subject: TestEntity, conditions: { organizationId: 'org123' } },
      ])

      const result = abilityToMongoQuery(ability, handler, { where: {}, limit: 10 }, false, true)

      expect(result.where).toBeDefined()
      expect(result.where.$and).toBeDefined()
      expect(result.where.$and).toContainEqual({ $or: [{ organizationId: 'org123' }] })
      expect(result.where.deletedAt).toEqual({ $exists: false })
    })

    it('should merge complex rule conditions with query parameters', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestEntity,
          conditions: {
            $or: [{ organizationId: 'org123' }, { 'meta.createdBy': 'user456' }],
          },
        },
      ])

      const userQuery = {
        where: { status: 'active' },
        sort: { createdAt: -1 },
      } as FilterArgsWithProjection<TestEntity>

      const result = abilityToMongoQuery(ability, handler, userQuery, false, true)

      expect(result.where.status).toBe('active')
      expect(result.where.$and).toBeDefined()
      expect(result.where.$and[0]).toHaveProperty('$or')
      expect(result.sort.createdAt).toBe(-1)
    })

    it('should handle field-level permissions as projections differently for REST vs GraphQL', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestEntity,
          fields: ['name', 'email', 'status'],
        },
      ])

      const restResult = abilityToMongoQuery<TestEntity>(ability, handler, { where: {}, limit: 20 }, false, false)

      expect(restResult.projection).toBeDefined()
      expect(restResult.projection.name).toBe(1)
      expect(restResult.projection.email).toBe(1)
      expect(restResult.projection.status).toBe(1)

      const graphqlResult = abilityToMongoQuery(ability, handler, { where: {}, limit: 20 }, true, false)

      expect(graphqlResult.projection).toBeUndefined()
    })

    it('should use accessibleFieldsBy for field-level projections', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestEntity,
          fields: ['name', 'email', 'status'],
        },
      ])

      const restResult = abilityToMongoQuery<TestEntity>(ability, handler, { where: {}, limit: 20 }, false, false)

      expect(restResult.projection).toBeDefined()
      expect(restResult.projection.name).toBe(1)
      expect(restResult.projection.email).toBe(1)
      expect(restResult.projection.status).toBe(1)
    })
  })

  describe('abilityToMongoPipeline', () => {
    const handler = {
      action: Action.Read,
      dto: TestEntity,
      type: 'test_entity',
    }

    it('should inject ability conditions into existing pipeline', () => {
      const ability = createMongoAbility([
        { action: Action.Read, subject: TestEntity, conditions: { status: 'published' } },
      ])

      const pipeline = [
        { $match: { createdAt: { $gt: new Date('2023-01-01') } } },
        { $sort: { createdAt: -1 } },
        { $limit: 10 },
      ]

      const result = abilityToMongoPipeline(ability, handler, pipeline, false, true)

      expect(result[0].$match.$and).toBeDefined()
      expect(result[0].$match.$and[0].$or).toBeDefined()
      expect(result[0].$match.$and[0].$or[0]).toHaveProperty('status', 'published')
      expect(result[0].$match.deletedAt).toEqual({ $exists: false })
      expect(result[0].$match.createdAt).toEqual({ $gt: expect.any(Date) })

      expect(result[1].$sort).toEqual({ createdAt: -1 })
      expect(result[2].$limit).toBe(10)
    })

    it('should handle complex pipelines with lookups and projections', () => {
      const ability = createMongoAbility([
        { action: Action.Read, subject: TestEntity, conditions: { status: 'active' } },
      ])

      const complexPipeline = [
        { $match: { type: 'customer' } },
        {
          $lookup: {
            from: 'organizations',
            localField: 'organizationId',
            foreignField: '_id',
            as: 'organization',
          },
        },
        { $unwind: '$organization' },
        { $project: { password: 0, 'organization.privateData': 0 } },
      ]

      const result = abilityToMongoPipeline(ability, handler, complexPipeline, false, true)

      expect(result[0].$match.$and).toBeDefined()
      expect(result[0].$match.$and[0].$or).toBeDefined()
      expect(result[0].$match.$and[0].$or[0]).toHaveProperty('status', 'active')
      expect(result[0].$match.type).toBe('customer')
      expect(result[0].$match.deletedAt).toEqual({ $exists: false })

      expect(result[1].$lookup).toBeDefined()
      expect(result[2].$unwind).toBe('$organization')
      expect(result[3].$project).toBeDefined()
    })

    it('should use accessibleFieldsBy for pipeline projections', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestEntity,
          fields: ['name', 'email', 'status'],
        },
      ])

      const pipeline = [{ $match: { type: 'customer' } }]

      const result = abilityToMongoPipeline(ability, handler, pipeline, false, true)

      const projectStage = result.find((stage) => stage.$project)
      expect(projectStage).toBeDefined()
      expect(projectStage.$project.name).toBe(1)
      expect(projectStage.$project.email).toBe(1)
      expect(projectStage.$project.status).toBe(1)
    })

    it('should merge with existing project stages', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestEntity,
          fields: ['name', 'email', 'status'],
        },
      ])

      const pipeline = [{ $match: { type: 'customer' } }, { $project: { privateData: 0 } }]

      const result = abilityToMongoPipeline(ability, handler, pipeline, false, true)

      const projectStage = result.find((stage) => stage.$project)
      expect(projectStage).toBeDefined()
      expect(projectStage.$project.name).toBe(1)
      expect(projectStage.$project.email).toBe(1)
      expect(projectStage.$project.status).toBe(1)
      expect(projectStage.$project.privateData).toBe(0)
    })
  })

  describe('mergeProjections', () => {
    it('should return newProj when existingProj is empty', () => {
      const existingProj = {}
      const newProj = { name: 1, email: 1 }

      const result = mergeProjections(existingProj, newProj)

      expect(result).toEqual(newProj)
    })

    it('should return existingProj when newProj is empty', () => {
      const existingProj = { name: 1, email: 1 }
      const newProj = {}

      const result = mergeProjections(existingProj, newProj)

      expect(result).toEqual(existingProj)
    })

    it('should handle inclusion-based projections correctly', () => {
      const existingProj = { name: 1, email: 1 }
      const newProj = { age: 1, status: 1 }

      const result = mergeProjections(existingProj, newProj)

      expect(result).toEqual({
        name: 1,
        email: 1,
        age: 1,
        status: 1,
      })
    })

    it('should handle exclusion-based projections correctly', () => {
      const existingProj = { password: 0, secretKey: 0 }
      const newProj = { internalId: 0, notes: 0 }

      const result = mergeProjections(existingProj, newProj)

      expect(result).toEqual({
        password: 0,
        secretKey: 0,
        internalId: 0,
        notes: 0,
      })
    })

    it('should prioritize exclusions when mixing inclusion and exclusion', () => {
      const existingProj = { name: 1, email: 1, password: 0 }
      const newProj = { age: 1, email: 0 }

      const result = mergeProjections(existingProj, newProj)

      expect(result).toEqual({
        name: 1,
        email: 0, // Exclusion takes precedence
        password: 0,
        age: 1,
      })
    })

    it('should handle complex projections with nested fields', () => {
      const existingProj = {
        'profile.name': 1,
        'profile.email': 1,
        security: 0,
      }
      const newProj = {
        'profile.phone': 1,
        'profile.email': 0,
        metadata: 1,
      }

      const result = mergeProjections(existingProj, newProj)

      expect(result).toEqual({
        'profile.name': 1,
        'profile.email': 0, // Exclusion takes precedence
        'profile.phone': 1,
        security: 0,
        metadata: 1,
      })
    })

    it('should handle null or undefined projections', () => {
      expect(mergeProjections(null, { name: 1 })).toEqual({ name: 1 })
      expect(mergeProjections({ name: 1 }, null)).toEqual({ name: 1 })
      expect(mergeProjections(undefined, { name: 1 })).toEqual({ name: 1 })
      expect(mergeProjections({ name: 1 }, undefined)).toEqual({ name: 1 })
    })
  })
})
