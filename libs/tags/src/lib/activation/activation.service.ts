import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Collection, Filter, ObjectId } from 'mongodb'

import { IntrospectionResponse, NotifyUserInput } from '@phigital-loyalty/core'
import {
  InjectCollection,
  InjectDataloader,
  MongoDBLoader,
  MongoDBEntityService,
  getPagingParametersForMongoDBAggregation,
  PaginationArgs,
  NotifyService,
} from '@phigital-loyalty/database'

import { Activation, ActivationSearch, ActivityEntityEvent } from './activation.entity'
import { PaginatedPerks } from './perk.entity'
import { UserService } from '../shared'
import { TagService } from '../tag'

@Injectable()
export class ActivationService extends MongoDBEntityService<Activation> {
  constructor(
    @InjectCollection('activation') collection: Collection<Activation>,
    @InjectDataloader('activation') dataloader: MongoDBLoader<Activation>,
    moduleRef: ModuleRef,
    readonly userService: UserService,
    readonly tagService: TagService,
    readonly notifyService: NotifyService,
  ) {
    super(Activation, collection, dataloader, moduleRef)
  }

  async participate(auth: IntrospectionResponse, id: string | ObjectId) {
    const activation = await this.findById(id)

    if (!activation) {
      throw new BadRequestException(`Perk with id ${id} not found`)
    }

    const user = await this.userService.findById(auth.sub)

    if (!user) {
      throw new BadRequestException(`User with id ${auth.sub} not found`)
    }

    if (activation.limit && activation.participantIds && activation.participantIds.length >= activation.limit) {
      throw new BadRequestException(`Perk has reached its participant limit`)
    }

    await this.checkIfUserIsAllowedToParticipate(user._id, activation)

    const res = await this.updateOne({ _id: activation._id }, { $addToSet: { participantIds: user._id } })
    await this.registerAuditEvent(ActivityEntityEvent.optIn, res)
    return res
  }

  async withdraw(auth: IntrospectionResponse, id: string | ObjectId) {
    const activation = await this.findOne({
      _id: new ObjectId(id),
      participantIds: { $in: [new ObjectId(auth.sub)] },
    })

    if (!activation) {
      throw new BadRequestException(
        `Activation with id ${id} not found or User with id ${auth.sub} is not a participant`,
      )
    }

    const user = await this.userService.findById(auth.sub)

    if (!user) {
      throw new BadRequestException(`User with id ${auth.sub} not found`)
    }

    await this.registerAuditEvent(ActivityEntityEvent.optOut, activation)

    return this.updateOne({ _id: activation._id }, { $pull: { participantIds: user._id } })
  }

  async perksForTag(
    auth: IntrospectionResponse,
    tagId: string | ObjectId,
    search?: ActivationSearch,
    options: PaginationArgs<Activation> = { limit: 10 },
  ): Promise<PaginatedPerks> {
    const tag = await this.tagService.findById(tagId)

    if (!tag?.userId) {
      return { nodes: [], totalCount: 0 }
    }

    if (tag.userId.toString() !== auth.sub) {
      return { nodes: [], totalCount: 0 }
    }

    const result = await this.aggregate<PaginatedPerks>(
      getPagingParametersForMongoDBAggregation(
        {
          ...options,
          where: this.generateFilter(search, {
            organizationId: tag.organizationId,
            $and: [
              {
                $or: [
                  { 'filter.registeredMinDate': { $exists: false } },
                  { 'filter.registeredMinDate': null },
                  {
                    $expr: {
                      $lte: ['$filter.registeredMinDate', tag.registeredAt],
                    },
                  },
                ],
              },
              {
                $or: [
                  { 'filter.registeredMaxDate': { $exists: false } },
                  { 'filter.registeredMaxDate': null },
                  {
                    $expr: {
                      $gte: ['$filter.registeredMaxDate', tag.registeredAt],
                    },
                  },
                ],
              },
              // Checking filter.labels
              {
                $or: [
                  { 'filter.labels': { $exists: false } },
                  { 'filter.labels': { $size: 0 } },
                  { 'filter.labels': null },
                  {
                    $expr: {
                      $gt: [
                        {
                          $size: {
                            $setIntersection: ['$filter.labels', tag.labels || []],
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
              // Checking filter.programIds
              {
                $or: [
                  { 'filter.programIds': { $exists: false } },
                  { 'filter.programIds': { $size: 0 } },
                  { 'filter.programIds': null },
                  {
                    $expr: {
                      $gt: [
                        {
                          $size: {
                            $setIntersection: ['$filter.programIds', tag.programId ? [tag.programId] : []],
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
              // Checking filter.productIds
              {
                $or: [
                  { 'filter.productIds': { $exists: false } },
                  { 'filter.productIds': { $size: 0 } },
                  { 'filter.productIds': null },
                  {
                    $expr: {
                      $gt: [
                        {
                          $size: {
                            $setIntersection: ['$filter.productIds', tag.productId ? [tag.productId] : []],
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
            ],
          }),
        },
        [
          {
            $addFields: {
              isParticipant: {
                $in: [new ObjectId(auth.sub), { $ifNull: ['$participantIds', []] }],
              },
              isFull: {
                $cond: {
                  if: { $not: ['$limit'] }, // if "limit" is null or missing
                  then: false,
                  else: {
                    $gte: [{ $size: { $ifNull: ['$participantIds', []] } }, '$limit'],
                  },
                },
              },
            },
          },
        ],
      ),
    )

    if (!result.length) return { nodes: [], totalCount: 0 }

    return result[0]
  }

  async perks(
    auth: IntrospectionResponse,
    search: ActivationSearch,
    options: PaginationArgs<Activation> = { limit: 10 },
  ): Promise<PaginatedPerks> {
    if (search?.tagId) {
      return await this.perksForTag(auth, search.tagId, search, options)
    }

    const tagFilter = {
      userId: new ObjectId(auth.sub),
      ...(search.organizationId && { organizationId: search.organizationId }),
      $and: [
        {
          $or: [
            {
              disabled: { $exists: false },
            },
            {
              disabled: false,
            },
          ],
        },
        ...(search.labels?.length ? [{ labels: { $in: search.labels } }] : []),
        ...(search.programIds?.length ? [{ programId: { $in: search.programIds } }] : []),
        ...(search.productIds?.length ? [{ productId: { $in: search.productIds } }] : []),
      ],
    }

    if (!tagFilter.$and?.length) {
      delete tagFilter.$and
    }

    const result = await this.tagService.aggregate<PaginatedPerks>([
      { $match: tagFilter },
      {
        $group: {
          _id: '$userId',
          tags: {
            $push: {
              organizationId: '$organizationId',
              labels: '$labels',
              programId: '$programId',
              productId: '$productId',
              registeredAt: '$registeredAt',
            },
          },
        },
      },
      {
        $lookup: {
          from: 'activation',
          let: {
            tags: '$tags',
          },
          pipeline: getPagingParametersForMongoDBAggregation(
            {
              ...options,
              where: this.generateFilter(search, {
                $expr: {
                  $gt: [
                    {
                      $size: {
                        $filter: {
                          input: '$$tags',
                          as: 'tag',
                          cond: {
                            $and: [
                              {
                                $eq: ['$organizationId', '$$tag.organizationId'],
                              },

                              // Check labels
                              {
                                $or: [
                                  { $eq: [{ $ifNull: ['$filter.labels', null] }, null] },
                                  { $eq: [{ $size: { $ifNull: ['$filter.labels', []] } }, 0] },

                                  {
                                    $gt: [
                                      {
                                        $size: {
                                          $setIntersection: [
                                            { $ifNull: ['$$tag.labels', []] },
                                            { $ifNull: ['$filter.labels', []] },
                                          ],
                                        },
                                      },
                                      0,
                                    ],
                                  },
                                ],
                              },

                              // Check programIds
                              {
                                $or: [
                                  { $eq: [{ $ifNull: ['$filter.programIds', null] }, null] },
                                  { $eq: [{ $size: { $ifNull: ['$filter.programIds', []] } }, 0] },
                                  {
                                    $in: ['$$tag.programId', { $ifNull: ['$filter.programIds', []] }],
                                  },
                                ],
                              },

                              // Check productIds
                              {
                                $or: [
                                  { $eq: [{ $ifNull: ['$filter.productIds', null] }, null] },
                                  { $eq: [{ $size: { $ifNull: ['$filter.productIds', []] } }, 0] },
                                  {
                                    $in: ['$$tag.productId', { $ifNull: ['$filter.productIds', []] }],
                                  },
                                ],
                              },

                              // e.g. If the filter has a registeredMinDate/MaxDate, ensure
                              // the tag.registeredAt is within that range:
                              {
                                $or: [
                                  { $not: ['$filter.registeredMinDate'] },
                                  {
                                    $lte: ['$filter.registeredMinDate', '$$tag.registeredAt'],
                                  },
                                ],
                              },
                              {
                                $or: [
                                  { $not: ['$filter.registeredMaxDate'] },
                                  {
                                    $gte: ['$filter.registeredMaxDate', '$$tag.registeredAt'],
                                  },
                                ],
                              },
                            ],
                          },
                        },
                      },
                    },
                    0,
                  ],
                },
              }),
            },
            [
              {
                $addFields: {
                  isParticipant: {
                    $in: [new ObjectId(auth.sub), { $ifNull: ['$participantIds', []] }],
                  },
                  isFull: {
                    $cond: {
                      if: { $not: ['$limit'] },
                      then: false,
                      else: {
                        $gte: [{ $size: { $ifNull: ['$participantIds', []] } }, '$limit'],
                      },
                    },
                  },
                },
              },
            ],
          ),
          as: 'activations',
        },
      },
      { $replaceRoot: { newRoot: { $ifNull: [{ $arrayElemAt: ['$activations', 0] }, { nodes: [], totalCount: 0 }] } } },
    ])

    return result?.[0] ?? { nodes: [], totalCount: 0 }
  }

  async notifyParticipants(id: string | ObjectId, input: NotifyUserInput) {
    const activation = await this.findById(id)

    if (!activation?.participantIds?.length) {
      return
    }

    const users = await this.userService.find({ _id: { $in: activation.participantIds } })

    await this.notifyService.send(users, input)
  }

  async checkIfUserIsAllowedToParticipate(userId: string | ObjectId, activation: Activation) {
    const { filter } = activation

    const tags = await this.tagService.find({
      userId: new ObjectId(userId),
      organizationId: activation.organizationId,
      ...(filter?.labels?.length ||
      filter.programIds?.length ||
      filter.productIds?.length ||
      filter.registeredMinDate ||
      filter.registeredMaxDate
        ? {
            $and: [
              ...(filter.labels?.length ? [{ labels: { $in: filter.labels } }] : []),
              ...(filter.programIds?.length ? [{ programId: { $in: filter.programIds } }] : []),
              ...(filter.productIds?.length ? [{ productId: { $in: filter.productIds } }] : []),
              ...(filter.registeredMinDate ? [{ registeredAt: { $gte: filter.registeredMinDate } }] : []),
              ...(filter.registeredMaxDate ? [{ registeredAt: { $lte: filter.registeredMaxDate } }] : []),
              // ...(filter.countries ? [{ country: { $in: user.zoneinfo } }] : []),
            ],
          }
        : {}),
    })

    if (!tags?.length) {
      throw new ForbiddenException('User is not allowed to participate in this activation')
    }
  }

  generateFilter(search: ActivationSearch, filter: Filter<Activation> = {}): Filter<Activation> {
    const commonFilter = this.commonFilter(search, filter)

    const result = {
      ...commonFilter,
      $and: [
        ...commonFilter.$and,
        ...(search.labels?.length ? [{ 'filter.labels': { $in: search.labels } }] : []),
        ...(search.programIds?.length ? [{ 'filter.programs': { $in: search.programIds } }] : []),
        ...(search.productIds?.length ? [{ 'filter.products': { $in: search.productIds } }] : []),
        // {
        //   $or: [
        //     { countries: { $in: user.country } },
        //     { countries: { $exists: false } },
        //     { countries: null },
        //   ]
        // },
      ],
    }

    if (result.$and.length === 0) {
      delete result.$and
    }

    return result
  }

  private commonFilter(search: ActivationSearch, filter: Filter<Activation> = {}): Filter<Activation> {
    const now = new Date()

    return {
      ...(search.organizationId && { organizationId: search.organizationId }),
      ...filter,
      $and: [
        ...(filter.$and ?? []),
        ...(search.active
          ? [
              {
                $or: [{ runFrom: { $lte: now } }, { runFrom: { $exists: false } }, { runFrom: null }],
              },
              {
                $or: [{ runTo: { $gte: now } }, { runTo: { $exists: false } }, { runTo: null }],
              },
            ]
          : []),
      ],
    }
  }
}
