import { ObjectType, Field, GraphQLISODateTime, Directive } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import { EntityType, paginated } from '@phigital-loyalty/core'
import { FieldObjectID, MongoDBEntity } from '@phigital-loyalty/database'

import { ActivityType } from './activation.entity'

@ObjectType()
@Directive('@key(fields: "_id")')
@EntityType<Perk>({
  stats: true,

  paginated: () => PaginatedPerks,
})
export class Perk extends MongoDBEntity {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => String, { description: 'Name of the perk' })
  @ApiProperty({ type: String, description: 'Name of the perk' })
  name: string

  @Field(() => String, { description: 'URL for an external page that should have relevant information for the Perk' })
  @ApiProperty({
    type: String,
    description: 'URL for an external page that should have relevant information for the Perk',
  })
  url: string

  @Field(() => ActivityType, { description: 'The type of the perk' })
  @ApiProperty({ enum: ActivityType, description: 'The type of the perk' })
  type: ActivityType

  @Field(() => Boolean, { description: 'Whether the user is a participant' })
  @ApiProperty({ type: Boolean, description: 'Whether the user is a participant' })
  isParticipant: boolean

  @Field(() => Boolean, { description: 'Whether the perk is full' })
  @ApiProperty({ type: Boolean, description: 'Whether the perk is full' })
  isFull: boolean

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the perk starts.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the perk starts.' })
  runFrom?: Date

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the perk ends.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the perk ends.' })
  runTo?: Date

  @Field(() => String, { nullable: true, description: 'The URL to the picture of the perk' })
  @ApiProperty({ type: String, required: false, description: 'The URL to the picture of the perk' })
  picture?: string

  @Field(() => String, { nullable: true, description: 'The description of the perk' })
  @ApiProperty({ type: String, required: false, description: 'The description of the perk' })
  description?: string

  @Field(() => Date, { nullable: true, description: 'The date of the perk' })
  @ApiProperty({ type: Date, required: false, description: 'The date of the perk' })
  date?: Date

  @Field(() => String, { nullable: true, description: 'The location of the perk' })
  @ApiProperty({ type: String, required: false, description: 'The location of the perk' })
  location?: string
}

@ObjectType()
export class PaginatedPerks extends paginated(Perk) {}
