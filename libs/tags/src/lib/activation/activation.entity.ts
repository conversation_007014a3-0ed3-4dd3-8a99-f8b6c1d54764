import { Directive, Field, GraphQLISODateTime, InputType, ObjectType, registerEnumType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import { EntityType, Iso3166Alpha2, paginated } from '@phigital-loyalty/core'
import { FieldObjectID, MongoDBEntity } from '@phigital-loyalty/database'

export enum ActivityEntityEvent {
  optIn = 'opt-in',
  optOut = 'opt-out',
}

export enum ActivityType {
  IN_PERSON = 'IN_PERSON',
  VIRTUAL = 'VIRTUAL',
}
registerEnumType(ActivityType, {
  name: 'ActivityType',
  description: 'The type of activity',
})

@ObjectType()
@InputType('ActivationFilterInput')
export class ActivationFilter {
  @Field(() => [String], { nullable: true, description: 'The labels of the activation' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The labels of the activation' })
  labels?: string[]

  @Field(() => [ObjectId], { nullable: true, description: 'The IDs of the products' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The IDs of the products' })
  productIds?: ObjectId[]

  @Field(() => [ObjectId], { nullable: true, description: 'The IDs of the programs' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The IDs of the programs' })
  programIds?: ObjectId[]

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The minimum date of registration' })
  @ApiProperty({ type: Date, required: false, description: 'The minimum date of registration' })
  registeredMinDate?: Date

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The maximum date of registration' })
  @ApiProperty({ type: Date, required: false, description: 'The maximum date of registration' })
  registeredMaxDate?: Date

  @Field(() => Iso3166Alpha2, { nullable: true, description: 'The countries of the activation' })
  @ApiProperty({ enum: Iso3166Alpha2, isArray: true, required: false, description: 'The countries of the activation' })
  countries?: Iso3166Alpha2[]
}

@ObjectType()
@Directive('@key(fields: "_id")')
@EntityType<Activation>({
  relations: {
    participantIds: 'User',
  },

  stats: true,

  paginated: () => PaginatedActivations,
})
export class Activation extends MongoDBEntity {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => String, { description: 'Name of the activation' })
  @ApiProperty({ type: String, description: 'Name of the activation' })
  name: string

  @Field(() => String, {
    description: 'URL for an external page that should have relevant information for the Activation',
  })
  @ApiProperty({
    type: String,
    description: 'URL for an external page that should have relevant information for the Activation',
  })
  url: string

  @Field(() => ActivityType, { nullable: true, description: 'The type of the activity' })
  @ApiProperty({ enum: ActivityType, required: false, description: 'The type of the activity' })
  type: ActivityType

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the activation starts.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the activation starts.' })
  runFrom?: Date

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the activation ends.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the activation ends.' })
  runTo?: Date

  @Field(() => ActivationFilter, { description: 'The filter for the activation' })
  @ApiProperty({ type: ActivationFilter, description: 'The filter for the activation' })
  filter: ActivationFilter

  @Field(() => Number, { nullable: true, description: 'The limit of participants' })
  @ApiProperty({ type: Number, required: false, description: 'The limit of participants' })
  limit?: number

  @Field(() => [ObjectId], { description: 'The IDs of the participants' })
  @ApiProperty({ type: String, isArray: true, description: 'The IDs of the participants' })
  participantIds: ObjectId[]

  @Field(() => String, { nullable: true, description: 'The URL to the picture of the activation' })
  @ApiProperty({ type: String, required: false, description: 'The URL to the picture of the activation' })
  picture?: string

  @Field(() => String, { nullable: true, description: 'The description of the activation' })
  @ApiProperty({ type: String, required: false, description: 'The description of the activation' })
  description?: string

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The date of the activation' })
  @ApiProperty({ type: Date, required: false, description: 'The date of the activation' })
  date?: Date

  @Field(() => String, { nullable: true, description: 'The location of the activation' })
  @ApiProperty({ type: String, required: false, description: 'The location of the activation' })
  location?: string
}

@ObjectType()
export class PaginatedActivations extends paginated(Activation) {}

@InputType()
export class CreateActivation {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => String, { description: 'Name of the activation' })
  @ApiProperty({ type: String, description: 'Name of the activation' })
  name: string

  @Field(() => String, {
    description: 'URL for an external page that should have relevant information for the Activation',
  })
  @ApiProperty({
    type: String,
    description: 'URL for an external page that should have relevant information for the Activation',
  })
  url: string

  @Field(() => ActivityType, { nullable: true, description: 'The type of the activity' })
  @ApiProperty({ enum: ActivityType, required: false, description: 'The type of the activity' })
  type: ActivityType

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the activation starts.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the activation starts.' })
  runFrom?: Date

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the activation ends.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the activation ends.' })
  runTo?: Date

  @Field(() => ActivationFilter, { description: 'The filter for the activation' })
  @ApiProperty({ type: ActivationFilter, description: 'The filter for the activation' })
  filter: ActivationFilter

  @Field(() => Number, { nullable: true, description: 'The limit of participants' })
  @ApiProperty({ type: Number, required: false, description: 'The limit of participants' })
  limit?: number

  @Field(() => String, { nullable: true, description: 'The URL to the picture of the activation' })
  @ApiProperty({ type: String, required: false, description: 'The URL to the picture of the activation' })
  picture?: string

  @Field(() => String, { nullable: true, description: 'The description of the activation' })
  @ApiProperty({ type: String, required: false, description: 'The description of the activation' })
  description?: string

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The date of the activation' })
  @ApiProperty({ type: Date, required: false, description: 'The date of the activation' })
  date?: Date

  @Field(() => String, { nullable: true, description: 'The location of the activation' })
  @ApiProperty({ type: String, required: false, description: 'The location of the activation' })
  location?: string
}

@InputType()
export class ActivationSearch {
  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Organization.',
  })
  organizationId?: ObjectId

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Tag.',
  })
  tagId?: ObjectId

  @Field(() => Boolean, { nullable: true, description: 'Whether the user is a participant' })
  @ApiProperty({ type: Boolean, required: false, description: 'Whether the user is a participant' })
  participant?: boolean

  @Field(() => Boolean, { nullable: true, description: 'Whether the activation is available' })
  @ApiProperty({ type: Boolean, required: false, description: 'Whether the activation is available' })
  available?: boolean

  @Field(() => Boolean, { nullable: true, description: 'Whether the activation is active' })
  @ApiProperty({ type: Boolean, required: false, description: 'Whether the activation is active' })
  active?: boolean

  @Field(() => Boolean, { nullable: true, description: 'Whether the activation is inactive' })
  @ApiProperty({ type: Boolean, required: false, description: 'Whether the activation is inactive' })
  inactive?: boolean

  @Field(() => [String], { nullable: true, description: 'The labels of the activation' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The labels of the activation' })
  labels?: string[]

  @Field(() => [ObjectId], { nullable: true, description: 'The IDs of the products' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The IDs of the products' })
  productIds?: ObjectId[]

  @Field(() => [ObjectId], { nullable: true, description: 'The IDs of the programs' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The IDs of the programs' })
  programIds?: ObjectId[]
}
