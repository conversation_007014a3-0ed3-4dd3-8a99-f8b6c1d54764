import { Directive, Field, InputType, ObjectType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import { EntityType, paginated } from '@phigital-loyalty/core'
import { FieldObjectID, MongoDBEntity } from '@phigital-loyalty/database'

@ObjectType()
@InputType('ProductDetailsInput')
export class ProductDetails {
  @Field(() => String, { nullable: true, description: 'Stock Keeping Unit - unique identifier for a product' })
  @ApiProperty({ type: String, required: false, description: 'Stock Keeping Unit - unique identifier for a product' })
  sku?: string

  @Field(() => String, { nullable: true, description: 'Unique identifier assigned to an individual product unit' })
  @ApiProperty({
    type: String,
    required: false,
    description: 'Unique identifier assigned to an individual product unit',
  })
  serialNumber?: string

  @Field(() => String, { nullable: true, description: 'Batch or lot number for manufacturing identification' })
  @ApiProperty({ type: String, required: false, description: 'Batch or lot number for manufacturing identification' })
  batch?: string

  @Field(() => String, { nullable: true, description: 'Color of the product' })
  @ApiProperty({ type: String, required: false, description: 'Color of the product' })
  color?: string

  @Field(() => String, { nullable: true, description: 'Size specification of the product' })
  @ApiProperty({ type: String, required: false, description: 'Size specification of the product' })
  size?: string

  @Field(() => String, { nullable: true, description: 'URL to product picture' })
  @ApiProperty({ type: String, required: false, description: 'URL to product picture' })
  picture?: string

  @Field(() => String, { nullable: true, description: 'Url for an image of a stamp or certificate for the product' })
  @ApiProperty({
    type: String,
    required: false,
    description: 'Url for an image of a stamp or certificate for the product',
  })
  stamp?: string
}

@ObjectType()
@Directive('@key(fields: "_id")')
@EntityType<Product>({
  stats: true,

  paginated: () => PaginatedProducts,
})
export class Product extends MongoDBEntity {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => String, { description: 'Name of the product' })
  @ApiProperty({ type: String, description: 'Name of the product' })
  name: string

  @Field(() => String, { nullable: true, description: 'URL to the main product image' })
  @ApiProperty({ type: String, required: false, description: 'URL to the main product image' })
  picture?: string

  @Field(() => String, { nullable: true, description: 'Detailed description of the product' })
  @ApiProperty({ type: String, required: false, description: 'Detailed description of the product' })
  description?: string

  @Field(() => String, { nullable: true, description: 'A URL of a publicly-accessible webpage for this product.' })
  @ApiProperty({
    type: String,
    required: false,
    description: 'A URL of a publicly-accessible webpage for this product.',
  })
  url?: string
}

@ObjectType()
export class PaginatedProducts extends paginated(Product) {}
