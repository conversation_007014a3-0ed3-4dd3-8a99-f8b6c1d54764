import { Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Collection } from 'mongodb'

import { InjectCollection, InjectDataloader, MongoDBLoader, MongoDBEntityService } from '@phigital-loyalty/database'

import { Product } from './product.entity'

@Injectable()
export class ProductService extends MongoDBEntityService<Product> {
  constructor(
    @InjectCollection('product') collection: Collection<Product>,
    @InjectDataloader('product') dataloader: MongoDBLoader<Product>,
    moduleRef: ModuleRef,
  ) {
    super(Product, collection, dataloader, moduleRef)
  }
}
