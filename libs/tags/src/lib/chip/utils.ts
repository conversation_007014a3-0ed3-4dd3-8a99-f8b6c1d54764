import { randomInt } from 'crypto'

export function checkCounter(counter: number, previousCounterValue = 0, _acceptableRange = 1): boolean {
  // Ensure the counter value is incrementing and within an acceptable range
  if (isNaN(counter)) {
    throw new Error('Counter value is not a number')
  }

  if (counter <= previousCounterValue) {
    throw new Error('Counter value is not incrementing')
  }

  // TODO: implement a smart flagging system based on diferent acceptable ranges
  // if (counter > previousCounterValue + acceptableRange) {
  //   throw new Error('Counter value exceeds acceptable range')
  // }

  return true
}

export function generateSecurePassword(length: number) {
  // Define the characters to include in the password
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+[]{}|;:,.<>?'

  // Generate a random password
  let password = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = randomInt(0, charset.length)
    password += charset[randomIndex]
  }

  return password
}
