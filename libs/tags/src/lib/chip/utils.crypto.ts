import { createCipheriv } from 'crypto'

// CMAC (AES-128-CMAC)
export function calculateCmac(message: string, key: Buffer): string {
  // Create an AES-128-CMAC instance
  const cipher = createCipheriv('aes-128-ecb', key, Buffer.alloc(0))
  cipher.setAutoPadding(false)

  // Calculate the CMAC
  const messageBuffer = Buffer.from(message, 'utf8')
  const paddedMessage = padMessage(messageBuffer)

  let cmac = Buffer.alloc(16, 0)
  for (let i = 0; i < paddedMessage.length / 16; i++) {
    const block = paddedMessage.slice(i * 16, (i + 1) * 16)
    const xorResult = xorBuffers(cmac, block)
    cmac = cipher.update(xorResult)
  }
  cmac = cipher.final()

  return cmac.toString('hex')
}

function padMessage(buffer: Buffer): Buffer {
  const padLength = 16 - (buffer.length % 16)
  return Buffer.concat([buffer, Buffer.alloc(padLength, 0)])
}

function xorBuffers(buf1: Buffer, buf2: Buffer): Buffer {
  const length = Math.min(buf1.length, buf2.length)
  const result = Buffer.alloc(length)
  for (let i = 0; i < length; i++) {
    result[i] = buf1[i] ^ buf2[i]
  }
  return result
}
