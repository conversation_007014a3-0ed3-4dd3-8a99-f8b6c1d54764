import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'
import { randomNumericString } from './utils'

import type { EPCComponentsBase } from './epc.entities'

/**
 * SGLN-96 Components Interface
 */
export interface SGLN96Components extends EPCComponentsBase {
  scheme: EPCScheme.SGLN96
  filter: number
  companyPrefix: string
  locationReference: string
  extension: string
}

/**
 * Encode SGLN-96
 */
export function encodeSGLN96(data: SGLN96Components): string {
  // Header
  const header = '00110011' // SGLN-96 header (binary for 0x33)

  // Validations
  if (data.filter < 0 || data.filter > 7) {
    throw new Error('Filter value must be between 0 and 7.')
  }

  if (!/^\d+$/.test(data.companyPrefix)) {
    throw new Error('Company Prefix must be a numeric string.')
  }

  if (!/^\d+$/.test(data.locationReference)) {
    throw new Error('Location Reference must be a numeric string.')
  }

  if (!/^\d+$/.test(data.extension)) {
    throw new Error('Extension must be a numeric string.')
  }

  // Determine partition based on the length of the company prefix
  const companyPrefixDigits = data.companyPrefix.length

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, lrDigits: 0, lrBits: 1 },
    { partition: 1, cpDigits: 11, cpBits: 37, lrDigits: 1, lrBits: 4 },
    { partition: 2, cpDigits: 10, cpBits: 34, lrDigits: 2, lrBits: 7 },
    { partition: 3, cpDigits: 9, cpBits: 30, lrDigits: 3, lrBits: 11 },
    { partition: 4, cpDigits: 8, cpBits: 27, lrDigits: 4, lrBits: 14 },
    { partition: 5, cpDigits: 7, cpBits: 24, lrDigits: 5, lrBits: 17 },
    { partition: 6, cpDigits: 6, cpBits: 20, lrDigits: 6, lrBits: 21 },
  ]

  const partitionInfo = partitionTable.find((p) => p.cpDigits === companyPrefixDigits)

  if (!partitionInfo) {
    throw new Error('Invalid Company Prefix length for SGLN-96.')
  }

  const partition = partitionInfo.partition
  const { cpBits, lrBits } = partitionInfo

  // Convert fields to binary strings
  const filterBin = data.filter.toString(2).padStart(3, '0')
  const partitionBin = partition.toString(2).padStart(3, '0')

  const companyPrefixNumber = BigInt(data.companyPrefix)
  if (companyPrefixNumber >= 1n << BigInt(cpBits)) {
    throw new Error(`Company Prefix exceeds ${cpBits} bits.`)
  }
  const companyPrefixBin = companyPrefixNumber.toString(2).padStart(cpBits, '0')

  const locationReferenceNumber = BigInt(data.locationReference)
  if (locationReferenceNumber >= 1n << BigInt(lrBits)) {
    throw new Error(`Location Reference exceeds ${lrBits} bits.`)
  }
  const locationReferenceBin = locationReferenceNumber.toString(2).padStart(lrBits, '0')

  const extensionNumber = BigInt(data.extension)
  if (extensionNumber >= 1n << 41n) {
    throw new Error('Extension exceeds 41 bits.')
  }
  const extensionBin = extensionNumber.toString(2).padStart(41, '0') // Extension is 41 bits

  // Combine all parts
  const epcBinary = header + filterBin + partitionBin + companyPrefixBin + locationReferenceBin + extensionBin

  // Validate total length
  if (epcBinary.length !== 96) {
    throw new Error('Combined EPC components should result in 96 bits for SGLN-96.')
  }

  // Convert binary string to hexadecimal EPC
  const epcHex = BigInt('0b' + epcBinary)
    .toString(16)
    .toUpperCase()
    .padStart(24, '0')

  return epcHex
}

/**
 * Decode SGLN-96
 */
export function decodeSGLN96(epc: string): SGLN96Components {
  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract fields
  // const header = epcBinary.substring(0, 8)
  const filterBin = epcBinary.substring(8, 11)
  const partitionBin = epcBinary.substring(11, 14)
  const partition = parseInt(partitionBin, 2)

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, lrDigits: 1, lrBits: 4 },
    { partition: 1, cpDigits: 11, cpBits: 37, lrDigits: 2, lrBits: 7 },
    { partition: 2, cpDigits: 10, cpBits: 34, lrDigits: 3, lrBits: 10 },
    { partition: 3, cpDigits: 9, cpBits: 30, lrDigits: 4, lrBits: 14 },
    { partition: 4, cpDigits: 8, cpBits: 27, lrDigits: 5, lrBits: 17 },
    { partition: 5, cpDigits: 7, cpBits: 24, lrDigits: 6, lrBits: 20 },
    { partition: 6, cpDigits: 6, cpBits: 20, lrDigits: 7, lrBits: 24 },
  ]

  const partitionInfo = partitionTable.find((p) => p.partition === partition)

  if (!partitionInfo) {
    throw new Error('Invalid partition value in EPC.')
  }

  const { cpBits, lrBits, cpDigits, lrDigits } = partitionInfo

  const filter = parseInt(filterBin, 2)

  const companyPrefixBin = epcBinary.substring(14, 14 + cpBits)
  const locationReferenceBin = epcBinary.substring(14 + cpBits, 14 + cpBits + lrBits)
  const extensionBin = epcBinary.substring(14 + cpBits + lrBits)

  const companyPrefixNumber = BigInt('0b' + companyPrefixBin)
  const locationReferenceNumber = BigInt('0b' + locationReferenceBin)
  const extensionNumber = BigInt('0b' + extensionBin)

  const companyPrefix = companyPrefixNumber.toString().padStart(cpDigits, '0')
  const locationReference = locationReferenceNumber.toString().padStart(lrDigits, '0')
  const extension = extensionNumber.toString()

  return {
    scheme: EPCScheme.SGLN96,
    filter,
    companyPrefix,
    locationReference,
    extension,
  }
}

/**
 * Generate Random SGLN-96 Components
 */
export function generateRandomSGLN96Components(): SGLN96Components {
  // Randomly select a partition
  const partitionTable = [
    { partition: 0, cpDigits: 12, lrDigits: 0 },
    { partition: 1, cpDigits: 11, lrDigits: 1 },
    { partition: 2, cpDigits: 10, lrDigits: 2 },
    { partition: 3, cpDigits: 9, lrDigits: 3 },
    { partition: 4, cpDigits: 8, lrDigits: 4 },
    { partition: 5, cpDigits: 7, lrDigits: 5 },
    { partition: 6, cpDigits: 6, lrDigits: 6 },
  ]

  const partitionInfo = partitionTable[randomInt(0, partitionTable.length)]

  // Generate random numeric strings for company prefix and location reference
  const companyPrefix = randomNumericString(partitionInfo.cpDigits)
  const locationReference = partitionInfo.lrDigits > 0 ? randomNumericString(partitionInfo.lrDigits) : '0'

  // Generate a random extension (up to 41 bits)
  const extension = randomInt(0, Math.pow(2, 41) - 1).toString()

  // Generate a random filter value between 0 and 7
  const filter = randomInt(0, 8)

  return {
    scheme: EPCScheme.SGLN96,
    filter,
    companyPrefix,
    locationReference,
    extension,
  }
}
