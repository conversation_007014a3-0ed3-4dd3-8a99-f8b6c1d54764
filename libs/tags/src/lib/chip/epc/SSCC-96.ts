import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'

// Random numeric string generator that preserves leading zeros
function randomNumericString(length: number): string {
  let result = ''
  while (result.length < length) {
    const randomDigit = randomInt(0, 10).toString()
    result += randomDigit
  }
  return result.slice(0, length)
}

// SSCC-96 Components Interface
export interface SSCC96Components {
  scheme: EPCScheme.SSCC96
  filter: number
  companyPrefix: string
  serialReference: string
  extensionDigit: number
}

const PARTITION_TABLE = [
  { partition: 0, cpDigits: 12, cpBits: 40, srDigits: 5, srBits: 18 },
  { partition: 1, cpDigits: 11, cpBits: 37, srDigits: 6, srBits: 21 },
  { partition: 2, cpDigits: 10, cpBits: 34, srDigits: 7, srBits: 24 },
  { partition: 3, cpDigits: 9, cpBits: 30, srDigits: 8, srBits: 28 },
  { partition: 4, cpDigits: 8, cpBits: 27, srDigits: 9, srBits: 31 },
  { partition: 5, cpDigits: 7, cpBits: 24, srDigits: 10, srBits: 34 },
]

export function encodeSSCC96(data: SSCC96Components): string {
  // Header
  const header = '00110001' // SSCC-96 header (binary for 0x31)

  // Validations
  if (data.filter < 0 || data.filter > 7) {
    throw new Error('Filter value must be between 0 and 7.')
  }

  if (data.extensionDigit < 0 || data.extensionDigit > 9) {
    throw new Error('Extension digit must be between 0 and 9.')
  }

  if (!/^\d+$/.test(data.companyPrefix)) {
    throw new Error('Company Prefix must be a numeric string.')
  }

  if (!/^\d+$/.test(data.serialReference)) {
    throw new Error('Serial Reference must be a numeric string.')
  }

  // Determine partition based on the length of the company prefix
  const companyPrefixDigits = data.companyPrefix.length

  const partitionInfo = PARTITION_TABLE.find((p) => p.cpDigits === companyPrefixDigits)

  if (!partitionInfo) {
    throw new Error('Invalid Company Prefix length for SSCC-96.')
  }

  const { partition, cpBits, srBits, srDigits } = partitionInfo

  // Validate serialReference length
  if (data.serialReference.length !== srDigits - 1) {
    throw new Error(`Serial Reference must be exactly ${srDigits - 1} digits.`)
  }

  // Combine Extension Digit and Serial Reference
  const serialReferencePadded = data.serialReference // Already validated for length
  const fullSerialReference = data.extensionDigit.toString() + serialReferencePadded

  if (fullSerialReference.length !== srDigits) {
    throw new Error(`Combined Extension Digit and Serial Reference must be ${srDigits} digits.`)
  }

  // Convert fields to binary strings
  const filterBin = data.filter.toString(2).padStart(3, '0')
  const partitionBin = partition.toString(2).padStart(3, '0')

  const companyPrefixNumber = BigInt(data.companyPrefix)
  if (companyPrefixNumber >= 1n << BigInt(cpBits)) {
    throw new Error(`Company Prefix exceeds ${cpBits} bits.`)
  }
  const companyPrefixBin = companyPrefixNumber.toString(2).padStart(cpBits, '0')

  const serialReferenceNumber = BigInt(fullSerialReference)
  if (serialReferenceNumber >= 1n << BigInt(srBits)) {
    throw new Error(`Serial Reference exceeds ${srBits} bits.`)
  }
  const serialReferenceBin = serialReferenceNumber.toString(2).padStart(srBits, '0')

  // Combine all parts
  const epcBinary = header + filterBin + partitionBin + companyPrefixBin + serialReferenceBin

  // Validate total length
  if (epcBinary.length !== 96) {
    throw new Error('Combined EPC components should result in 96 bits for SSCC-96.')
  }

  // Convert binary string to hexadecimal EPC
  const epcHex = BigInt('0b' + epcBinary)
    .toString(16)
    .toUpperCase()
    .padStart(24, '0')

  return epcHex
}

/**
 * Decode SSCC-96
 */
export function decodeSSCC96(epc: string): SSCC96Components {
  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract fields
  // const header = epcBinary.substring(0, 8)
  const filterBin = epcBinary.substring(8, 11)
  const partitionBin = epcBinary.substring(11, 14)
  const partition = parseInt(partitionBin, 2)

  const partitionInfo = PARTITION_TABLE.find((p) => p.partition === partition)

  if (!partitionInfo) {
    throw new Error('Invalid partition value in EPC.')
  }

  const { cpBits, srBits, cpDigits, srDigits } = partitionInfo

  const filter = parseInt(filterBin, 2)

  const extensionDigitBin = epcBinary.substring(14, 17)
  const extensionDigit = parseInt(extensionDigitBin, 2)

  const companyPrefixBin = epcBinary.substring(17, 17 + cpBits)
  const serialReferenceBin = epcBinary.substring(17 + cpBits, 17 + cpBits + srBits)

  const companyPrefixNumber = BigInt('0b' + companyPrefixBin)
  const serialReferenceNumber = BigInt('0b' + serialReferenceBin)

  const companyPrefix = companyPrefixNumber.toString().padStart(cpDigits, '0')
  const serialReference = serialReferenceNumber.toString().padStart(srDigits, '0')

  return {
    scheme: EPCScheme.SSCC96,
    filter,
    extensionDigit,
    companyPrefix,
    serialReference,
  }
}

export function generateRandomSSCC96Components(): SSCC96Components {
  const partitionInfo = PARTITION_TABLE[randomInt(0, PARTITION_TABLE.length)]
  const { cpDigits, srDigits } = partitionInfo

  // Generate random numeric string for company prefix
  const companyPrefix = randomNumericString(cpDigits)

  // Generate a random extension digit between 0 and 9
  const extensionDigit = randomInt(0, 10) // 0 to 9 inclusive

  // Generate serial reference digits (excluding extension digit)
  const serialReferenceDigits = srDigits - 1 // Subtract 1 for extension digit
  const serialReference = randomNumericString(serialReferenceDigits)

  // Generate a random filter value between 0 and 7
  const filter = randomInt(0, 8) // 0 to 7 inclusive

  return {
    scheme: EPCScheme.SSCC96,
    filter,
    extensionDigit,
    companyPrefix,
    serialReference,
  }
}
