import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'

import type { EPCComponentsBase } from './epc.entities'

// GID-96 Components Interface
export interface GID96Components extends EPCComponentsBase {
  scheme: EPCScheme.GID96
  generalManagerNumber: string
  objectClass: string
  serialNumber: string
}

/**
 * Encode GID-96
 */
export function encodeGID96(data: GID96Components): string {
  // Header
  const header = '00110010' // GID-96 header

  // Validations
  if (!/^\d+$/.test(data.generalManagerNumber)) {
    throw new Error('General Manager Number must be a numeric string.')
  }

  if (!/^\d+$/.test(data.objectClass)) {
    throw new Error('Object Class must be a numeric string.')
  }

  if (!/^\d+$/.test(data.serialNumber)) {
    throw new Error('Serial Number must be a numeric string.')
  }

  const generalManagerNumberNumber = BigInt(data.generalManagerNumber)
  if (generalManagerNumberNumber < 0n || generalManagerNumberNumber > 268_435_455n) {
    // 2^28 - 1
    throw new Error('General Manager Number must be between 0 and 268,435,455.')
  }
  const generalManagerNumberBin = generalManagerNumberNumber.toString(2).padStart(28, '0')

  const objectClassNumber = BigInt(data.objectClass)
  if (objectClassNumber < 0n || objectClassNumber > 16_777_215n) {
    // 2^24 - 1
    throw new Error('Object Class must be between 0 and 16,777,215.')
  }
  const objectClassBin = objectClassNumber.toString(2).padStart(24, '0')

  const serialNumberNumber = BigInt(data.serialNumber)
  if (serialNumberNumber < 0n || serialNumberNumber > 68_719_476_735n) {
    // 2^36 - 1
    throw new Error('Serial Number must be between 0 and 68,719,476,735.')
  }
  const serialNumberBin = serialNumberNumber.toString(2).padStart(36, '0')

  // Combine all parts
  const epcBinary = header + generalManagerNumberBin + objectClassBin + serialNumberBin

  // Validate total length
  if (epcBinary.length !== 96) {
    throw new Error('Combined EPC components should result in 96 bits for GID-96.')
  }

  // Convert binary string to hexadecimal EPC
  const epcHex = BigInt('0b' + epcBinary)
    .toString(16)
    .toUpperCase()
    .padStart(24, '0')

  return epcHex
}

/**
 * Decode GID-96
 */
export function decodeGID96(epc: string): GID96Components {
  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract fields
  // const header = epcBinary.substring(0, 8);
  const generalManagerNumberBin = epcBinary.substring(8, 36)
  const objectClassBin = epcBinary.substring(36, 60)
  const serialNumberBin = epcBinary.substring(60)

  const generalManagerNumber = BigInt('0b' + generalManagerNumberBin).toString()
  const objectClass = BigInt('0b' + objectClassBin).toString()
  const serialNumber = BigInt('0b' + serialNumberBin).toString()

  return {
    scheme: EPCScheme.GID96,
    generalManagerNumber,
    objectClass,
    serialNumber,
  }
}

/**
 * Generate Random GID-96 Components
 */
export function generateRandomGID96Components(): GID96Components {
  // Generate random numbers within valid ranges
  const generalManagerNumber = randomInt(0, 268_435_455).toString() // 2^28 - 1
  const objectClass = randomInt(0, 16_777_215).toString() // 2^24 - 1
  const serialNumber = randomInt(0, 68_719_476_735).toString() // 2^36 - 1

  return {
    scheme: EPCScheme.GID96,
    generalManagerNumber,
    objectClass,
    serialNumber,
  }
}
