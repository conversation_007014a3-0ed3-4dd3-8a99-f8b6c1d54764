import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'
import { randomNumericString } from './utils'

import type { EPCComponentsBase } from './epc.entities'

// SGTIN-96 Components Interface
export interface SGTIN96Components extends EPCComponentsBase {
  scheme: EPCScheme.SGTIN96
  filter: number
  companyPrefix: string
  itemReference: string
  serialNumber: string
}

/**
 * Encode SGTIN-96
 */
export function encodeSGTIN96(data: SGTIN96Components): string {
  // Header
  const header = '00110000' // SGTIN-96 header

  // Validations
  if (data.filter < 0 || data.filter > 7) {
    throw new Error('Filter value must be between 0 and 7.')
  }

  if (!/^\d+$/.test(data.companyPrefix)) {
    throw new Error('Company Prefix must be a numeric string.')
  }

  if (!/^\d+$/.test(data.itemReference)) {
    throw new Error('Item Reference must be a numeric string.')
  }

  if (!/^\d+$/.test(data.serialNumber)) {
    throw new Error('Serial Number must be a numeric string.')
  }

  // Determine partition based on the length of the company prefix
  const companyPrefixDigits = data.companyPrefix.length
  let partition = null

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, irBits: 4 },
    { partition: 1, cpDigits: 11, cpBits: 37, irBits: 7 },
    { partition: 2, cpDigits: 10, cpBits: 34, irBits: 10 },
    { partition: 3, cpDigits: 9, cpBits: 30, irBits: 14 },
    { partition: 4, cpDigits: 8, cpBits: 27, irBits: 17 },
    { partition: 5, cpDigits: 7, cpBits: 24, irBits: 20 },
    { partition: 6, cpDigits: 6, cpBits: 20, irBits: 24 },
  ]

  const partitionInfo = partitionTable.find((p) => p.cpDigits === companyPrefixDigits)

  if (!partitionInfo) {
    throw new Error('Invalid Company Prefix length for SGTIN-96.')
  }

  partition = partitionInfo.partition
  const { cpBits, irBits } = partitionInfo

  // Convert fields to binary strings
  const filterBin = data.filter.toString(2).padStart(3, '0')
  const partitionBin = partition.toString(2).padStart(3, '0')

  const companyPrefixNumber = BigInt(data.companyPrefix)
  const companyPrefixBin = companyPrefixNumber.toString(2).padStart(cpBits, '0')

  const itemReferenceNumber = BigInt(data.itemReference)
  const itemReferenceBin = itemReferenceNumber.toString(2).padStart(irBits, '0')

  const serialNumberNumber = BigInt(data.serialNumber)
  if (serialNumberNumber < BigInt(0) || serialNumberNumber > BigInt('274877906943')) {
    // 2^38 - 1
    throw new Error('Serial Number must be between 0 and 274,877,906,943.')
  }
  const serialNumberBin = serialNumberNumber.toString(2).padStart(38, '0')

  // Combine all parts
  const epcBinary = header + filterBin + partitionBin + companyPrefixBin + itemReferenceBin + serialNumberBin

  // Validate total length
  if (epcBinary.length !== 96) {
    throw new Error('Combined EPC components should result in 96 bits for SGTIN-96.')
  }

  // Convert binary string to hexadecimal EPC
  const epcHex = BigInt('0b' + epcBinary)
    .toString(16)
    .toUpperCase()
    .padStart(24, '0')

  return epcHex
}

/**
 * Decode SGTIN-96
 */
export function decodeSGTIN96(epc: string): SGTIN96Components {
  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract fields
  // const header = epcBinary.substring(0, 8)
  const filterBin = epcBinary.substring(8, 11)
  const partitionBin = epcBinary.substring(11, 14)
  const partition = parseInt(partitionBin, 2)

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, irDigits: 1, irBits: 4 },
    { partition: 1, cpDigits: 11, cpBits: 37, irDigits: 2, irBits: 7 },
    { partition: 2, cpDigits: 10, cpBits: 34, irDigits: 3, irBits: 10 },
    { partition: 3, cpDigits: 9, cpBits: 30, irDigits: 4, irBits: 14 },
    { partition: 4, cpDigits: 8, cpBits: 27, irDigits: 5, irBits: 17 },
    { partition: 5, cpDigits: 7, cpBits: 24, irDigits: 6, irBits: 20 },
    { partition: 6, cpDigits: 6, cpBits: 20, irDigits: 7, irBits: 24 },
  ]

  const partitionInfo = partitionTable.find((p) => p.partition === partition)

  if (!partitionInfo) {
    throw new Error('Invalid partition value in EPC.')
  }

  const { cpBits, irBits, cpDigits, irDigits } = partitionInfo

  const filter = parseInt(filterBin, 2)

  const companyPrefixBin = epcBinary.substring(14, 14 + cpBits)
  const itemReferenceBin = epcBinary.substring(14 + cpBits, 14 + cpBits + irBits)
  const serialNumberBin = epcBinary.substring(14 + cpBits + irBits)

  const companyPrefixNumber = BigInt('0b' + companyPrefixBin)
  const itemReferenceNumber = BigInt('0b' + itemReferenceBin)
  const serialNumberNumber = BigInt('0b' + serialNumberBin)

  const companyPrefix = companyPrefixNumber.toString().padStart(cpDigits, '0')
  const itemReference = itemReferenceNumber.toString().padStart(irDigits, '0')
  const serialNumber = serialNumberNumber.toString()

  return {
    scheme: EPCScheme.SGTIN96,
    filter,
    companyPrefix,
    itemReference,
    serialNumber,
  }
}

export function generateRandomSGTIN96Components(): SGTIN96Components {
  // Randomly select a partition
  const partitionTable = [
    { partition: 0, cpDigits: 12, irDigits: 1 },
    { partition: 1, cpDigits: 11, irDigits: 2 },
    { partition: 2, cpDigits: 10, irDigits: 3 },
    { partition: 3, cpDigits: 9, irDigits: 4 },
    { partition: 4, cpDigits: 8, irDigits: 5 },
    { partition: 5, cpDigits: 7, irDigits: 6 },
    { partition: 6, cpDigits: 6, irDigits: 7 },
  ]

  const partitionInfo = partitionTable[randomInt(0, partitionTable.length - 1)]

  // Generate random numeric strings for company prefix and item reference
  const companyPrefix = randomNumericString(partitionInfo.cpDigits)
  const itemReference = randomNumericString(partitionInfo.irDigits)

  // Generate a random serial number (0 to 2^38 - 1)
  const serialNumber = randomInt(0, 274_877_906_943).toString()

  // Generate a random filter value between 0 and 7
  const filter = randomInt(0, 7)

  return {
    scheme: EPCScheme.SGTIN96,
    filter,
    companyPrefix,
    itemReference,
    serialNumber,
  }
}
