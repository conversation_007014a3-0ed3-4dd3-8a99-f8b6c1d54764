import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'
import { encodeGIAI96, decodeGIAI96, generateRandomGIAI96Components } from './GIAI-96'
import { encodeGID96, decodeGID96, generateRandomGID96Components } from './GID-96'
import { encodeGRAI96, decodeGRAI96, generateRandomGRAI96Components } from './GRAI-96'
import { encodeSGLN96, decodeSGLN96, generateRandomSGLN96Components } from './SGLN-96'
import { encodeSGTIN96, decodeSGTIN96, generateRandomSGTIN96Components } from './SGTIN-96'
import { encodeSSCC96, decodeSSCC96 } from './SSCC-96'

import type { GIAI96Components } from './GIAI-96'
import type { GID96Components } from './GID-96'
import type { GRAI96Components } from './GRAI-96'
import type { SGLN96Components } from './SGLN-96'
import type { SGTIN96Components } from './SGTIN-96'
import type { SSCC96Components } from './SSCC-96'

export * from './epc.entities'

export type EPCComponents =
  | SGTIN96Components
  | SSCC96Components
  | GID96Components
  | SGLN96Components
  | GRAI96Components
  | GIAI96Components

/**
 * Encode EPC based on the scheme
 */
export function encodeEPC(data: EPCComponents): string {
  switch (data.scheme) {
    case 'SGTIN-96':
      return encodeSGTIN96(data as SGTIN96Components)
    case 'SSCC-96':
      return encodeSSCC96(data as SSCC96Components)
    case 'GID-96':
      return encodeGID96(data as GID96Components)
    case 'SGLN-96':
      return encodeSGLN96(data as SGLN96Components)
    case 'GRAI-96':
      return encodeGRAI96(data as GRAI96Components)
    case 'GIAI-96':
      return encodeGIAI96(data as GIAI96Components)
    default:
      throw new Error('Unsupported EPC scheme.')
  }
}

/**
 * Decode EPC and determine the scheme based on the header
 */
export function decodeEPC(epc: string): EPCComponents {
  // Validate EPC length
  if (epc.length !== 24) {
    throw new Error('EPC code should be 24 hexadecimal characters long (96 bits).')
  }

  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract header to determine scheme
  const header = epcBinary.substring(0, 8)

  switch (header) {
    case '00110000':
      return decodeSGTIN96(epc)
    case '00110001':
      return decodeSSCC96(epc)
    case '00110010':
      return decodeGID96(epc)
    case '00110011':
      return decodeSGLN96(epc)
    case '00110100':
      return decodeGRAI96(epc)
    case '00110101':
      return decodeGIAI96(epc)
    default:
      console.error(`Unsupported EPC header: ${header}`)
  }
}

// Generate EPC function
export function generateEPC(scheme?: EPCScheme): { epc: string; components: EPCComponents } {
  // If scheme is not specified, select one at random
  const schemes: EPCScheme[] = [
    EPCScheme.SGTIN96,
    // EPCScheme.SSCC96,
    EPCScheme.GID96,
    EPCScheme.SGLN96,
    EPCScheme.GRAI96,
    EPCScheme.GIAI96,
  ]
  const selectedScheme = scheme || schemes[randomInt(0, schemes.length)]

  let components: EPCComponents

  switch (selectedScheme) {
    case 'SGTIN-96':
      components = generateRandomSGTIN96Components()
      break
      // case 'SSCC-96':
      //   components = generateRandomSSCC96Components()
      break
    case 'GID-96':
      components = generateRandomGID96Components()
      break
    case 'SGLN-96':
      components = generateRandomSGLN96Components()
      break
    case 'GRAI-96':
      components = generateRandomGRAI96Components()
      break
    case 'GIAI-96':
      components = generateRandomGIAI96Components()
      break
    default:
      throw new Error('Unsupported EPC scheme.')
  }

  const epc = encodeEPC(components)
  return { epc, components }
}
