import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'
import { randomNumericString } from './utils'

import type { EPCComponentsBase } from './epc.entities'

/**
 * GRAI-96 Components Interface
 */
export interface GRAI96Components extends EPCComponentsBase {
  scheme: EPCScheme.GRAI96
  filter: number
  companyPrefix: string
  assetType: string
  serialNumber: string
}

/**
 * Encode GRAI-96
 */
export function encodeGRAI96(data: GRAI96Components): string {
  // Header
  const header = '00110100' // GRAI-96 header (binary for 0x34)

  // Validations
  if (data.filter < 0 || data.filter > 7) {
    throw new Error('Filter value must be between 0 and 7.')
  }

  if (!/^\d+$/.test(data.companyPrefix)) {
    throw new Error('Company Prefix must be a numeric string.')
  }

  if (!/^\d+$/.test(data.assetType)) {
    throw new Error('Asset Type must be a numeric string.')
  }

  if (!/^\d+$/.test(data.serialNumber)) {
    throw new Error('Serial Number must be a numeric string.')
  }

  // Determine partition based on the length of the company prefix
  const companyPrefixDigits = data.companyPrefix.length

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, atDigits: 0, atBits: 4 },
    { partition: 1, cpDigits: 11, cpBits: 37, atDigits: 1, atBits: 7 },
    { partition: 2, cpDigits: 10, cpBits: 34, atDigits: 2, atBits: 10 },
    { partition: 3, cpDigits: 9, cpBits: 30, atDigits: 3, atBits: 14 },
    { partition: 4, cpDigits: 8, cpBits: 27, atDigits: 4, atBits: 17 },
    { partition: 5, cpDigits: 7, cpBits: 24, atDigits: 5, atBits: 20 },
    { partition: 6, cpDigits: 6, cpBits: 20, atDigits: 6, atBits: 24 },
  ]

  const partitionInfo = partitionTable.find((p) => p.cpDigits === companyPrefixDigits)

  if (!partitionInfo) {
    throw new Error('Invalid Company Prefix length for GRAI-96.')
  }

  const partition = partitionInfo.partition
  const { cpBits, atBits } = partitionInfo

  // Convert fields to binary strings
  const filterBin = data.filter.toString(2).padStart(3, '0')
  const partitionBin = partition.toString(2).padStart(3, '0')

  const companyPrefixNumber = BigInt(data.companyPrefix)
  if (companyPrefixNumber >= 1n << BigInt(cpBits)) {
    throw new Error(`Company Prefix exceeds ${cpBits} bits.`)
  }
  const companyPrefixBin = companyPrefixNumber.toString(2).padStart(cpBits, '0')

  const assetTypeNumber = BigInt(data.assetType)
  if (assetTypeNumber >= 1n << BigInt(atBits)) {
    throw new Error(`Asset Type exceeds ${atBits} bits.`)
  }
  const assetTypeBin = assetTypeNumber.toString(2).padStart(atBits, '0')

  const serialNumberNumber = BigInt(data.serialNumber)
  if (serialNumberNumber > 274_877_906_943n) {
    // 2^38 - 1
    throw new Error('Serial Number must be between 0 and 274,877,906,943.')
  }
  const serialNumberBin = serialNumberNumber.toString(2).padStart(38, '0') // Serial Number is 38 bits

  // Combine all parts
  const epcBinary = header + filterBin + partitionBin + companyPrefixBin + assetTypeBin + serialNumberBin

  // Validate total length
  if (epcBinary.length !== 96) {
    throw new Error('Combined EPC components should result in 96 bits for GRAI-96.')
  }

  // Convert binary string to hexadecimal EPC
  const epcHex = BigInt('0b' + epcBinary)
    .toString(16)
    .toUpperCase()
    .padStart(24, '0')

  return epcHex
}

/**
 * Decode GRAI-96
 */
export function decodeGRAI96(epc: string): GRAI96Components {
  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract fields
  // const header = epcBinary.substring(0, 8)
  const filterBin = epcBinary.substring(8, 11)
  const partitionBin = epcBinary.substring(11, 14)
  const partition = parseInt(partitionBin, 2)

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, atDigits: 2, atBits: 4 },
    { partition: 1, cpDigits: 11, cpBits: 37, atDigits: 3, atBits: 7 },
    { partition: 2, cpDigits: 10, cpBits: 34, atDigits: 4, atBits: 10 },
    { partition: 3, cpDigits: 9, cpBits: 30, atDigits: 5, atBits: 14 },
    { partition: 4, cpDigits: 8, cpBits: 27, atDigits: 6, atBits: 17 },
    { partition: 5, cpDigits: 7, cpBits: 24, atDigits: 7, atBits: 20 },
    { partition: 6, cpDigits: 6, cpBits: 20, atDigits: 8, atBits: 24 },
  ]

  const partitionInfo = partitionTable.find((p) => p.partition === partition)

  if (!partitionInfo) {
    throw new Error('Invalid partition value in EPC.')
  }

  const { cpBits, atBits, cpDigits, atDigits } = partitionInfo

  const filter = parseInt(filterBin, 2)

  const companyPrefixBin = epcBinary.substring(14, 14 + cpBits)
  const assetTypeBin = epcBinary.substring(14 + cpBits, 14 + cpBits + atBits)
  const serialNumberBin = epcBinary.substring(14 + cpBits + atBits)

  const companyPrefixNumber = BigInt('0b' + companyPrefixBin)
  const assetTypeNumber = BigInt('0b' + assetTypeBin)
  const serialNumberNumber = BigInt('0b' + serialNumberBin)

  const companyPrefix = companyPrefixNumber.toString().padStart(cpDigits, '0')
  const assetType = assetTypeNumber.toString().padStart(atDigits, '0')
  const serialNumber = serialNumberNumber.toString()

  return {
    scheme: EPCScheme.GRAI96,
    filter,
    companyPrefix,
    assetType,
    serialNumber,
  }
}

/**
 * Generate Random GRAI-96 Components
 */
export function generateRandomGRAI96Components(): GRAI96Components {
  // Randomly select a partition
  const partitionTable = [
    { partition: 0, cpDigits: 12, atDigits: 0 },
    { partition: 1, cpDigits: 11, atDigits: 1 },
    { partition: 2, cpDigits: 10, atDigits: 2 },
    { partition: 3, cpDigits: 9, atDigits: 3 },
    { partition: 4, cpDigits: 8, atDigits: 4 },
    { partition: 5, cpDigits: 7, atDigits: 5 },
    { partition: 6, cpDigits: 6, atDigits: 6 },
  ]

  const partitionInfo = partitionTable[randomInt(0, partitionTable.length - 1)]

  // Generate random numeric strings for company prefix and asset type
  const companyPrefix = randomNumericString(partitionInfo.cpDigits)
  const assetType = partitionInfo.atDigits > 0 ? randomNumericString(partitionInfo.atDigits) : '0'

  // Generate a random serial number (0 to 274,877,906,943)
  const serialNumber = randomInt(0, 274_877_906_943).toString()

  // Generate a random filter value between 0 and 7
  const filter = randomInt(0, 7)

  return {
    scheme: EPCScheme.GRAI96,
    filter,
    companyPrefix,
    assetType,
    serialNumber,
  }
}
