import { randomInt } from 'crypto'

import { EPCScheme } from './epc.entities'
import { randomNumericString } from './utils'

import type { EPCComponentsBase } from './epc.entities'

/**
 * GIAI-96 Components Interface
 */
export interface GIAI96Components extends EPCComponentsBase {
  scheme: EPCScheme.GIAI96
  filter: number
  companyPrefix: string
  individualAssetReference: string
}

/**
 * Encode GIAI-96
 */
export function encodeGIAI96(data: GIAI96Components): string {
  // Header
  const header = '00110101' // GIAI-96 header

  // Validations
  if (data.filter < 0 || data.filter > 7) {
    throw new Error('Filter value must be between 0 and 7.')
  }

  if (!/^\d+$/.test(data.companyPrefix)) {
    throw new Error('Company Prefix must be a numeric string.')
  }

  if (!/^\d+$/.test(data.individualAssetReference)) {
    throw new Error('Individual Asset Reference must be a numeric string.')
  }

  // Determine partition based on the length of the company prefix
  const companyPrefixDigits = data.companyPrefix.length
  let partition = null

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, iarDigits: 12, iarBits: 42 },
    { partition: 1, cpDigits: 11, cpBits: 37, iarDigits: 13, iarBits: 45 },
    { partition: 2, cpDigits: 10, cpBits: 34, iarDigits: 14, iarBits: 48 },
    { partition: 3, cpDigits: 9, cpBits: 30, iarDigits: 15, iarBits: 52 },
    { partition: 4, cpDigits: 8, cpBits: 27, iarDigits: 16, iarBits: 55 },
    { partition: 5, cpDigits: 7, cpBits: 24, iarDigits: 17, iarBits: 58 },
    { partition: 6, cpDigits: 6, cpBits: 20, iarDigits: 18, iarBits: 62 },
  ]

  const partitionInfo = partitionTable.find((p) => p.cpDigits === companyPrefixDigits)

  if (!partitionInfo) {
    throw new Error('Invalid Company Prefix length for GIAI-96.')
  }

  partition = partitionInfo.partition
  const { cpBits, iarBits } = partitionInfo

  // Convert fields to binary strings
  const filterBin = data.filter.toString(2).padStart(3, '0')
  const partitionBin = partition.toString(2).padStart(3, '0')

  const companyPrefixNumber = BigInt(data.companyPrefix)
  const companyPrefixBin = companyPrefixNumber.toString(2).padStart(cpBits, '0')

  const individualAssetReferenceNumber = BigInt(data.individualAssetReference)
  const individualAssetReferenceBin = individualAssetReferenceNumber.toString(2).padStart(iarBits, '0')

  // Combine all parts
  const epcBinary = header + filterBin + partitionBin + companyPrefixBin + individualAssetReferenceBin

  // Validate total length
  if (epcBinary.length !== 96) {
    throw new Error('Combined EPC components should result in 96 bits for GIAI-96.')
  }

  // Convert binary string to hexadecimal EPC
  const epcHex = BigInt('0b' + epcBinary)
    .toString(16)
    .toUpperCase()
    .padStart(24, '0')

  return epcHex
}

/**
 * Decode GIAI-96
 */
export function decodeGIAI96(epc: string): GIAI96Components {
  // Convert EPC hex to binary string
  const epcBinary = BigInt('0x' + epc)
    .toString(2)
    .padStart(96, '0')

  // Extract fields
  // const header = epcBinary.substring(0, 8)
  const filterBin = epcBinary.substring(8, 11)
  const partitionBin = epcBinary.substring(11, 14)
  const partition = parseInt(partitionBin, 2)

  const partitionTable = [
    { partition: 0, cpDigits: 12, cpBits: 40, iarDigits: 12, iarBits: 42 },
    { partition: 1, cpDigits: 11, cpBits: 37, iarDigits: 13, iarBits: 45 },
    { partition: 2, cpDigits: 10, cpBits: 34, iarDigits: 14, iarBits: 48 },
    { partition: 3, cpDigits: 9, cpBits: 30, iarDigits: 15, iarBits: 52 },
    { partition: 4, cpDigits: 8, cpBits: 27, iarDigits: 16, iarBits: 55 },
    { partition: 5, cpDigits: 7, cpBits: 24, iarDigits: 17, iarBits: 58 },
    { partition: 6, cpDigits: 6, cpBits: 20, iarDigits: 18, iarBits: 62 },
  ]

  const partitionInfo = partitionTable.find((p) => p.partition === partition)

  if (!partitionInfo) {
    throw new Error('Invalid partition value in EPC.')
  }

  const {
    cpBits,
    // arBits,
    cpDigits,
    iarDigits,
  } = partitionInfo

  const filter = parseInt(filterBin, 2)

  const companyPrefixBin = epcBinary.substring(14, 14 + cpBits)
  const individualAssetReferenceBin = epcBinary.substring(14 + cpBits)

  const companyPrefixNumber = BigInt('0b' + companyPrefixBin)
  const individualAssetReferenceNumber = BigInt('0b' + individualAssetReferenceBin)

  const companyPrefix = companyPrefixNumber.toString().padStart(cpDigits, '0')
  const individualAssetReference = individualAssetReferenceNumber.toString().padStart(iarDigits, '0')

  return {
    scheme: EPCScheme.GIAI96,
    filter,
    companyPrefix,
    individualAssetReference,
  }
}

/**
 * Generate Random GIAI-96 Components
 */
export function generateRandomGIAI96Components(): GIAI96Components {
  // Randomly select a partition
  const partitionTable = [
    { partition: 0, cpDigits: 12, iarDigits: 12 },
    { partition: 1, cpDigits: 11, iarDigits: 13 },
    { partition: 2, cpDigits: 10, iarDigits: 14 },
    { partition: 3, cpDigits: 9, iarDigits: 15 },
    { partition: 4, cpDigits: 8, iarDigits: 16 },
    { partition: 5, cpDigits: 7, iarDigits: 17 },
    { partition: 6, cpDigits: 6, iarDigits: 18 },
  ]

  const partitionInfo = partitionTable[randomInt(0, partitionTable.length)]

  // Generate random numeric strings for company prefix and individual asset reference
  const companyPrefix = randomNumericString(partitionInfo.cpDigits)
  const individualAssetReference = randomNumericString(partitionInfo.iarDigits)

  // Generate a random filter value between 0 and 7
  const filter = randomInt(0, 8)

  return {
    scheme: EPCScheme.GIAI96,
    filter,
    companyPrefix,
    individualAssetReference,
  }
}
