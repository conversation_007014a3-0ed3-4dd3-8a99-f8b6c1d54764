// epc.entities.ts

import { ObjectType, Field, Int, registerEnumType, createUnionType } from '@nestjs/graphql'
import { ApiProperty, getSchemaPath } from '@nestjs/swagger'

export enum EPCScheme {
  SGTIN96 = 'SGTIN-96',
  SSCC96 = 'SSCC-96',
  GID96 = 'GID-96',
  SGLN96 = 'SGLN-96',
  GRAI96 = 'GRAI-96',
  GIAI96 = 'GIAI-96',
  ISO15963 = 'ISO15963',
}
registerEnumType(EPCScheme, {
  name: 'EPCScheme',
})

// Common EPC Components Interface
export interface EPCComponentsBase {
  scheme: EPCScheme
}

@ObjectType({ description: 'Base EPC Components Interface' })
export class EPCComponents {
  @Field(() => EPCScheme)
  @ApiProperty({ enum: EPCScheme, description: 'EPC Scheme' })
  scheme: EPCScheme
}

@ObjectType({ description: 'SGTIN-96 Components' })
export class SGTIN96Components extends EPCComponents {
  @Field(() => Int)
  @ApiProperty({ description: 'Filter value (0-7)' })
  filter: number

  @Field(() => String)
  @ApiProperty({ description: 'Company Prefix' })
  companyPrefix: string

  @Field(() => String)
  @ApiProperty({ description: 'Item Reference' })
  itemReference: string

  @Field(() => String)
  @ApiProperty({ description: 'Serial Number' })
  serialNumber: string
}

@ObjectType({ description: 'SSCC-96 Components' })
export class SSCC96Components extends EPCComponents {
  @Field(() => Int)
  @ApiProperty({ description: 'Filter value (0-7)' })
  filter: number

  @Field(() => Int)
  @ApiProperty({ description: 'Extension Digit (0-9)' })
  extensionDigit: number

  @Field(() => String)
  @ApiProperty({ description: 'Company Prefix' })
  companyPrefix: string

  @Field(() => String)
  @ApiProperty({ description: 'Serial Reference' })
  serialReference: string
}

@ObjectType({ description: 'GID-96 Components' })
export class GID96Components extends EPCComponents {
  @Field(() => String)
  @ApiProperty({ description: 'General Manager Number' })
  generalManagerNumber: string

  @Field(() => String)
  @ApiProperty({ description: 'Object Class' })
  objectClass: string

  @Field(() => String)
  @ApiProperty({ description: 'Serial Number' })
  serialNumber: string
}

@ObjectType({ description: 'SGLN-96 Components' })
export class SGLN96Components extends EPCComponents {
  @Field(() => Int)
  @ApiProperty({ description: 'Filter value (0-7)' })
  filter: number

  @Field(() => String)
  @ApiProperty({ description: 'Company Prefix' })
  companyPrefix: string

  @Field(() => String)
  @ApiProperty({ description: 'Location Reference' })
  locationReference: string

  @Field(() => String)
  @ApiProperty({ description: 'Extension' })
  extension: string
}

@ObjectType({ description: 'GRAI-96 Components' })
export class GRAI96Components extends EPCComponents {
  @Field(() => Int)
  @ApiProperty({ description: 'Filter value (0-7)' })
  filter: number

  @Field(() => String)
  @ApiProperty({ description: 'Company Prefix' })
  companyPrefix: string

  @Field(() => String)
  @ApiProperty({ description: 'Asset Type' })
  assetType: string

  @Field(() => String)
  @ApiProperty({ description: 'Serial Number' })
  serialNumber: string
}

@ObjectType({ description: 'GIAI-96 Components' })
export class GIAI96Components extends EPCComponents {
  @Field(() => Int)
  @ApiProperty({ description: 'Filter value (0-7)' })
  filter: number

  @Field(() => String)
  @ApiProperty({ description: 'Company Prefix' })
  companyPrefix: string

  @Field(() => String)
  @ApiProperty({ description: 'Individual Asset Reference' })
  individualAssetReference: string
}

@ObjectType({ description: 'ISO 15963 Components' })
export class ISO15963Components extends EPCComponents {
  @Field(() => String)
  @ApiProperty({ description: 'Manufacturer Code' })
  manufacturerCode: string

  @Field(() => String)
  @ApiProperty({ description: 'Serial Number' })
  serialNumber: string
}

// Union type for EPCComponents
export const EPCComponentsUnion = createUnionType({
  name: 'EPCComponentsUnion',
  types: () =>
    [
      SGTIN96Components,
      SSCC96Components,
      GID96Components,
      SGLN96Components,
      GRAI96Components,
      GIAI96Components,
      ISO15963Components,
    ] as const,
  resolveType(value) {
    switch (value.scheme) {
      case EPCScheme.SGTIN96:
        return SGTIN96Components
      case EPCScheme.SSCC96:
        return SSCC96Components
      case EPCScheme.GID96:
        return GID96Components
      case EPCScheme.SGLN96:
        return SGLN96Components
      case EPCScheme.GRAI96:
        return GRAI96Components
      case EPCScheme.GIAI96:
        return GIAI96Components
      case EPCScheme.ISO15963:
        return ISO15963Components
      default:
        return null
    }
  },
})

@ObjectType()
export class EPCData {
  @Field(() => String)
  @ApiProperty({ description: 'EPC code as a hexadecimal string.' })
  epc: string

  @Field(() => EPCComponentsUnion)
  @ApiProperty({
    description: 'Decoded EPC components.',
    discriminator: { propertyName: 'scheme' },
    oneOf: [
      { $ref: getSchemaPath(SGTIN96Components) },
      { $ref: getSchemaPath(SSCC96Components) },
      { $ref: getSchemaPath(GID96Components) },
      { $ref: getSchemaPath(SGLN96Components) },
      { $ref: getSchemaPath(GRAI96Components) },
      { $ref: getSchemaPath(GIAI96Components) },
      { $ref: getSchemaPath(ISO15963Components) },
    ],
  })
  components: typeof EPCComponentsUnion
}
