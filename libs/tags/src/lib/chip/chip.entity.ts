import { InputType, Field, createUnionType, ObjectType, registerEnumType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'

import {
  CreateChipEM4425CSVRow,
  CreateEM4425Chip,
  EM4425V12,
  EM4425V13,
  EM4425V16,
  EM4425ValidationArguments,
  EMChipType,
  generateRandomEMChip,
} from './EM'
import {
  CreateChipNXPCSVRow,
  CreateNXPChip,
  ICODEDNA,
  ICODESLIX,
  NTAG224DNA,
  NTAG424DNA,
  NXPChipType,
  NXPValidationArguments,
} from './NXP'
import { generateRandomNXPChip } from './NXP/nxp.utils.gen'

export const ChipType = {
  ...EMChipType,
  ...NXPChipType,
}

export type ChipType = EMChipType | NXPChipType

registerEnumType(ChipType, {
  name: 'ChipType',
  description: 'The NFC chip type',
})

@ObjectType()
export class Chip {
  @Field(() => String)
  @ApiProperty({ type: String })
  serialNumber: string

  @Field(() => ChipType)
  @ApiProperty({ enum: ChipType })
  type: ChipType
}

export const chipInterfaceResolver = (chip: Chip): ChipUnion => {
  switch (chip?.type) {
    case ChipType.EM4425V12:
      return chip instanceof EM4425V12 ? chip : new EM4425V12(chip as EM4425V12)
    case ChipType.EM4425V13:
      return chip instanceof EM4425V13 ? chip : new EM4425V13(chip as EM4425V13)
    case ChipType.EM4425V16:
      return chip instanceof EM4425V16 ? chip : new EM4425V16(chip as EM4425V16)
    case ChipType.NTAG424DNA:
      return chip instanceof NTAG424DNA ? chip : new NTAG424DNA(chip as NTAG424DNA)
    case ChipType.NTAG224DNA:
      return chip instanceof NTAG224DNA ? chip : new NTAG224DNA(chip as NTAG224DNA)
    case ChipType.ICODEDNA:
      return chip instanceof ICODEDNA ? chip : new ICODEDNA(chip as ICODEDNA)
    case ChipType.ICODESLIX:
      return chip instanceof ICODESLIX ? chip : new ICODESLIX(chip as ICODESLIX)
  }
}

export type ChipUnion = EM4425V12 | EM4425V13 | EM4425V16 | NTAG424DNA | NTAG224DNA | ICODEDNA | ICODESLIX
export const ChipUnion = createUnionType({
  name: 'ChipUnion',
  types: () => [EM4425V12, EM4425V13, EM4425V16, NTAG424DNA, NTAG224DNA, ICODEDNA, ICODESLIX] as const,
  resolveType: (value) => {
    return chipInterfaceResolver(value)
  },
})

@InputType()
export class CreateChip {
  @Field(() => CreateEM4425Chip, { nullable: true, description: 'The EM4425 chip to create.' })
  @ApiProperty({ type: CreateEM4425Chip, required: false, description: 'The EM4425 chip to create.' })
  em?: CreateEM4425Chip

  @Field(() => CreateNXPChip, { nullable: true, description: 'The NXP chip to create.' })
  @ApiProperty({ type: CreateNXPChip, required: false, description: 'The NXP chip to create.' })
  nxp?: CreateNXPChip
}

@InputType()
export class ChipValidationArguments {
  @Field(() => EM4425ValidationArguments, { nullable: true, description: 'The EM4425 validation arguments.' })
  @ApiProperty({ type: EM4425ValidationArguments, required: false, description: 'The EM4425 validation arguments.' })
  em?: EM4425ValidationArguments

  @Field(() => NXPValidationArguments, { nullable: true, description: 'The NXP validation arguments.' })
  @ApiProperty({ type: NXPValidationArguments, required: false, description: 'The NXP validation arguments.' })
  nxp?: NXPValidationArguments
}

export type CreateChipCSVRow = CreateChipEM4425CSVRow | CreateChipNXPCSVRow

export function generateRandomChip(type?: ChipType): Chip {
  if (!type) {
    type = (Object.values(ChipType) as ChipType[])[Math.floor(Math.random() * Object.values(ChipType).length)]
  }

  if (Object.values(EMChipType).includes(type as EMChipType)) {
    return generateRandomEMChip(type as EMChipType)
  } else if (Object.values(NXPChipType).includes(type as NXPChipType)) {
    return generateRandomNXPChip(type as NXPChipType)
  }
}
