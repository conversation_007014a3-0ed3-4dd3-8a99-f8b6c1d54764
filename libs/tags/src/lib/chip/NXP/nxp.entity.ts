import { Directive, Field, HideField, InputType, ObjectType, registerEnumType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { Exclude } from 'class-transformer'
import { IsEnum, IsNumberString, IsOptional, IsString } from 'class-validator'

import type { Chip } from '../chip.entity'

export enum NXPChipType {
  NTAG224DNA = 'NTAG224DNA',
  NTAG424DNA = 'NTAG424DNA',
  ICODEDNA = 'ICODEDNA',
  ICODESLIX = 'ICODESLIX',
}

registerEnumType(NXPChipType, {
  name: 'NXPChipType',
  description: 'The NXP NFC chip type',
})

export interface CreateChipNXPCSVRow {
  serialNumber: string
  type: NXPChipType
  sdmMetaReadKey?: string
  sdmFileReadKey?: string
}

@InputType()
export class NXPValidationArguments {
  @Field(() => String, { nullable: true, description: 'Encrypted PICC data for DNA chips' })
  @ApiProperty({ type: String, required: false, description: 'Encrypted PICC data for DNA chips' })
  @IsString()
  @IsOptional()
  piccEncData?: string

  @Field(() => String, { nullable: true, description: 'SDMMAC for DNA chips' })
  @ApiProperty({ type: String, required: false, description: 'SDMMAC for DNA chips' })
  @IsString()
  @IsOptional()
  sdmmac?: string

  @Field(() => String, { nullable: true, description: 'Encrypted file data or EPC for DNA chips' })
  @ApiProperty({ type: String, required: false, description: 'Encrypted file data or EPC for DNA chips' })
  @IsString()
  @IsOptional()
  encFileData?: string

  @Field(() => String, { nullable: true, description: 'EPC for ICODE SLIX' })
  @ApiProperty({ type: String, required: false, description: 'EPC for ICODE SLIX' })
  @IsString()
  @IsOptional()
  epc?: string

  constructor(data: Partial<NXPValidationArguments>) {
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class NXP implements Chip {
  @Field(() => String, { description: 'The serial number of the chip.' })
  @ApiProperty({ type: String, description: 'The serial number of the chip.' })
  @IsString()
  serialNumber: string

  @Field(() => NXPChipType, { description: 'The type of the chip.' })
  @ApiProperty({ enum: NXPChipType, description: 'The type of the chip.' })
  @IsEnum(NXPChipType)
  type: NXPChipType

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsOptional()
  @IsString()
  sdmMetaReadKey?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsOptional()
  @IsString()
  sdmFileReadKey?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsNumberString()
  @IsOptional()
  counter?: number
}

@InputType()
export class CreateNXPChip {
  @Field(() => String, { description: 'The serial number of the chip.' })
  @ApiProperty({ type: String, description: 'The serial number of the chip.' })
  serialNumber: string

  @Field(() => NXPChipType, { description: 'The type of the chip.' })
  @ApiProperty({ enum: NXPChipType, description: 'The type of the chip.' })
  type: NXPChipType

  @Field(() => String, { nullable: true, description: 'The SDM meta read key of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The SDM meta read key of the chip.' })
  @IsOptional()
  @IsString()
  sdmMetaReadKey?: string

  @Field(() => String, { nullable: true, description: 'The SDM file read key of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The SDM file read key of the chip.' })
  @IsOptional()
  @IsString()
  sdmFileReadKey?: string
}

@ObjectType()
@Directive('@key(fields: "id")')
export class NTAG424DNA extends NXP {
  constructor(data: Partial<NTAG424DNA>) {
    super()
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class NTAG224DNA extends NXP {
  constructor(data: Partial<NTAG224DNA>) {
    super()
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class ICODEDNA extends NXP {
  constructor(data: Partial<ICODEDNA>) {
    super()
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class ICODESLIX extends NXP {
  constructor(data: Partial<ICODESLIX>) {
    super()
    Object.assign(this, data)
  }
}
