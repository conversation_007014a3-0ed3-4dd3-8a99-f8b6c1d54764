import { createCipheriv, createDecipheriv, timingSafeEqual } from 'crypto'

export enum EncMode {
  AES = 0,
  LRP = 1,
}

export enum ParamMode {
  SEPARATED = 0,
  BULK = 1,
}

interface GenerateSunMessageParams {
  mode: EncMode
  paramMode: ParamMode
  sdmMetaReadKey: Buffer
  sdmFileReadKey: Buffer
  uid: Buffer
  fileData?: Buffer
  readCtr: number
}

function calculateMac(key: <PERSON>uffer, data: Buffer): Buffer {
  const iv = Buffer.alloc(16, 0)
  const macInput = Buffer.concat([
    Buffer.from([0x3c, 0xc3, 0x00, 0x01, 0x00, 0x80]), // MAC input prefix
    data,
  ])
  const cipher = createCipheriv('aes-128-cbc', key, iv)
  return cipher.update(macInput).slice(0, 8) // Take first 8 bytes
}

export function generateSunMessage({
  mode,
  paramMode,
  sdmMetaReadKey,
  sdmFileReadKey,
  uid,
  fileData,
  readCtr,
}: GenerateSunMessageParams) {
  if (mode !== EncMode.AES) {
    throw new Error('Only AES mode is currently supported')
  }

  if (uid.length !== 7 && uid.length !== 8) {
    throw new Error(`Invalid UID length: ${uid.length}. Expected 7 or 8 bytes.`)
  }

  // Prepare PICC Data
  const piccDataTag = Buffer.alloc(1)
  piccDataTag[0] = 0x80 | 0x40 | uid.length // UID mirroring enabled, SDM read counter enabled, UID length

  const readCtrBuffer = Buffer.alloc(3)
  readCtrBuffer.writeUIntLE(readCtr, 0, 3)

  const piccData = Buffer.concat([piccDataTag, uid, readCtrBuffer])

  // Generate IV (all zeros for NTAG 424 DNA and 224 DNA)
  const iv = Buffer.alloc(16, 0)

  // Encrypt PICC Data
  const cipher = createCipheriv('aes-128-cbc', sdmMetaReadKey, iv)
  const piccEncData = Buffer.concat([cipher.update(piccData), cipher.final()])

  // Generate MAC
  const sdmmac = calculateMac(sdmMetaReadKey, piccEncData)

  let encFileData: Buffer | undefined

  if (paramMode === ParamMode.BULK && fileData) {
    // Encrypt File Data
    const fileIv = Buffer.concat([uid.slice(0, 7), readCtrBuffer, Buffer.alloc(6, 0)])
    const fileCipher = createCipheriv('aes-128-cbc', sdmFileReadKey, fileIv)
    encFileData = Buffer.concat([fileCipher.update(fileData), fileCipher.final()])
  }

  return {
    piccEncData,
    sdmmac,
    encFileData,
  }
}

function getEncryptionMode(piccEncData: Buffer): EncMode {
  if (piccEncData.length === 16) return EncMode.AES
  if (piccEncData.length === 24) return EncMode.LRP
  throw new Error('Unsupported encryption mode.')
}

function decryptFileData(
  sdmFileReadKey: Buffer,
  uid: Buffer,
  readCtr: Buffer,
  encFileData: Buffer,
  mode: EncMode,
): Buffer {
  if (mode === EncMode.AES) {
    const fileIv = Buffer.concat([uid.slice(0, 7), readCtr, Buffer.alloc(6, 0)])
    const decipher = createDecipheriv('aes-128-cbc', sdmFileReadKey, fileIv)
    return Buffer.concat([decipher.update(encFileData), decipher.final()])
  } else {
    throw new Error('LRP file data decryption not implemented')
  }
}

export function decryptSunMessage(
  paramMode: ParamMode,
  sdmMetaReadKey: Buffer,
  sdmFileReadKey: Buffer,
  piccEncData: Buffer,
  sdmmac: Buffer,
  encFileData?: Buffer,
) {
  const mode = getEncryptionMode(piccEncData)
  let decryptedData: Buffer

  if (mode === EncMode.AES) {
    const iv = Buffer.alloc(16, 0)
    const decipher = createDecipheriv('aes-128-cbc', sdmMetaReadKey, iv)
    decryptedData = Buffer.concat([decipher.update(piccEncData), decipher.final()])
  } else {
    throw new Error('LRP decryption not implemented.')
  }

  // Verify MAC
  const calculatedMac = calculateMac(sdmMetaReadKey, piccEncData)
  if (!timingSafeEqual(calculatedMac, sdmmac)) {
    throw new Error('MAC verification failed.')
  }

  const piccDataTag = decryptedData[0]
  const uidMirroringEn = (piccDataTag & 0x80) === 0x80
  const sdmReadCtrEn = (piccDataTag & 0x40) === 0x40
  const uidLength = piccDataTag & 0x0f

  if (uidLength !== 0x07 && uidLength !== 0x08) {
    throw new Error(`Unsupported UID length: ${uidLength}`)
  }

  let offset = 1
  let uid: Buffer | undefined
  let readCtr: Buffer | undefined
  let readCtrNum: number | undefined

  if (uidMirroringEn) {
    uid = decryptedData.subarray(offset, offset + uidLength)
    offset += uidLength
  }

  if (sdmReadCtrEn) {
    readCtr = decryptedData.subarray(offset, offset + 3)
    offset += 3
    readCtrNum = readCtr.readUIntLE(0, 3)
  }

  if (!uid) {
    throw new Error('UID is required but not present in the decrypted data.')
  }

  let fileData: Buffer | undefined
  if (encFileData && paramMode === ParamMode.BULK) {
    if (!readCtr) {
      throw new Error('SDMReadCtr is required to decipher SDMENCFileData.')
    }
    fileData = decryptFileData(sdmFileReadKey, uid, readCtr, encFileData, mode)
  }

  return {
    piccDataTag,
    uid,
    readCtr: readCtrNum,
    fileData,
    encryptionMode: mode,
  }
}
