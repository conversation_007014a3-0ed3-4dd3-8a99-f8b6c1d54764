<PERSON> (<PERSON>)
"serialNumber","type","accessPassword","killPassword","cryptoKeyId0","cryptoKeyId1","cryptoKeyId2"
"E0:04:01:00:8A:97:1A:CC","ICODESLIX","","","","",""
"E0:04:01:53:03:CE:6E:0B","ICODESLIX","","","","",""
"E0:04:01:53:03:CE:5C:5A","ICODESLIX","","","","",""
"E0:04:01:53:03:CE:69:39","ICODESLIX","","","","",""

<PERSON><PERSON><PERSON> (Panthers)
"serialNumber","type","accessPassword","killPassword","cryptoKeyId0","cryptoKeyId1","cryptoKeyId2"
"E0:04:01:50:B9:AB:A6:8E","ICODESLIX","","","","",""
"E0:04:01:50:B9:AB:AA:4D","ICODESLIX","","","","",""
"E0:04:01:50:B9:AB:8E:44","ICODESLIX","","","","",""

Dave (Panthers)
"serialNumber","type","accessPassword","killPassword","cryptoKeyId0","cryptoKeyId1","cryptoKeyId2"
"E0:04:01:50:B9:AB:99:3A","ICODESLIX","","","","",""

Dave (Aparrel)
"serialNumber","type","accessPassword","killPassword","cryptoKeyId0","cryptoKeyId1","cryptoKeyId2"
"E0:04:01:50:B9:AB:85:C9","ICODESLIX","","","","",""

Claire (Aparrel)
"serialNumber","type","accessPassword","killPassword","cryptoKeyId0","cryptoKeyId1","cryptoKeyId2"
"E0:04:01:50:B9:AB:A7:DC","ICODESLIX","","","","",""
"E0:04:01:50:B9:AB:8F:8A","ICODESLIX","","","","",""

Rodric (Panthers)
"serialNumber","type","accessPassword","killPassword","cryptoKeyId0","cryptoKeyId1","cryptoKeyId2"
"E0:04:01:50:DC:0C:7B:BC","ICODESLIX","","","","",""