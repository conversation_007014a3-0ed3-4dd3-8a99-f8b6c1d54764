import { ICODEDNA, ICODESLIX, NTAG224DNA, NTAG424DNA, NXPChipType } from './nxp.entity'
import { decodeEPC } from '../epc'
import { checkCounter } from '../utils'
import { decryptSunMessage, ParamMode } from './nxp.utils.crypto'

import type { CreateNXPChip, NXP, NXPValidationArguments } from './nxp.entity'

function verifySunMessage(chip: NXP, args: NXPValidationArguments): { uid: Buffer; readCtr?: number; epc?: Buffer } {
  if (!chip.sdmMetaReadKey || !chip.sdmFileReadKey) {
    throw new Error('Missing SDM keys for chip')
  }

  const piccEncData = Buffer.from(args.piccEncData, 'hex')
  const sdmmac = Buffer.from(args.sdmmac, 'hex')
  const encFileData = args.encFileData ? Buffer.from(args.encFileData, 'hex') : undefined

  const decrypted = decryptSunMessage(
    ParamMode.BULK,
    Buffer.from(chip.sdmMetaReadKey, 'hex'),
    Buffer.from(chip.sdmFileReadKey, 'hex'),
    piccEncData,
    sdmmac,
    encFileData,
  )

  // Validate UID
  const expectedUIDLength = chip.type === NXPChipType.ICODEDNA || chip.type === NXPChipType.ICODESLIX ? 8 : 7
  const chipUID = chip.serialNumber
    .replace(/:/g, '')
    .toUpperCase()
    .slice(0, expectedUIDLength * 2)
  const decryptedUID = decrypted.uid.toString('hex').toUpperCase()

  if (decryptedUID !== chipUID) {
    throw new Error('Invalid UID in decrypted data')
  }

  if (decrypted.uid.length !== expectedUIDLength) {
    throw new Error(`Invalid UID length. Expected ${expectedUIDLength}, got ${decrypted.uid.length}`)
  }

  return {
    uid: decrypted.uid,
    readCtr: decrypted.readCtr,
    epc: decrypted.fileData,
  }
}

export function authenticateNXP(chip: NXP, args: NXPValidationArguments) {
  switch (chip.type) {
    case NXPChipType.NTAG224DNA:
    case NXPChipType.NTAG424DNA:
    case NXPChipType.ICODEDNA: {
      let decrypted: { uid: Buffer; readCtr?: number; epc?: Buffer }

      try {
        decrypted = verifySunMessage(chip, args)
      } catch (error) {
        throw new Error(`SUN message verification failed: ${error.message}`)
      }

      if (decrypted.readCtr !== undefined) {
        if (!checkCounter(decrypted.readCtr, chip.counter ?? 0)) {
          throw new Error('Invalid counter')
        }
      }

      return {
        uid: decrypted.uid.toString('hex'),
        counter: decrypted.readCtr,
        epc: decrypted.epc && decodeEPC(decrypted.epc.toString('hex')),
      }
    }

    case NXPChipType.ICODESLIX:
      return {
        uid: args.piccEncData, // For SLIX, piccEncData is actually the UID
        counter: undefined,
        epc: args.encFileData && decodeEPC(args.encFileData),
      }

    default:
      throw new Error('Unsupported chip type')
  }
}

const classMap = {
  [NXPChipType.NTAG224DNA]: NTAG224DNA,
  [NXPChipType.NTAG424DNA]: NTAG424DNA,
  [NXPChipType.ICODEDNA]: ICODEDNA,
  [NXPChipType.ICODESLIX]: ICODESLIX,
}

export function createNXPChip(data: CreateNXPChip): NXP {
  return new classMap[data.type](data)
}

// Debug
// const demoChip = new NTAG224DNA({
//   serialNumber: 'E0:16:78:01:1C:DB:2C',
//   type: NXPChipType.NTAG224DNA,
//   sdmMetaReadKey: 'F0E0D0C090A0B0800102030405060708',
//   sdmFileReadKey: 'F0E0D0C090A0B0800102030405060708',
// })

// // const demoChip = generateRandomNXPChip(NXPChipType.NTAG224DNA)
// // console.log({ demoChip })

// const generatedUrl = generateNXPUrl(demoChip)
// console.log({ generatedUrl })

// const parsedUrl = new URL(generatedUrl)

// // const parsedUrl = new URL(
// //   'https://daptapgo.io/tap?uid=04A1B2C3D4E5F6&piccEncData=1A2B3C4D5E6F7G8H9I0J1K2L3M4N5O6P&sdmmac=A1B2C3D4E5F6G7H8&encFileData=ABCDEF0123456789ABCDEF0123456789&epc=30340000000000000000000000000001&ctr=42',
// // )

// // const parsedUrl = new URL('https://daptapgo.io/tap?uid=E004010000000001&piccEncData=1A2B3C4D5E6F7G8H9I0J1K2L3M4N5O6P&sdmmac=A1B2C3D4E5F6G7H8&encFileData=ABCDEF0123456789ABCDEF0123456789&epc=30340000000000000000000000000002&ctr=17')

// // const parsedUrl = new URL('https://daptapgo.io/tap?uid=E004020000000002&epc=30340000000000000000000000000003')

// console.log(
//   authenticateNXP(demoChip, {
//     piccEncData: parsedUrl.searchParams.get('piccEncData'),
//     sdmmac: parsedUrl.searchParams.get('sdmmac'),
//     encFileData: parsedUrl.searchParams.get('encFileData'),
//     epc: parsedUrl.searchParams.get('epc'),
//   }),
// )
