import { randomBytes } from 'crypto'

import { NXPChipType, type NXP } from './nxp.entity'
import { generateEPC } from '../epc'
import { EncMode, ParamMode, generateSunMessage } from './nxp.utils.crypto'

function generateRandomChipId(uidLength: number): string {
  return randomBytes(uidLength).toString('hex').toUpperCase()
}

export function generateNXPUrl(chip: NXP): string {
  const uidLength = chip.type === NXPChipType.ICODEDNA || chip.type === NXPChipType.ICODESLIX ? 8 : 7
  const uid = Buffer.from(chip.serialNumber.replace(/:/g, ''), 'hex').slice(0, uidLength)

  const { epc } = generateEPC()

  switch (chip.type) {
    case NXPChipType.NTAG224DNA:
    case NXPChipType.NTAG424DNA:
    case NXPChipType.ICODEDNA: {
      if (!chip.sdmMetaReadKey || !chip.sdmFileReadKey) {
        throw new Error('Missing SDM keys for chip')
      }

      const sunMessage = generateSunMessage({
        mode: EncMode.AES,
        paramMode: ParamMode.BULK,
        sdmMetaReadKey: Buffer.from(chip.sdmMetaReadKey, 'hex'),
        sdmFileReadKey: Buffer.from(chip.sdmFileReadKey, 'hex'),
        uid,
        fileData: Buffer.from(epc, 'hex'),
        readCtr: chip.counter || 1,
      })

      const url = new URL('https://daptapgo.io/tap')
      url.searchParams.append('uid', uid.toString('hex'))
      url.searchParams.append('piccEncData', sunMessage.piccEncData.toString('hex'))
      url.searchParams.append('sdmmac', sunMessage.sdmmac.toString('hex'))
      if (sunMessage.encFileData) {
        url.searchParams.append('encFileData', sunMessage.encFileData.toString('hex'))
      }
      url.searchParams.append('epc', epc)
      if (chip.counter !== undefined) {
        url.searchParams.append('ctr', chip.counter.toString())
      }
      return url.toString()
    }
    case NXPChipType.ICODESLIX: {
      const url = new URL('https://daptapgo.io/tap')
      url.searchParams.append('uid', uid.toString('hex'))
      url.searchParams.append('epc', epc)
      return url.toString()
    }
    default:
      throw new Error('Unsupported chip type')
  }
}

export function generateRandomNXPChip(type: NXPChipType): NXP {
  let uidLength: number
  switch (type) {
    case NXPChipType.NTAG224DNA:
    case NXPChipType.NTAG424DNA:
      uidLength = 7 // 7-byte UID
      break
    case NXPChipType.ICODEDNA:
    case NXPChipType.ICODESLIX:
      uidLength = 8 // 8-byte UID
      break
    default:
      throw new Error('Unsupported chip type')
  }

  const serialNumber = generateRandomChipId(uidLength)
    .match(/.{1,2}/g)
    .join(':')

  switch (type) {
    case NXPChipType.NTAG224DNA:
    case NXPChipType.NTAG424DNA:
    case NXPChipType.ICODEDNA:
      return {
        serialNumber,
        type,
        sdmMetaReadKey: randomBytes(16).toString('hex'), // AES-128 key
        sdmFileReadKey: randomBytes(16).toString('hex'), // AES-128 key
        counter: 0,
      }
    case NXPChipType.ICODESLIX:
      return {
        serialNumber,
        type,
      }
    default:
      throw new Error('Unsupported chip type')
  }
}
