import { EM4425ChipType, EM4425Mode, EM4425V12, EM4425V13, EM4425V16 } from './em4425.entity'
import { generateUid, generateTid } from './em4425.utils.gen'
import { checkCounter } from '../utils'
import { generateOtpSignature, signatureToDataBlock } from './em4425.utils.crypto'
import { decodeEPC } from '../epc'

import type { CreateEM4425Chip, EM4425, EM4425ValidationArguments } from './em4425.entity'
import type { ParsedDataBlock } from './em4425.utils.crypto'

function inferModeAndKey(chip: EM4425) {
  if (chip.cryptoKeyId1) {
    // If cryptoKeyId1 is present, we assume it's CryptoMode2
    return { mode: EM4425Mode.CryptoMode2, cryptoKey: chip.cryptoKeyId1 }
  } else if (chip.cryptoKeyId0) {
    // If only cryptoKeyId0 is present, we assume it's CryptoMode1
    return { mode: EM4425Mode.CryptoMode1, cryptoKey: chip.cryptoKeyId0 }
  } else {
    // If no crypto keys are present, we assume it's CryptoMode0
    return { mode: EM4425Mode.CryptoMode0, cryptoKey: undefined }
  }
}

export function authenticateEM4425(chip: EM4425, args: EM4425ValidationArguments) {
  const { mode, cryptoKey } = inferModeAndKey(chip)

  // Decrypt or process the signature based on the mode
  let data: ParsedDataBlock

  if (mode === EM4425Mode.CryptoMode0) {
    // In CryptoMode0 (No Encryption), process the signature without a key
    data = signatureToDataBlock(args.signature, undefined)
  } else {
    // In encrypted modes, decrypt the signature using the crypto key
    data = signatureToDataBlock(args.signature, cryptoKey)
  }

  // Validate the data extracted from the signature
  if ((data.uid && data.uid !== chip.uid) || (data.tid && data.tid !== chip.tid)) {
    throw new Error('Invalid ID in signature data')
  }

  // Validate ID length based on flags
  if (data.flags.idLength === 0) {
    // ID should be 12 bytes long in 16-bit mode and 15 bytes long in 24-bit mode
    if (data.tid?.length !== 15) {
      throw new Error('Invalid TID length')
    }
  } else {
    // ID should be 8 bytes long in 16-bit mode and 11 bytes long in 24-bit mode
    if (data.uid?.length !== 11) {
      throw new Error('Invalid UID length')
    }
  }

  // Check for tamper alarm and UHF kill flag
  if (data.flags.tamperAlarm) {
    throw new Error('Tamper alarm triggered. Possible security breach.')
  }

  if (data.flags.uhfKillFlag) {
    throw new Error('UHF has been killed. Access denied.')
  }

  // Validate token
  if (data.token !== args.token) {
    throw new Error('Token mismatch')
  }

  // Validate token length
  const tokenBuffer = Buffer.from(data.token, 'base64')
  if (tokenBuffer.length !== 3) {
    throw new Error('Invalid token length')
  }

  // Check the counter
  if (!checkCounter(data.counter, chip.counter ?? 0, 8)) {
    throw new Error('Invalid counter')
  }

  // Validate the signature (only in encrypted modes)
  if (mode !== EM4425Mode.CryptoMode0) {
    const calculatedSignature = generateOtpSignature(
      args.flags,
      args.token,
      data.uid || data.tid,
      mode,
      cryptoKey,
      data.sessionPassword,
    )
    if (calculatedSignature !== args.signature) {
      throw new Error('Invalid signature')
    }
  }

  // Decode the EPC
  const epc = decodeEPC(args.epc)

  return {
    counter: data.counter,
    sessionPassword: data.sessionPassword?.toString(),
    epc,
  }
}

const classMap = {
  [EM4425ChipType.EM4425V12]: EM4425V12,
  [EM4425ChipType.EM4425V13]: EM4425V13,
  [EM4425ChipType.EM4425V16]: EM4425V16,
}

export function createEM4425Chip(data: CreateEM4425Chip) {
  return new classMap[data.type]({
    serialNumber: data.serialNumber,
    uid: generateUid(data.serialNumber),
    ...(data.type !== EM4425ChipType.EM4425V16 && { tid: generateTid(data.serialNumber) }),
    type: data.type,
    ...(data.accessPassword && { accessPassword: data.accessPassword }),
    ...(data.killPassword && { killPassword: data.killPassword }),
    ...(data.cryptoKeyId0 && { cryptoKeyId0: data.cryptoKeyId0 }),
    ...(data.cryptoKeyId1 && { cryptoKeyId1: data.cryptoKeyId1 }),
    ...(data.type === EM4425ChipType.EM4425V13 && data.cryptoKeyId2 && { cryptoKeyId2: data.cryptoKeyId2 }),
  })
}

/**
 * Extracts the encryption key from the EM4425 memory data.
 * @param {string} data212B - The 8-byte hex string from address 212B.
 * @param {string} data212C - The 8-byte hex string from address 212C.
 * @returns {string} - The extracted 16-byte encryption key as a hex string.
 */
export function extractEncryptionKey(data212B: string, data212C: string): string {
  // Helper function to reverse byte order in an 8-byte hex string
  function reverseBytes(hexStr: string): string {
    if (hexStr.length !== 16) {
      throw new Error('Input must be an 8-byte hex string (16 hex characters).')
    }

    // Split the hex string into an array of bytes
    const bytes = hexStr.match(/.{2}/g)
    if (!bytes) {
      throw new Error('Invalid hex string.')
    }

    // Reverse the array of bytes
    const reversedBytes = bytes.reverse()

    // Join the reversed bytes back into a hex string
    return reversedBytes.join('')
  }

  // Reverse the byte order for each block
  const reversed212B = reverseBytes(data212B)
  const reversed212C = reverseBytes(data212C)

  // Concatenate the reversed blocks to form the key
  const encryptionKey = reversed212B + reversed212C

  return encryptionKey.toUpperCase()
}

// // Example usage with your provided data
// const data212B = '80B0A090C0D0E0F0'
// const data212C = '0807060504030201'

// const encryptionKey = extractEncryptionKey(data212B, data212C)
// console.log('Extracted Encryption Key:', encryptionKey)

// Debug
// const demoChip = new EM4425V12({
//   serialNumber: 'E0:16:78:01:1C:DB:2C:76',
//   uid: generateUid('E0:16:78:01:1C:DB:2C:76'),
//   cryptoKeyId0: 'F0E0D0C090A0B0800102030405060708',
//   accessPassword: '00000000', // '04030201'
//   killPassword: '00000000', //'0D0C0B0A'
// })

// // const generatedUrl = generateEM4425Url(demoChip)
// // console.log({ generatedUrl })

// // const parsedUrl = new URL(generatedUrl)

// const parsedUrl = new URL(
//   'https://daptapgo.io/tap?uid=4BZ4ARzbLHY&token=AAA4&f=g&sig=IkEpnffWsnt_D4MuAXnOZA&EPC=E280B11020007801080261D1',
// )
// console.log(
//   authenticateEM4425(demoChip, {
//     token: parsedUrl.searchParams.get('token'),
//     flags: parsedUrl.searchParams.get('f'),
//     signature: parsedUrl.searchParams.get('sig'),
//     epc: parsedUrl.searchParams.get('EPC'),
//   }),
// )
