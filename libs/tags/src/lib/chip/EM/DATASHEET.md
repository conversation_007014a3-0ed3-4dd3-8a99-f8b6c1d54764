DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
1 http://www.emmicroelectronic.com
```

RAINFC AUTHENTICITY TRANSPONDER IC

DESCRIPTION

em|echo-V corresponds to the latest generation of EM
Microelectronic RAINFC devices, bringing innovative
features to the HF, NFC, and RAIN RFIDTM worlds. The chip
combines all functionalities on a single die, with NFC for
proximity range, HF for vicinity range, and RAIN technology
used for long range application purposes. All protocols make
use of a shared memory and a common IC serial number.

Targeted applications and market segments include retail,
product authentication, consumer engagement, industrial,
automotive, and aerospace logistics.

A Tag or label based on the em|echo-V provides multiple
benefits and usages via the RAIN communication interface
like stock inventory, product returns, and data privacy. The
same Tag or label also enables new consumer services like
product authentication, product information, or loyalty
programs using an NFC enabled smartphone.

The chip is a dual frequency device supporting ISO/IEC
15693, ISO/IEC 18000-3 Mode 1, NFC Forum Type 5 Tag,
ISO/IEC18000- 63 , EPCTM Gen2v2, and ISO/IEC 291 67 - 10.

The em|echo-V offers a user configurable non-volatile
memory which is accessible by NFC, HF, and UHF air
interfaces.

APPLICATIONS

Ɩ Inventory and supply chain management

Ɩ Customer engagement, coupons, loyalty programs

Ɩ Product authentication with tamper evidence detection

Ɩ Industrial, automotive, and aerospace logistics

FEATURES

Ɩ Advanced RAIN RFID technology

Ɩ AES-128 crypto for HF tag authentication, HF session
password authentication, and NFC web based
authentication

Ɩ Digital signature for authenticity

Ɩ Tamper Detection

Ɩ Shared memory

Ɩ Dual Frequency 1-step inlay manufacturing

Ɩ Minimum 100k write cycles endurance

Ɩ Minimum 50 years data retention

Ɩ On-chip resonant capacitor: 50pF

Ɩ Extended temperature range: - 40 C to +85C

Ɩ Sawn wafers, 6-mil thickness, gold bumps

HF INTERFACE

```
Ɩ ISO/IEC 15693 and 18000-3 compliant
Ɩ ISO/IEC 29167-10 (AES-128 crypto) compliant
Ɩ NIST SP 800- 22 compliant
Ɩ Optional AES tag authentication supported with up to 3
crypto keys
Ɩ Optional random ID and secure customer privacy
Ɩ Protected memory using password or crypto
Ɩ Control of UHF privacy features with password or crypto
Ɩ Tamper Alarm is readable
```

NFC INTERFACE

```
Ɩ NFC Forum Type 5 Tag compliant
Ɩ Optional app-free web based crypto authentication
Ɩ Optional app-free tamper detection
Ɩ Optional ACCESS counter increased at first reading
Ɩ Optional app access of protected memory with password
protection
Ɩ Optional app control of UHF privacy features with
password protection
```

UHF INTERFACE

```
Ɩ ISO/IEC 18000-63 compliant
Ɩ EPCTM Generation-2 Version 2 (Gen2v2) certified:
```

- Alteration EAS certified
- Tag Alteration (Core) certified
  Ɩ Read sensitivity up to -20dBm with a dipole antenna
  Ɩ Write sensitivity up to - 1 4.5dBm with a dipole antenna
  Ɩ Optional NFC ACCESS counter is readable
  Ɩ Tamper Alarm is readable

MEMORY

```
Ɩ Shared unique IC serial number included in:
```

- 64 - bit UID (HF)
- 96 - bit TID (UHF)
  Ɩ Configurable 2048-bit memory used for:
- HF USER memory
- Up to 480-bit EPC/UII encodings
- UHF USER memory
- Optional Digital Signature (none, 256b, 384b, 512b)
  Ɩ 1 - step tag encoding possible from either HF or UHF
  interface.

RAIN RFID is a trademark of the RAIN RFID Alliance.

EPC is a trademark of EPCglobal Inc.

# Identiv

## DATASHEET

Copyright  2021 , EM Microelectronic-Marin SA
2 [http://www.emmicroelectronic.com](http://www.emmicroelectronic.com)

DATASHEET

Copyright  2021 , EM Microelectronic-Marin SA

- 1. Typical Operating Configurations TABLE OF CONTENTS
- 2. Block Diagram
- 3. Electrical specifications
  - 3.1. Absolute Maximum Ratings
  - 3.2. Handling Procedures
  - 3.3. Operating Conditions
  - 3.4. Electrical Characteristics – HF Interface
  - 3.5. Electrical Characteristics – UHF Interface
  - 3.6. Tamper Loop Electrical Characteristics
  - 3.7. NVM Electrical Characteristics
  - 3.8. Timing
- 4. Product overview
  - 4.1. Overview (HF).......................................................................................................................................................................
  - 4.2. Overview (NFC)
  - 4.3. Overview (UHF)
  - 4.4. Functional Description
  - 4.5. Functional Configurations
    - 4.5.1. HF / NFC Functionality
      - NFC ACCESS Counter
      - Web Authentication
      - AES Crypto
      - HF / NFC Functional Configurations
    - 4.5.2. Memory Partition Sizes
    - 4.5.3. Access Rights for each Air Interface
- 5. Memory
  - 5.1. HF / NFC Interface
    - 5.1.1. HF / NFC Memory Map
    - 5.1.2. HF Unique Identifier (UID)
    - 5.1.3. NFC Type 5 Tag Data Structure
  - 5.2. UHF Interface
    - 5.2.1. UHF Memory Map
    - 5.2.2. Reserved Memory Bank
    - 5.2.3. EPC/UII Memory Bank
    - 5.2.4. TID Memory Bank
    - 5.2.5. User Memory Bank
  - 5.3. Memory Delivery State
- 6. HF / NFC Commands
     - 6.1.1. Authenticate Command
     - 6.1.2. Extended Get System Information Command
     - 6.1.3. Quiet Storage Command
     - 6.1.4. Read Signature Command
     - 6.1.5. Write Signature Command
     - 6.1.6. Inventory Block Read Command
     - 6.1.7. Change Password Command 3 http://www.emmicroelectronic.com
     - 6.1.8. Destroy Command
     - 6.1.9. Enable Privacy Command
     - 6.1.10. Disable Privacy Command
     - 6.1.11. Enable Random ID Command
     - 6.1.12. Disable Random ID Command
     - 6.1.13. Fast Read Multiple Blocks Command
     - 6.1.14. Login Command
- 7. UHF Commands
     - 7.1.1. Untraceable Command
     - 7.1.2. SQUARE Commands
       - SQUARE_0 Command
       - SQUARE_N Command
       - SQUARE Examples
- 8. Privacy Features
  - 8.1. Privacy Using Untraceable Command
- 9. Using the Tag Notification (TN) indicator
- 10. Security Services Using ISO/IEC 29167-10 (AES-128)
  - 10.1. Commands
  - 10.2. Tag Authentication Message 1 (TAM1)
  - 10.3. Tag Authentication Message 2 (TAM2)
  - 10.4. Dynamic Session Password Using TAM1 or TAM2
- 11. User Configuration
  - 11.1. Configuration via the HF Interface
  - 11.2. Configuration via the UHF Interface..................................................................................................................................
  - 11.3. System Configuration Blocks / Words
  - 11.4. Crypto Key Blocks / Words
  - 11.5. Additional UHF System Words
- 12. Product Life Cycle
- 13. Tag/Label Personalization Examples
  - 13.1. Personalization Using the UHF interface
  - 13.2. Personalization Using the HF interface
- 14. Pad location diagram
- 15. Ordering Information
  - 15.1. Product Selection Guide
  - 15.2. Versions
  - 15.3. Programmed Values for Optimized Versions
  - 15.4. Standard Versions and Samples
- 16. Product Support

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
4 http://www.emmicroelectronic.com
```

## 1. Typical Operating Configurations TABLE OF CONTENTS

## 2. Block Diagram

## 3. Electrical specifications

### 3.1. Absolute Maximum Ratings

```
Parameters Symbol Min. Max. Unit
Storage temperature TSTORAGE -^50 125 °C^
RF power at antenna attached to A+, A- 1) PMAX-ABS 25 dBm^
AC current induced on L1, L2 IMAX-ABS 50 mA
Electrostatic discharge on all pads/pins 2) VESD -^2000 2000 V^
```

Note 1: Antenna matched to IC impedance at read sensitivity (PREAD)

Note 2: Human Body Model (HBM; 100pF; 1.5kOhm) for all combinations between pads/pins. ESD measurements are made with die mounted
into CDIP packages

Stresses above these listed maximum ratings may cause permanent damages to the device. Exposure beyond specified operating
conditions may affect device reliability or cause malfunction.

### 3.2. Handling Procedures

This device has built-in protection against high static voltages or electric fields; however, anti-static precautions must be taken as
for any other CMOS component. Unless otherwise specified, proper operation can only occur when all terminal voltages are kept
within the voltage range. Unused inputs must always be tied to a defined logic voltage level.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
5 http://www.emmicroelectronic.com
```

### 3.3. Operating Conditions

```
Parameters Symbol Min. Max. Unit
Operating temperature TOP - 40 +85 °C
RF power at antenna attached to A+, A- 1) PMAX-OP 20 dBm
RF carrier frequency fA 860 960 MHz
AC peak current induced on L1, L2 IMAX-OP 30 mA
```

### 3.4. Electrical Characteristics – HF Interface

Operating conditions (unless otherwise specified): Vcoil = 4V (peak to peak), VSS = 0V, fc = 13.56MHz sine wave, TOP = 25°C.

```
Parameters Symbol Conditions Min. Typ. Max. Unit
```

Operating frequency (^) fc - 13.56 - MHz
Resonance Capacitor (^) Cr
fc = 13.56MHz;
U = 2Vrms 47.5^50 52.5^ pF^

### 3.5. Electrical Characteristics – UHF Interface

Operating conditions (unless otherwise specified): TOP = 25°C.

```
Parameters Symbol Conditions Min. Typ. Max. Unit
IC input capacitance Cp Parallel - 0.57 - pF
```

```
IC impedance 3) ZAB
```

```
fA=866MHz
fA=915MHz
```

###### -

```
19.1-j
17.6-j
```

###### - ^

```
Typical assembly capacitance 4) CASSY - 0.2 - pF^
```

```
IC read (inventory) sensitivity 5 )^6 )^7 )8)9) PREAD
```

```
fA=866MHz
fA=915MHz
```

###### -

###### - 18

###### - 18

###### -

```
dBm
dBm
```

```
IC write sensitivity 5 )^6 )^7 )8)9) PWRITE
```

```
fA=866MHz
fA=915MHz
```

###### -

###### - 1 2.

###### - 1 2.

###### -

```
dBm
dBm
Note 3 : Measured directly on wafer with a 100Ω differential network analyzer at minimum operating RF power level
Note 4 : The antenna should be matched assuming 200fF additional input capacitance from assembly
Note 5 : IC impedance conjugate matched to antenna at read sensitivity (PREAD)
Note 6 : IC is configured with tamper pads disabled and EPC/UII encoding of 96 bits
Note 7 : Interrogator using PR-ASK modulation with link parameters Tari = 25 μs, PR = 1.5, BLF = 256 KHz with Miller-4 encoding
Note 8 : HF field is not present
Note 9 : Sensitivity values are for IC devices in die form and do not include antenna gain
```

(^) Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
6 http://www.emmicroelectronic.com
```

### 3.6. Tamper Loop Electrical Characteristics

Operating conditions (unless otherwise specified): TOP = 25°C.

```
Parameters Symbol Conditions Min. Typ. Max. Unit
```

```
Tamper loop maximum capacitance Cmax Measured between tamper pads^ 12.5^ pF
```

```
Tamper loop maximum inductance Lmax Measured between tamper pads 40 nH
```

```
Resistance connected between
TAMPER_IN and TAMPER_OUT to
assure a closed (short) loop
```

```
RCLOSED Cloadmax between tamper
pads/pins = 12.5pF;
Tamper loop enabled
```

###### 0.9^ M

```
Resistance connected between
TAMPER_IN and TAMPER_OUT to
assure an open (broken) loop
```

```
ROPEN Cloadmax between tamper
pads/pins = 12.5pF;
Tamper loop enabled
```

(^10) M
Input impedance between
TAMPER_IN and TAMPER_OUT
ZTAMPER RF power = PREAD;
Pads configured for HI-Z;
fA = 866MHz
5.2-j^106 
RF power = PREAD;
Pads configured for Tamper Loop;
fA = 866MHz
17.5-j^106 
RF power = PREAD;
Pads configured for HI-Z;
fA = 915MHz
5.1-j^101 
RF power = PREAD;
Pads configured for Tamper Loop;
fA = 915MHz
16.1-j^101 

### 3.7. NVM Electrical Characteristics

```
Parameters Symbol Conditions Min. Typ. Max. Unit
Erase / write endurance TCYC TOP^ = 25ºC^ 100,000 Cycles
```

```
Retention TRET TOP = 55ºC 50 Years
```

### 3.8. Timing

```
Parameters Symbol Conditions Min. Typ. Max. Unit
```

```
HF interface execution time to write
a value into NVM
```

###### TWRITE

```
All commands except for Write
Multiple Blocks and Write
Signature
```

```
5.5 ms
```

```
Write Multiple Blocks and Write
Signature when writing two
physical blocks
```

```
10.6 ms
```

```
Authenticate (TA) AES-128 crypto
execution time for TAM
TTAM1 5.5 ms
```

```
Authenticate (TA) AES-128 crypto
execution time for TAM
```

###### TTAM

```
ProtMode = 0000 2 (plaintext) 5.5 ms
ProtMode = 00012 (encryption) 10 ms
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
7 http://www.emmicroelectronic.com
```

## 4. Product overview

em|echo-V is used in passive transponder applications and provides support for use as either an HF / UHF product, an NFC /
UHF product, or as an HF / NFC / UHF product.

The user defines the IC memory partitions which determine the size of HF User memory, UHF User memory, UHF EPC/UII
memory, and Digital Signature memory.

Both the HF / NFC and UHF interfaces have access to all of memory although access operations may be protected and require
the use of passwords or crypto. No priority is given to either air interface. The memory cannot be accessed in parallel and memory
access arbitration is performed on a per command basis as the commands are received over the air interfaces.

The user has the option to enable the tamper detection feature which checks impedance of a continuity loop at power-up between
two pads/pins to determine if the loop is intact (closed) or broken (open).

### 4.1. Overview (HF).......................................................................................................................................................................

em|echo-V corresponds to the latest generation of ISO/IEC 15693 and 18000-3 Mode 1 devices offering innovative and enriched
features.

The IC supports data rates at 6kbps, 26kbps, and 53kbps.

em|echo-V offers the maximum of flexibility in terms of security using ISO/IEC 29167-10 (AES-128) security services, or password
protection, or none at all.

Each em|echo-V chip is delivered with a unique 64 - bit inalterable UID number programmed at wafer level to ensure full traceability.

em|echo-V supports the optional Write Multiple Blocks command, enabling rapid tag encoding

The HF memory is also accessible through the UHF interface as specified later on.

The HF specific mechanisms and features do not influence UHF functionality excluding memory sharing and mechanisms which
are explicitly described.

### 4.2. Overview (NFC)

em|echo-V corresponds to the latest generation of NFC Type 5 devices offering innovative and enriched features.

The IC supports data rates at 26kbps.

The HF / NFC memory contains the NFC Capability Container, the NDEF message, and other proprietary data.

The user has the option to enable the use of the ACCESS counter and web authentication, and/or web based tamper detection.

The HF / NFC memory is also accessible through UHF interface as specified later on.

The NFC specific mechanisms and features do not influence UHF functionality excluding memory sharing and mechanisms which
are explicitly described.

### 4.3. Overview (UHF)

em|echo-V is a RAIN RFID IC compliant with ISO/IEC 18000-63 and EPC Gen2v2. It supports the Tag Alteration (Core) and
Alteration EAS application requirements to provide data privacy and EAS capability.

Each em|echo-V chip is delivered with a 96-bit inalterable TID to ensure full traceability.

em|echo-V supports the optional BlockWrite command, enabling rapid tag encoding.

The UHF memory is also accessible through HF / NFC interface as specified later on.

The UHF specific mechanisms and features do not influence HF / NFC functionality excluding memory sharing and mechanisms
which are explicitly described.

### 4.4. Functional Description

As soon as the em|echo-V enters an RF operating field (HF / NFC or UHF), the energy from the operating field is extracted to
provide power for the IC. Both fields can be present simultaneously.

em|echo-V initialization occurs during power-up (BOOT) and the device reads initial values from NVM to configure the Tag for
normal operation. The em|echo-V stays quiet and ignores all incoming communication during BOOT.

If UHF field is present, then UHF mode is available (if not killed) after BOOT and UHF interface is ready to execute commands.
If HF field is present, HF / NFC mode is available (if not killed) after BOOT and HF / NFC interface is ready to execute commands.
If both fields are present, then both HF / NFC mode and UHF mode are available and commands will be executed on a first-in,
first-served basis.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
8 http://www.emmicroelectronic.com
```

This device is in full compliance with the following documents:

###### HF :

```
 "ISO/IEC 15693 - 2:2006 Identification cards – Contactless integrated circuit cards – Vicinity cards – Part 2: Air interface
and initialization”, Publication Date: 2006- 12
 "ISO/IEC 15693 - 3:2009 Identification cards – Contactless integrated circuit cards – Vicinity cards – Part 3: Anticollision
and transmission protocol”, Publication Date: 2009- 04
 "ISO/IEC 15693 - 3:2009/Amd 2:2017 Identification cards – Contactless integrated circuit cards – Vicinity cards – Part 3:
Anticollision and transmission protocol AMENDMENT 2: Clarification of use of Data Elements”, Publication Date:
2015 - 08
 "ISO/IEC 15693 - 3:2009/Amd 4:2017 Identification cards – Contactless integrated circuit cards – Vicinity cards – Part 3:
Anticollision and transmission protocol AMENDMENT 4: Security framework”, Publication Date: 2017- 05
 "ISO/IEC 18000 - 3:2010 Information technology – Radio frequency identification for item management – Part 3:
Parameters for air interface communications at 13,56 MHz”, Publication Date: 2010- 11
 "ISO/IEC 29167 - 10:2017 Information technology – Automatic identification and data capture techniques – Part 10:
Crypto suite AES-128 security services for air interface communications”, Publication Date: 2017- 09
```

###### NFC :

```
 “NFC Forum Analog, Technical Specification, Version 2.1”, Publication Date: 2018- 02 - 19
 “NFC Forum Activity, Technical Specification, Version 2.0”, Publication Date: 2017- 04 - 30
 “NFC Forum Digital Protocol, Technical Specification, Version 2.0”, Publication Date: 2017- 05 - 09
 “NFC Forum Type 5 Tag Operation, Technical Specification, Version 1.0”, Publication Date: 2015- 07 - 07
```

###### UHF :

```
 "ISO/IEC 18000- 6 3:2015 Information technology – Radio frequency identification for item management – Part 6 3 :
Parameters for air interface communications at 860 MHz to 960 MHz Type C”, Publication Date: 2015- 10
 "EPCTM Radio-Frequency Identity Protocols, Generation-2 UHF RFID, Specification for RFID Air Interface Protocol for
Communications at 860 MHz - 960 MHz, Release 2.1, Ratified, Jul 2018” from GS
 "EPC Tag Data Standard, defines the Electronic Product CodeTM and specifies the memory content of Gen 2 RFID
Tags, Release 1.13, Ratified, Nov 2019" from GS
```

### 4.5. Functional Configurations

em|echo-V is easily configured to support many use cases. There are three main configuration aspects to be considered by the
user:

```
 HF / NFC functionality
 Memory partition sizes
 Access rights for each air interface
```

#### 4.5.1. HF / NFC Functionality

There are two considerations for selection of the HF / NFC configuration:

```
 The type of Interrogator intended for the application
o HF fixed/handheld/desktop reader
o NFC mobile device with a dedicated App
o NFC mobile device without a dedicated App (App-free)
 The type of HF / NFC security intended for the application
o Password based for HF reader or NFC mobile device with a dedicated App
o Web based one time password (OTP) crypto for NFC mobile device with or without a dedicated App, plus
password based for HF reader or NFC mobile device with a dedicated App
o Web based one time password (OTP) crypto for NFC mobile device with or without a dedicated App, plus
AES crypto based for HF reader or NFC mobile device with a dedicated App
o AES crypto based for HF reader or NFC mobile device with a dedicated App
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
9 http://www.emmicroelectronic.com
```

##### NFC ACCESS Counter

The NFC ACCESS counter provides a mechanism to monitor the amount of consumer engagement with a Tag. The NFC ACCESS
counter cannot be accessed by the HF / NFC interface but it provides the UHF interface with the number of times an NFC mobile
device initiated a polling sequence to inventory the Tag. UHF access to the counter is temporarily enabled for a UHF inventory
round by issuing either a SQUARE_0 command or a Select command with any of the assigned EM Microelectronic Mask Designer
ID’s (MDID’s) provided that the MDID is not untraceably hidden. The Select command parameters are MemBank = 10 2 , Pointer =
08h, and either Length = 0Ch with matching Mask = 00Bh or = 40Bh or = 80Bh or = C0Bh, or Length = 1 0h with matching Mask
= 00BXh or = 40BXh or = 80BXh or = C0BXh where X can be any hexadecimal value.

When enabled, the XPC_W2 Indicator (XEB) is asserted (= 1) in the XPC_W1 word, and the XPC_W2 word = NFC ACCESS
counter value or it is set to the max value of FFFFh when the NFC ACCESS counter value exceeds FFFFh.

A Tag assumes the source of the communication to be an NFC reader/writer unless it observes a command using two subcarriers
or with low data rate, then it is an HF interrogator. A Tag will not communicate with an NFC reader/writer unless the NFC ACCESS
counter has been updated. However, a Tag will communicate with an HF Interrogator with or without update of the NFC ACCESS
counter. Once the source of the communication is determined to be an HF interrogator, it remains in effect until the Tag is no
longer powered and the HF Interrogator may use any subcarrier / data rate options.

##### Web Authentication

Web authentication uses AES-128 to create a one-time password (OTP) crypto signature based on a unique Security Token that
is updated with every use, an ID for the Tag, and some status flags for the Tag. The use of the Security Token is done in a manner
that protects against anti-tearing. Approximately 200,000 Security Token values can be generated during the lifetime of the device
and the 24-bit value of F0F0F0h is used to indicate end of life for the Security Token.

The crypto signature is computed by encrypting one 128-bit data block with AES-128. The data to be encrypted is determined
by the OTP Crypto Signature Mode and is comprised of three or four elements:

```
 The 6-bit status flag values
 The 24 - bit Security Token which always increases by a random amount each time it is used
 An ID which can be 64 or 96 bits in length depending on the type of identifier
 An optional 32-bit random number for use as a dynamic Session Password value
```

```
OTP Crypto Signature Configurations
```

###### OTP

```
Crypto
Signature
Mode
```

```
Input for Encryption
(Byte Number)
```

```
0 1 2 3 4 5 6 7 8 9 A B C D E F
= 0 OTP Crypto Signature is disabled
```

```
= 1
```

###### 012 ||

```
Flags
```

```
Security
Token
```

```
HF UID Session Password
```

###### = 2

###### 012 ||

```
Flags
```

```
Security
Token
```

###### UHF TID

###### = 3

###### 012 ||

```
Flags
```

```
Security
Token
```

###### UHF EPC/UII

The ID is chosen from the following options defined by the OTP Crypto Signature Mode value:

```
 The 64-bit HF UID
 The 96-bit UHF TID
 The 96-bit UHF EPC/UII (NOTE: UHF EPC/UII should be permalocked !)
```

The crypto signature consists of the complete AES 128-bit result. Once computed, NDEF memory substitution is used to add the
crypto signature, the Security Token, the ID, and the status flags. The NDEF is read by the NFC mobile device and sent to the
server that owns a database with all the Tag IDs and their associated key together with the last valid Security Token received.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
10 http://www.emmicroelectronic.com
```

Once the NDEF is received by the server, it extracts the following data:

```
 The ID
 The Security Token
 The status flags
 The 128-bit Crypto Signature
```

The server scans the database to find the ID and the corresponding secret key and last valid Security Token stored in the
database. The server can then verify the Tag and if successfully authenticated it will update the Security Token stored in the
database. Additional processing might occur based on the status flags and the application.

A Tag assumes the source of the communication to be an NFC reader/writer unless it observes a command using two subcarriers
or with low data rate, then it is an HF Interrogator. A Tag will not communicate with an NFC reader/writer unless an updated
Security Token has been generated along with a corresponding crypto signature when it is enabled. However, a Tag will
communicate with an HF Interrogator with or without an updated Security Token but will always read the actual HF User memory
values and not the memory substitution values. Once the source of the communication is determined to be an HF Interrogator, it
remains in effect until the Tag is no longer powered and the HF Interrogator may use any subcarrier / data rate options.

A dynamic Session Password from web authentication can be used only for HF Crypto Mode = 2. If the use of a Session Password
is selected, then a 32-bit random value is generated that is used for the HF Login Password value. Note that the use of a Session
Password is an indirect crypto authentication of the NFC reader/writer. The Session Password remains valid until an unsuccessful
Login occurs or the Tag no longer detects an HF field.

##### AES Crypto

AES crypto uses ISO/IEC 29167-10:2017 security services to support Tag Authentication (TA). Refer to Security Services Using
ISO/IEC 29167-10 (AES-128) for further information.

##### HF / NFC Functional Configurations

The functionality is set via the HF Crypto Mode value during device configuration by the user. Refer to User Configuration for
further information.

```
HF / NFC Functional Configurations
```

###### HF

```
Crypto
Mode
```

```
HF Login
Password
```

```
Web
Based
OTP
Crypto
```

###### AES TA

```
Crypto
```

```
Private
Memory
(optional)
```

###### NFC

###### ACCESS

```
Counter
```

###### OTP

```
Security
Token
```

###### NDEF

```
Tamper
Swap
(optional)
```

###### NDEF

```
Memory
Substitution
```

```
= 0  - -  - - - -
= 1   -     
```

```
= 2
Session
Password
```

######       

###### = 3

```
Session
Password
```

###### -   - - - -

###### NOTES:

- HF Login Password = zero is the same as having no protection.
- NFC ACCESS Counter, OTP Security Token, and NDEF Memory Substitution require using an NFC reader/writer (e.g.
  smartphone) supporting NFC Type A (NFC-A) and NFC Type 5 (NFC-V).
- OTP crypto key = AES KeyID1 and may be used for Tag Authentication using TAM1 when HF Crypto Mode = 2 but it will not
  generate a Session Password using TAM1.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
11 http://www.emmicroelectronic.com
```

#### 4.5.2. Memory Partition Sizes

There are four considerations for selection of the memory partition sizes:

```
 The need for a private Digital Signature for the application
 The amount of HF User memory needed for the application
 The bit length needed for the EPC/UII encoding for the application
 The amount of UHF User memory needed, if any, for the application
```

The private Digital Signature memory, if enabled, supports commonly used signature sizes of 256 bits, 384 bits, or 512 bits. Refer
to User Configuration for further information.

HF User memory is allocated in physical blocks of 64 bits (8 bytes). Many HF applications use less than 1K bits (16 blocks).
However, NFC Type 5 applications might need more than 1K bits as this would limit the NDEF message size to 120 bytes. Refer
to User Configuration for further information.

EPC/UII memory is also allocated in physical blocks of 64 bits (4 words). Refer to User Configuration for further information.

```
EPC/UII Memory Size Configurations
```

```
EPC/UII
Block
Size
```

```
MSW LSW
```

```
Encoding
Size
(# bits)
```

```
Typical Applications
```

```
= 0 StoredCRC StoredPC EPC/UII Word 0 EPC/UII Word 1 32 n/a
```

```
= 1 EPC/UII Word 2 EPC/UII Word 3 EPC/UII Word 4 EPC/UII Word 5 96
Retail (e.g. SGTIN-96),
Logistics, IATA, IPC, Tolling
= 2 EPC/UII Word 6 EPC/UII Word 7 EPC/UII Word 8 EPC/UII Word 9 160 Animal ID
= 3 EPC/UII Word 10 EPC/UII Word 11 EPC/UII Word 12 EPC/UII Word 13 224 Retail (e.g. SGTIN-198)
= 4 EPC/UII Word 14 EPC/UII Word 15 EPC/UII Word 16 EPC/UII Word 17 288 VDA
= 5 EPC/UII Word 18 EPC/UII Word 19 EPC/UII Word 20 EPC/UII Word 21 352 ATA
= 6 EPC/UII Word 22 EPC/UII Word 23 EPC/UII Word 24 EPC/UII Word 25 416 ATA
= 7 EPC/UII Word 26 EPC/UII Word 27 EPC/UII Word 28 EPC/UII Word 29 480 ATA
```

UHF User memory is also allocated in physical blocks of 64 bits (4 words). Refer to User Configuration for further information.
Not all applications require UHF User memory. When it is required, it is typically very little (e.g. 4 words or less for EAS systems)
or as much as possible (e.g. ATA single record or dual record).

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
12 http://www.emmicroelectronic.com
```

#### 4.5.3. Access Rights for each Air Interface

The HF air interface uses the HF Login Password, web authentication, and AES crypto security services to perform restricted
operations. No restrictions exist when the password is zero and no crypto keys are activated. Otherwise, the Tag only performs
restricted operations if the Interrogator can be authenticated by correctly providing the Session Password or nonzero HF Login
Password.

The UHF air interface uses the Secured state to perform restricted operations. The Tag obtains the Secured state if the UHF
Access Password for the Tag is zero or if the Interrogator can be authenticated by correctly providing the nonzero UHF Access
Password for the Tag. Some operations specifically require that the Tag obtain the Secured state as a result of the Interrogator
using a nonzero UHF Access Password. The Tag can be killed if the Interrogator can be authenticated by correctly providing the
nonzero UHF Kill Password for the Tag.

Access rights for the Tag can be generally summarized as follows:

```
 A password = 0 means the password is undefined and cannot be used for access
 No activated crypto keys means crypto operations or a Session password cannot be used for access
 A valid Session password including zero is equivalent to a non-zero HF Login password
 When HF memory access does NOT require the HF interface to use a nonzero HF password or activated crypto key
then the UHF interface can always access HF memory
 When HF memory access requires the HF interface to use a nonzero HF password then the UHF interface can only
access HF memory when it has a nonzero UHF password
 When HF memory access requires the HF interface to use an activated crypto key then the UHF interface cannot access
HF memory
 When UHF memory access does NOT require the UHF interface to use a nonzero UHF password then the HF interface
can always access UHF memory
 When UHF memory access requires the UHF interface to use a nonzero UHF password then the HF interface can only
access UHF memory when it has a Session Password or nonzero HF password
 Digital Signature memory access requires the HF interface to use a Login with a Session password or a nonzero HF
password and the UHF interface to use a nonzero UHF password to read and if the HF interface is configured to have
no access then a nonzero UHF password to write
 Any memory that is locked/permalocked applies to both interfaces
 Any memory access that requires a specific privilege applies to both interfaces (e.g. UHF Untraceable)
```

Refer to User Configuration for access rights prior to activation of the HF / NFC security services (i.e. HF Crypto Mode).

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
13 http://www.emmicroelectronic.com
```

## 5. Memory

em|echo-V supports multiple memory configurations and the selection is user programmable. The user defines the IC memory
partitions which determine the size of HF User memory, UHF User memory, UHF EPC/UII memory, and Digital Signature memory.
Both the HF / NFC and UHF interfaces have access to all of memory although access operations may be protected and require
the use of passwords or crypto. Refer to User Configuration for further information.

### 5.1. HF / NFC Interface

Physical memory is organized in blocks of 64 bits, 8 bytes per block. Logical memory may be organized as either blocks of 32 bits
or 64 bits depending upon the HF Crypto Mode and if legacy Android support is enabled. See User Configuration.

#### 5.1.1. HF / NFC Memory Map

```
HF User Memory when HF Crypto Mode = 0 or 3, or when Legacy Android is disabled and HF Crypto Mode = 1 or 2
```

```
Memory Bank Block Address Contents Block Number
for Lock Block
Comment
```

```
HF User
```

```
00 h User Block 0 0
Max block address is defined by the size of HF User memory
partition. See User Configuration.
```

```
Memory segments above the max block address are
included within the HF User Memory Bank address space
but are not part of the HF User memory.
```

```
01 h User Block 1 1
```

```
... ... ...
```

```
1Eh User Block 30 30
```

```
1Fh User Block 31 31
```

```
20h - 27h Digital Signature 32 - 39
Max block address is defined by size of Digital Signature memory
partition. See User Configuration.
```

```
28h - 2Fh UHF Reserved n/a
```

```
Block 28h : Word 0h || Word 1h (Kill Password)
Block 28h : Word 2h || Word 3h (Access Password)
Blocks 29h - 2Fh : see User Configuration
```

```
30h - 37h UHF EPC/UII n/a
```

```
Block 30h : Word 0h || Word 1h || Word 2h || Word 3h
...
Block 37h : Word 1Ch || Word 1Dh || Word 1Eh || Word 1Fh
Max block address is defined by size of UHF EPC/UII memory
partition. See User Configuration.
```

```
38h - 39h UHF TID n/a
```

```
Block 38h : Word 0h || Word 1h || Word 2h || Word 3h
Block 39h : Word 4h || Word 5h || 0000’0000h
```

```
3Ah - 58h UHF User 58 - 88
```

```
Block 3Ah : Word 0h || Word 1h || Word 2h || Word 3h
...
Block 58h : Word 78h || Word 79h || Word 7Ah || Word 7Bh
Max block address is defined by size of UHF User memory
partition. See User Configuration.
```

```
60h - 61h System memory n/a
```

```
Block 60h : System Configuration Block 1
Block 61h : System Configuration Block 2
See User Configuration.
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
14 http://www.emmicroelectronic.com
```

```
HF User Memory when Legacy Android is enabled and HF Crypto Mode = 1 or 2
```

```
Memory
Bank
```

```
32 - bit
Logical
Block
Address
```

```
64 - bit
Physical
Block
Address
```

```
Contents
```

```
Block Number
for Lock Block
```

```
Locking a
logical block
will lock both
logical blocks
that share a
physical block
```

```
Comment
```

```
(All references to block addresses are to physical 64-bt blocks)
```

HF User

```
00 h 00 h msb’s User Block 0 0
Max block address is defined by the size of HF User memory
partition. See User Configuration.
```

```
Memory segments above the max block address are
included within the HF User Memory Bank address space
but are not part of the HF User memory.
```

```
01 h 00h lsb’s User Block 1 1
... ... ... ...
```

```
3Eh 1Fh msb’s User Block 62 62
```

```
3Fh 1Fh lsb’s User Block 63 63
```

```
40h - 4Fh 20h - 27h
Digital
Signature
64 - 79
Max block address is defined by size of Digital Signature
memory partition. See User Configuration.
```

```
50h - 5Fh 28h - 2Fh UHF Reserved n/a
```

```
Block 28h : Word 0h || Word 1h (Kill Password)
Block 28h : Word 2h || Word 3h (Access Password)
Blocks 29h - 2Fh : see User Configuration
```

```
60h - 6Fh 30h - 37h UHF EPC/UII n/a
```

```
Block 30h : Word 0h || Word 1h || Word 2h || Word 3h
...
Block 37h : Word 1Ch || Word 1Dh || Word 1Eh || Word 1Fh
Max block address is defined by size of UHF EPC/UII memory
partition. See User Configuration.
```

```
70h - 73h 38h - 39h UHF TID n/a
```

```
Block 38h : Word 0h || Word 1h || Word 2h || Word 3h
Block 39h : Word 4h || Word 5h || 0000’0000h
```

```
74h - B1h 3Ah - 58h UHF User 116 - 177
```

```
Block 3Ah : Word 0h || Word 1h || Word 2h || Word 3h
...
Block 58h : Word 78h || Word 79h || Word 7Ah || Word 7Bh
Max block address is defined by size of UHF User memory
partition. See User Configuration.
```

```
C0h - C3h 60h - 61h
System
memory n/a^
```

```
Block 60h : System Configuration Block 1
Block 61h : System Configuration Block 2
See User Configuration.
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
15 http://www.emmicroelectronic.com
```

#### 5.1.2. HF Unique Identifier (UID)

The HF UID is a 64-bit value used to ensure the uniqueness of each device. It is programmed by EM during wafer test and cannot
be changed. The 48-bit IC Serial Number used for the HF UID is identical to the 48-bit IC Serial Number used for the UHF TID.

###### HF UID [63:56] HF UID [55:48] HF UID [47:0]

###### ISO/IEC 15963

```
Allocation Class (= E0h)
```

```
IC Manufacturer
Code (= 16h)
```

```
IC Serial Number
[47:40]
```

```
IC Serial
Number [39:32]
```

```
IC Serial Number [31:0]
```

```
78h for em|echo-V Customer
Number
```

The 48-bit IC Serial Number used for the HF UID is identical to the 48-bit IC Serial Number used for the UHF TID. The 48-bit IC
Serial Number is encoded with even parity. An Interrogator should calculate even parity with bitwise exclusive-OR as follows:

```
P = IC Serial Number [47]  IC Serial Number [46]  ...  IC Serial Number [1]  IC Serial Number [0]
```

If P = 0 then the IC Serial Number is correct. If P = 1 then the IC Serial Number has an error in it.

#### 5.1.3. NFC Type 5 Tag Data Structure

The data structure starts with Byte 0 of User Block 0 and consists of two fields, starting with the Capability Container (CC) and
immediately followed by the Data area. The CC manages the information of the Type 5 Tag and the size (length in bytes) of the
Data area is defined by the content of the CC.

```
HF User Memory when used for NFC Type 5 Tag (Legacy Android format disabled)
```

```
User Block Byte 0 Byte 1 Byte 2 Byte 3 Byte 4 Byte 5 Byte 6 Byte 7
0 CC byte 0 CC byte 1 CC byte 2 CC byte 3 CC byte 4 CC byte 5 CC byte 6 CC byte 7
Magic Byte Version &
Access
```

```
Memory size
(total / 8)
```

```
Type 5 Tag RFU RFU Memory size
(total / 8) msb’s
```

```
Memory size
(total / 8) lsb’s
e.g. E1h e.g. 40h e.g. 00h e.g. 09 h e.g. 00 h e.g. 00 h e.g. 00 h e.g. 20h
1 Data byte 0 Data byte 1 Data byte 2 Data byte 3 Data byte 4 Data byte 5 Data byte 6 Data byte 7
2 Data byte 8 Data byte 9 Data byte 10 Data byte 11 Data byte 12 Data byte 13 Data byte 14 Data byte 15
... ... ... ... ... ... ... ... ...
30 Data byte 232 Data byte 233 Data byte 234 Data byte 235 Data byte 236 Data byte 238 Data byte 238 Data byte 239
31 Data byte 240 Data byte 241 Data byte 242 Data byte 243 Data byte 244 Data byte 245 Data byte 246 Data byte 247
NOTE: Memory Size is defined by the size of HF User memory partition (see User Configuration).
```

```
HF User Memory when used for NFC Type 5 Tag (Legacy Android format enabled)
```

```
User Block Byte 0 Byte 1 Byte 2 Byte 3
0 CC byte 0 CC byte 1 CC byte 2 CC byte 3
Magic Byte Version &
Access
```

```
Memory size
(total / 8)
```

```
Type 5 Tag
```

```
e.g. E1h e.g. 40h e.g. 20h e.g. 09 h
1 Data byte 0 Data byte 1 Data byte 2 Data byte 3
2 Data byte 4 Data byte 5 Data byte 6 Data byte 7
3 Data byte 8 Data byte 9 Data byte 10 Data byte 11
4 Data byte 12 Data byte 13 Data byte 14 Data byte 15
5 Data byte 16 Data byte 17 Data byte 18 Data byte 19
... ... ... ... ...
60 Data byte 236 Data byte 237 Data byte 238 Data byte 239
61 Data byte 240 Data byte 241 Data byte 242 Data byte 243
62 Data byte 244 Data byte 245 Data byte 246 Data byte 247
63 Data byte 248 Data byte 249 Data byte 250 Data byte 251
NOTE: Memory Size is defined by the size of HF User memory partition (see User Configuration).
```

The user can configure the HF User memory to have NDEF messages supporting advanced features in em|echo-V using NDEF
Tamper Swap and NDEF Memory Substitution. Refer to User Configuration for further information.

NDEF Tamper Swap provides a means to generate one NDEF message when a tamper event has NOT been detected, and to
generate a different NDEF message when a tamper event has been detected. This permits having an app-free tamper detection
mechanism whereby a normal condition versus tamper condition can be reported by using two different URL’s.

NDEF Memory Substitution provides a means to insert fixed ID values and the dynamic values for Flags, Security Token, and
crypto signature into URL’s. This permits having an app-free web authentication mechanism for the Tag. The user defines which
Data bytes, if any, will be used for the HF UID, UHF TID, UHF EPC/UII, Security Token, and crypto signature. em|echo-V then
substitutes computed values for the actual corresponding NVM values. All computed values use "URL and Filename safe" Base
64 (base64url) encoding in accordance with IETF RFC 4648. The data lengths are implicitly known so PAD characters (PAD
character is ‘=’) are not used but padding zeros as lsb’s are used when needed to complete a 6-bit value.

# Identiv

DATASHEET

Copyright  2021 , EM Microelectronic-Marin SA
16 [http://www.emmicroelectronic.com](http://www.emmicroelectronic.com)

```
 Flags = 6 bits = 1 character in base64url
```

- Flags = ID length || Tamper Alarm || UHF Kill flag || UHF Range Reduction flag || UHF TN Flag || RFU
- ID length: 0 = 96 bits (UHF TID or UHF EPC/UII), 1 = 64 bits (HF UID)
   HF UID = 64 bits = 11 characters in base64url after padding with 2 zero lsb’s to make 66 bits
   UHF TID = 96 bits = 16 characters in base64url
   UHF EPC/UII = 96 bits = 16 characters in base64url
   Security Token = 24 bits = 4 characters in base64url with null value = ‘AAAA’
   Crypto signature = 128 bits = 22 characters in base64url after padding with 4 zero lsb’s to make 132 bits with null value
  = ‘AAAAAAAAAAAAAAAAAAAAAA’

```
Base64url Alphabet
```

```
6 - bit
value
```

```
Encoding
character
```

```
6 - bit
value
```

```
Encoding
character
```

```
6 - bit
value
```

```
Encoding
character
```

```
6 - bit
value
```

```
Encoding
character
0000002 A 0100002 Q 1000002 g 1100002 w
0000012 B 0100012 R 1000012 h 1100012 x
0000102 C 0100102 S 1000102 i 1100102 y
0000112 D 0100112 T 1000112 j 1100112 z
0001002 E 0101002 U 1001002 k 1101002 0
0001012 F 0101012 V 1001012 l 1101012 1
0001102 G 0101102 W 1001102 m 1101102 2
0001112 H 0101112 X 1001112 n 1101112 3
0010002 I 0110002 Y 1010002 o 1110002 4
0010012 J 0110012 Z 1010012 p 1110012 5
0010102 K 0110102 a 1010102 q 1110102 6
0010112 L 0110112 b 1010112 r 1110112 7
0011002 M 0111002 c 1011002 s 1111002 8
0011012 N 0111012 d 1011012 t 1111012 9
0011102 O 0111102 e 1011102 u 1111102 - (minus)
0011112 P 0111112 f 1011112 v 1111112 _ (underline)
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
17 http://www.emmicroelectronic.com
```

### 5.2. UHF Interface

Physical memory is organized in blocks of 64 bits, 4 words per block.

#### 5.2.1. UHF Memory Map

```
Memory Bank Word Address Contents
```

```
Physical Block
Number for
BlockPermalock
```

```
Comment
```

```
002 : Reserved
```

```
00 h - 01 h Kill Password n/a
02 h - 03 h Access Password n/a
```

```
0 4h - 1Fh HF Pswds/Keys n/a See Crypto Key Blocks / Words
```

```
012 : EPC/UII
```

```
00 h StoredCRC n/a
```

```
01 h StoredPC n/a
```

```
02h - 1Fh EPC/UII n/a
Max word address is defined by the size of EPC/UII
memory partition. See User Configuration.
```

```
21 h XPC_W1 n/a
```

```
2 2h XPC_W 2 n/a
```

```
See
NFC ACCESS Counter.
```

```
102 : TID 00 h - 05h TID n/a
```

```
112 : User (File_0)
```

```
00h - 03h
```

```
UHF User memory
```

```
0 Max word address is defined by size of UHF User memory
partition. See User Configuration.
```

```
Memory segments for Digital Signature, HF User
memory, and System memory are included within the
UHF User Memory Bank address space but are not
part of the UHF User memory. Read and BlockWrite
operations cannot span multiple memory segments.
```

```
0 4h - 07h 1
```

```
74h - 77h 29
```

```
78h - 7Bh 30
```

```
80h - 9 Fh Digital Signature 32 - 39 Max word address is defined by size of Digital Signature
memory partition. See User Configuration.
```

```
A0h - 11Bh HF User memory 40 - 70
Max word address is defined by size of HF User memory
partition (see User Configuration).
120h - 1 2Dh System memory n/a See User Configuration.
```

#### 5.2.2. Reserved Memory Bank

Reserved memory is as defined in ISO/IEC 18000- 63 and EPC Gen2V2 specs.

```
Word
```

###### MSB

###### 0

###### 1

###### 2

###### 3

###### 4

###### 5

###### 6

###### 7

###### 8

###### 9

###### A

###### B

###### C

###### D

###### E

###### LSB

###### F

```
0h Kill Password [31:16]
1h Kill Password [15:0]
2h Access Password [31:16]
3h Access Password [15:0]
4h
... See Crypto Key Blocks / Words
1Fh
```

#### 5.2.3. EPC/UII Memory Bank

EPC/UII memory is as defined in ISO/IEC 18000- 63 and EPC Gen2V2 specs.

The Tag reply to an ACK includes a field that is commonly referred to as the PC word. The format and definition of the PC word
for Gen2V2 is significantly different than in prior versions of the EPC Gen2 V1.x specifications. The em|echo-V supports
applications that require the legacy PC format and corresponding definition when enabled by the user during Tag configuration.
Refer to User Configuration for further information. A description of the PC word in the ACK reply is provided below.

A memory self-check is performed for the EPC/UII memory at every power-up if the EPC/UII Memory Bank is write permalocked.
The self-check compares the result of the dynamic CRC calculation for the StoredCRC during power-up with the static CRC

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
18 http://www.emmicroelectronic.com
```

calculation for the StoredCRC stored in NVM. If the CRC values do not match then Tag will reply to ACK with L = 00000 2 in the
PC word regardless of the actual value. An Interrogator could then consider the Tag for exception handling.

The XPC_W1 word existed in previous versions of the air interface protocol but it is now used much more extensively in the
ISO/IEC 18000- 63 and EPC Gen2V2 specs. For Gen2V2, if the XPC_W1 word has a non-zero value then it is reported to an
Interrogator as part of the reply to an ACK. Furthermore, if the XPC_W1 indicator (XI) = 1 in the PC word then the entire XPC_W
word is included in the reply to an ACK. Otherwise, the 8 LSB’s of the XPC_W1 word are reported as the 8 LSB’s of the PC word
in the reply to an ACK. A description of the XPC_W1 word is provided below.

```
PC word in ACK reply (Legacy Gen2 format disabled = Gen2 V2.0)
```

```
Word
```

###### MSB

###### 0

###### 1

###### 2

###### 3

###### 4

###### 5

###### 6

###### 7

###### 8

###### 9

###### A

###### B

###### C

###### D

###### E

###### LSB

###### F

```
1h
```

```
L (Length) UMI XI=0 T=0 B C SLI TN U K NR H
L (Length) UMI XI=1 T=0 RFU
L (Length) UMI XI T=1 ISO Application Family Identifier (AFI)
StoredPC : L is writeable, UMI is fixed = 0 when UHF User memory does not exist and is fixed = 1 when UHF User memory
does exist, XI is computed, T is writeable, AFI is writeable, and RFU is fixed = 00h
```

```
PC word in ACK reply (Legacy Gen2 format enabled = Gen2 V1.x)
```

```
Word
```

###### MSB

###### 0

###### 1

###### 2

###### 3

###### 4

###### 5

###### 6

###### 7

###### 8

###### 9

###### A

###### B

###### C

###### D

###### E

###### LSB

###### F

```
1h
```

```
L (Length) UMI XI=0 T=0 Numbering System Identifier (NSI) LSB’s
L (Length) UMI XI=0 T=1 ISO Application Family Identifier (AFI)
StoredPC : L is writeable, UMI is writeable, XI is fixed = 0, T is writeable, AFI is writeable, and NSI is writeable
```

```
XPC_W1 word
```

```
Word
```

###### MSB

###### 0

###### 1

###### 2

###### 3

###### 4

###### 5

###### 6

###### 7

###### 8

###### 9

###### A

###### B

###### C

###### D

###### E

###### LSB

###### F

```
21h XEB 0 0 0 SA 0 0 0 B C=0 SLI TN U=0 K NR H
```

```
 XEB (XPC_W2 indicator): This bit is used to indicate XPC_W2 word is included as part of an ACK reply. See
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
19 http://www.emmicroelectronic.com
```

```
 NFC ACCESS Counter for more information.
```

```
 SA (Sensor alarm indicator): This bit is used as the Tamper Alarm indicator. If bit is 0 then either tamper detection is
not enabled or the Tag is not reporting a Tamper Alarm condition. If bit is 1 then the Tag is reporting a Tamper Alarm
condition.
```

```
 B (Battery assisted passive indicator): This bit is used to indicate the device is in an HF field or the device is configured
to support an external capacitor to provide power for the Tag. If bit is 0 then the Tag is not detecting an HF field and the
device is not configured to support an external capacitor to provide power for the Tag. If bit is 1 then the Tag is detecting
an HF field or the Tag does support an external capacitor, and the read range of the Tag may increase significantly.
```

```
 C (Computed response indicator): Not supported by em|echo-V.
```

```
 SLI (SL-flag indicator): If bit is 0 then a Tag has a deasserted SL flag. If bit is 1 then a Tag has an asserted SL flag.
Upon receiving a Query the Tag maps its SL flag into the SLI and retains this SLI setting until starting a subsequent
inventory round.
```

```
 TN (Tag-notification indicator): This bit is used to indicate the state of the TN function defined by the application.
```

```
 U (Untraceable indicator): Not supported by em|echo-V.
```

```
 K (Killable indicator): If bit is 0 then the Tag is not killable using the Kill password. If bit is 1 then the Tag is killable
using the Kill password. Logically, K is defined as:
K = [(logical OR of all 32 bits of the kill password) OR (kill-pwd-read/write = 0) OR (kill-pwd-permalock = 0)].
o If any bits of the kill password are 1 then the Tag is killable
o If kill-pwd-read/write is 0 then the Tag is killable
o If kill-pwd-permalock is 0 then the Tag is killable
```

```
 NR (Nonremovable indicator): If bit is 0 then the Tag is removable. If bit is 1 then the Tag is nonremovable. This bit is
always 0 unless changed by an Interrogator via a Write or BlockWrite.
```

```
 H (Hazmat indicator): If bit is 0 then the Tag is not affixed to hazardous material. If bit is 1 then the Tag is affixed to
hazardous material. This bit is always 0 unless changed by an Interrogator via a Write or BlockWrite.
```

#### 5.2.4. TID Memory Bank

TID memory is as defined in ISO/IEC 18000- 63 and EPC Gen2V2 specs.

```
Word
```

###### MSB

###### 0

###### 1

###### 2

###### 3

###### 4

###### 5

###### 6

###### 7

###### 8

###### 9

###### A

###### B

###### C

###### D

###### E

###### LSB

###### F

```
0 h
```

```
ISO/IEC 15963 Allocation Class (= E2h) Tag MDID MSB’s (= 80h)
1 1 1 0 0 0 1 0 1 0 0 0 0 0 0 0
```

```
1 h
```

```
Tag MDID LSB’s
(= Bh)
```

```
Tag Model Number (TMN)
```

```
Model
```

```
User Options
(default = 0h)
1 0 1 1 0 0 0 1 0 0 0 1 see below
```

```
2 h
```

```
XTID (= 2000h)
0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0
```

```
3h
```

```
IC Serial Number [47:32]
78h for em|echo-V Customer Number
4 h IC Serial Number [31:16]
5 h IC Serial Number [15:0]
```

```
User Options from Configuration Word in System memory
MSB
C
D
E
```

```
LSB
F
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
20 http://www.emmicroelectronic.com
```

```
HF Crypto Mode
0 = Password based for HF reader or NFC mobile device
with a dedicated App
1 = Web based one time password (OTP) crypto for NFC
mobile device with or without a dedicated App, plus
password based for HF reader or NFC mobile device with
a dedicated App
2 = Web based one time password (OTP) crypto for NFC
mobile device with or without a dedicated App, plus AES
crypto based for HF reader or NFC mobile device with a
dedicated App
3 = AES crypto based for HF reader or NFC mobile
device with a dedicated App
```

```
Supports Tamper Detection
0 = Does not support tamper
detection
1 = Does support tamper
detection
```

```
Legacy Gen
0 = PC Word (StoredPC, PacketPC) is as
defined in Gen2V
1 = PC Word (StoredPC, PacketPC) is
compatible with prior versions of Gen2. All
bits in the StoredPC are writeable except XI
which is set = 0 regardless of the XPC_W
and XPC_W2 values
```

```
NOTE: Refer to User Configuration for further information.
```

The 48-bit IC Serial Number used for the UHF TID is identical to the 48-bit IC Serial Number used for the HF UID. The 48-bit IC
Serial Number is encoded with even parity. An Interrogator should calculate even parity with bitwise exclusive-OR as follows:

```
P = IC Serial Number [47]  IC Serial Number [46]  ...  IC Serial Number [1]  IC Serial Number [0]
```

If P = 0 then the IC Serial Number is correct. If P = 1 then the IC Serial Number has an error in it.

#### 5.2.5. User Memory Bank

The User Memory Bank contains four segments: UHF User memory, Digital Signature memory, HF User memory, and System
memory. UHF User memory, also known as File_0, is as defined in ISO/IEC 18000- 63 and EPC Gen2V2 specs. Digital Signature
memory is as defined below in User Configuration. HF User memory comprises the User Blocks as defined in HF / NFC Memory
Map. System memory is as defined below in User Configuration.

### 5.3. Memory Delivery State

The memory delivery state has the following default configuration and refer to User Configuration for further information:

```
 HF Crypto Mode = 0
 Tamper Detection and Digital Signature memory are disabled
 UHF TID and HF UID are programmed and write-permalocked
 UHF EPC/UII memory size is defined to be 8 words with a default 96-bit EPC encoded value that is a copy of the
96 - bit UHF TID memory
 UHF Access Password and UHF Kill Password are readable/writeable and each with a value 0000'0000h
 HF Login Password is writeable and has a value 0000'0000h
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
21 http://www.emmicroelectronic.com
```

## 6. HF / NFC Commands

```
Command
Code Function^
```

```
Request Mode
```

```
Option Flag
```

```
Comment
```

```
Inventory
Addressed
Non Addressed
```

```
Select
AFI
```

```
01h Inventory  - - -  -
02h Stay Quiet -  - - - -
20h Read Single Block -    - 
Private memory access requires a Login 10)11) using HF Login
Password.
21h Write Single Block -    - 
Private memory access requires a Login 10)11) using HF Login
Password.
22h Lock Block -    - 
Private memory access requires a Login 10)11) using HF Login
Password.
23h Read Multiple Blocks -    -  Private memory access requires a Login^
```

```
10)11) using HF Login
Password.
```

```
24h Write Multiple Blocks -    - 
```

```
Supports writing one or more logical blocks that are within two
physical blocks. Private memory access requires a Login 10)11)
using HF Login Password.
25h Select -  - - - -
26h Reset to Ready -    - -
27h Write AFI -    -  Requires a Login 10) using the HF Login Password.
28h Lock AFI -    -  Requires a Login 10) using the HF Login Password.
29h Write DSFID -    -  Requires a Login 10) using the HF Login Password.
2Ah Lock DSFID -    -  Requires a Login 10) using the HF Login Password.
2Bh Get System Information -    - - Number of Blocks = Number of logical User Blocks in HF User
Memory partition.
2Ch Get Multiple Block Security Status -    - -
35h Authenticate -    - - Command is processed^ only when^ HF Crypto Mode = 2 or 3.^
ResponseBuffer is not supported.
3Bh Extended Get System Information -    -  Command is processed only when HF Crypto Mode = 2 or 3.
A2h/BCh Quiet Storage -  - - - - Either command code may be used.
```

```
A5h/BDh Read Signature -    - 
```

```
Command is processed only when Digital Signature memory
exists. Either command code may be used. Requires a Login
using the HF Login Password and HF Access to Digital
Signature is enabled (see User Configuration).
```

```
A6h Write Signature -    - 
```

```
Command is processed only when Digital Signature memory
exists. Requires a Login using the HF Login Password and HF
Access to Digital Signature is enabled (see User
Configuration).
B0h Inventory Block Read  - - -  
Private memory access requires a Login 10)11) using HF Login
Password.
B4h Change Password -  -  -  Requires a Login 10) using the HF Login Password.
B9h Destroy -  -  - 
Requires a Login using the HF Login Password having the HF
DESTROY privilege.
BAh Enable Privacy -  -  - 
Requires a Login using the HF Login Password having the HF
PRIVACY/RANDOM ID privilege.
BBh Disable Privacy -  -  - 
Requires a Login using the HF Login Password having the HF
PRIVACY/RANDOM ID privilege.
BEh Enable Random ID -  -  -  Requires a Login^ using the HF Login Password having the HF
PRIVACY/RANDOM ID privilege.
BFh Disable Random ID -  -  -  Requires a Login^ using the HF Login Password having the HF
PRIVACY/RANDOM ID privilege.
C3h Fast Read Multiple Blocks -    -  Private memory access requires a Login^
```

```
10)11) using HF Login
Password.
C4h/E4h Login -  -  - -
Failed Login command results in a security timeout (~75ms
typical).
Note 1 0 : No Login is required when HF Crypto Mode = 0 or 1, and the HF Login Password value is zero
Note 1 1 : Access to UHF untraceably hidden memory requires the HF Login Password UHF Untraceable Privilege = 1 and a Login with a
nonzero HF Login Password value when HF Crypto Mode = 0 or 1, or a Login with the dynamic Session Password when HF Crypto Mode =
2 or 3
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
22 http://www.emmicroelectronic.com
```

#### 6.1.1. Authenticate Command

Authenticate was introduced in ISO/IEC 15693 - 3:2009/Amd 4:2017 as an optional command (command code = 35h). The
Authenticate command is used to perform Tag authentication.

The ISO/IEC 29167 - 10 (AES-128) Cryptographic Suite (CS) specifies the payload message formatting, the number of steps in an
authentication, whether an authentication implements wait states, the behavior if the Tag or Interrogator cannot complete a
computation, and the behavior in the event of an incorrect cryptographic response.

If the Tag receives an Authenticate specifying a CSI ≠ 00h then the Tag shall not execute the Authenticate and remain in the
current state. If the Tag receives an Authenticate specifying a CSI = 00h and an improperly formatted or not-executable payload
message, or an improper cryptographic parameter, then the Tag shall not execute the Authenticate and instead treat the command
parameters as unsupported, reset the crypto engine, transition to Ready state, and initiate a security timeout. If the Tag receives
an Authenticate specifying a CSI = 00h and a security timeout is in progress, then the Tag shall not execute the Authenticate and
send the generic cryptographic error code and remain in its current state.

If a Tag in Secured state receives an Authenticate that begins a new authentication then the Tag shall return to Ready state if the
Select_flag is deasserted (= 0) and shall return to Selected state if the Select_flag is asserted (= 1). It shall reset the crypto engine
and then begin the new authentication procedure.

If the Tag receives a properly formatted Authenticate but there is a cryptographic error, then the Tag shall reset the crypto engine
and initiate a security timeout.

NDEF Memory Substitution is disabled during processing of this command. If the Tag receives an Authenticate and HF Crypto
Mode = 2 then it shall invalidate the OTP crypto signature.

```
Authenticate Request Format
Interrogator
=> Tag SOF^ Flags^ Command Code^ UID^ CSI^ Payload Message^ CRC-^16 EOF^
# bits 8 8 64 8 multiples of 8 16
description 35h HF UID 00h size and content defined by the AES-128 CS CRC- 16
```

Request parameter:
(Optional) UID
CSI
Payload Message

```
Authenticate Response Format
Tag =>
Interrogator SOF^ Flags^ Barker Field^ Payload Message^ CRC-^16 EOF^
# bits 8 8 multiples of 8 16
description 04h A7h size and content defined by the AES-128 CS CRC- 16
```

Response parameter:
Barker Field
Payload Message

#### 6.1.2. Extended Get System Information Command

Extended Get System Information was introduced in ISO/IEC 15693 - 3:2009/Amd 4:2017 as an optional command (command
code = 3Bh) and allows for retrieving the system information from the Tag.

```
Extended Get System Information Request Format
Interrogator
=> Tag
SOF Flags Command Code Get System Info
parameter request field
UID CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description 3Bh HF UID CRC- 16
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
23 http://www.emmicroelectronic.com
```

Request parameter:
Get System Info parameter request field:

```
Bit Flag Name Value Description
```

```
b1 DSFID
```

```
0 No request of HF DSFID
1 Request of HF DSFID
```

```
b2 AFI
```

```
0 No request of HF AFI
1 Request of HF AFI
```

```
b3 Tag memory size
0 No request of data field on Tag memory size
1 Request of data field on Tag memory size
```

```
b4 IC reference
```

```
0 No request of Information on IC reference
1 Request of Information on IC reference
b5 MOI 1 Information on MOI always returned in response flag
```

```
b6 Tag command list
```

```
0 No request of Data field of all supported commands
1 Request of Data field of all supported commands
```

```
b7 CSI information
0 No request of CSI list
1 Request of CSI list
b8 RFU 0 Reserved for future use
```

```
(Optional) UID
```

```
Extended Get System Information Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Extended Get System Information Response Format when Error_flag is NOT set
Tag =>
Interrogator
SOF Flags Info
Flags
UID DSFID AFI Other
Fields
CRC- 16 EOF
```

```
# bits
8 8 64 8 8
see
below 16
description HF UID HF DSFID HF AFI CRC- 16
```

Response parameter:
Error Code
Info Flags:

```
Bit Flag Name Value Description
```

```
b1 DSFID
```

```
0 HF DSFID is not present
1 HF DSFID is present
```

```
b2 AFI
0 HF AFI is not present
1 HF AFI is present
```

```
b3 Tag memory size
0 Data field on Tag memory size is not present
1 Data field on Tag memory size is present
```

```
b4 IC reference
```

```
0 Information on IC reference field is not present
1 Information on IC reference field is present
b5 MOI 0 1 byte memory addressing
```

```
b6 Tag command list
0 Data field on all supported commands is not present
1 Data field on all supported commands is present
```

```
b7 CSI information
```

```
0 CSI list is not present
1 CSI list is present
b8 RFU 0 1 byte length of Info flag field
```

###### UID

```
Information fields, in the order of their corresponding flag, if their corresponding flag is asserted (= 1)
```

```
Tag memory size:
Legacy Android is disabled (3 byte value = 0700h || N, where value N indicates N+1 logical blocks of HF User memory)
Legacy Android is enabled (3 byte value = 0300h || N, where value N indicates N+1 logical blocks of HF User memory)
IC reference (1 byte value = IC Serial Number [47:40])
Tag command list (4 byte value = 06003FFFh)
CSI list (2 byte value = 0100h)
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
24 http://www.emmicroelectronic.com
```

#### 6.1.3. Quiet Storage Command

Quiet Storage is a custom command (command code = A2h or BCh) and is executed only in Addressed mode. If the Tag receives
a Quiet Storage, then the Tag enters the Quiet Storage state and does not send a response to the Interrogator. The Tag will only
process a request in the Quiet Storage state if the request has the Addressed_flag asserted (= 1) AND the Inventory_flag
deasserted (= 0), or if the request has the Inventory_flag asserted (= 1) AND the AFI_flag asserted (= 1).

The Tag exits the Quiet Storage state when any of the following occur:

 After the Quite Store Time expires during reset (power off)
 Reset to Ready with or without UID causes a transition to the Ready state
 Stay Quiet with UID causes a transition to the Quiet State
 Select with UID causes a transition to the Selected state

```
Quiet Storage Request Format
```

```
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID
(mandatory)
CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description A2h or BCh 16h HF UID CRC- 16
```

Request parameter:
IC Mfg Code
UID

#### 6.1.4. Read Signature Command

Read Signature is a custom command (command code = A5h or BDh) and allows for retrieving a digital signature from the Tag.
The Tag only executes a Read Signature when the request has the Option_flag deasserted (= 0), Digital Signature memory exists
(see User Configuration), HF Access to Digital Signature is enabled (see User Configuration), and only after a successful Login
using a Session Password or nonzero HF Login Password. If an error occurs, the Tag does not send a response to the Interrogator.

```
Read Signature Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description A5h or BDh 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID

```
Read Signature Response Format when Error_flag is NOT set
Tag =>
Interrogator
SOF Flags Digital Signature CRC- 16 EOF
```

```
# bits 8 256 or 384 or 512 16
description CRC- 16
```

Response parameter:
Digital Signature

#### 6.1.5. Write Signature Command

Write Signature is a custom command (command code = A6h) and allows for storing a digital signature on the Tag. The Tag only
executes a Write Signature when the Digital Signature memory exists (see User Configuration), HF Access to Digital Signature
is enabled (see User Configuration), and only after a successful Login using a Session Password or nonzero HF Login Password.

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the writing to Digital Signature memory.
If Option_flag is asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception returns
its response. The Interrogator must wait a minimum of TWRITE time per block before sending EOF in order to maintain a
proper energy condition for the Tag.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
25 http://www.emmicroelectronic.com
```

```
Write Signature Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID
First Block
Number
```

```
Number of
Blocks
Block Data CRC- 16 EOF
```

```
# bits 8 8 8 64 8 8 64 or 128 16
description A6h 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID
First Block Number (in Digital Signature memory)
Number of Blocks
Block Data

```
Write Signature Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Write Signature Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF^ Flags^ CRC-^16 EOF^
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

#### 6.1.6. Inventory Block Read Command

Inventory Block Read is a custom command (command code = B0h). When receiving an Inventory Block Read command, the
Tag performs the same as in the anti-collision sequence except that instead of UID number and DSFID, the Tag returns the
requested memory content.

The Tag will only process Inventory Block Read if the request has the Inventory_flag asserted (= 1). If the sum of First Block
Number and Number of Blocks exceeds the total available number of HF User blocks then the number of transmitted blocks is
less than the requested number of blocks, which means that the last returned block is the highest available HF User block followed
by the 16-bit CRC and the EOF.

NDEF Memory Substitution is disabled during processing of this command.

If an error is detected, the Tag remains silent.

If the Option_flag is asserted (= 1), the Tag returns the DSFID, the remaining part of the UID which is not part of the mask (and
slot number for 16 slot inventory) aligned to bytes, and n blocks of data. If Option_flag is deasserted (= 0), the Tag returns only n
blocks of data.

```
Inventory Block Read Request Format
```

Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code AFI Mask
Length

```
Mask
Value
```

```
First Block
Number
```

```
Number of
Blocks
CRC- 16 EOF
```

```
# bits 8 8 8 8 8 0 to 64 8 8 16
description B0h 16h HF AFI CRC- 16
```

Request parameter:
(Optional) AFI
Mask Length
Mask Value
First Block Number
Number of Blocks (value N indicates to read N+1 blocks)

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
26 http://www.emmicroelectronic.com
```

```
Inventory Block Read Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF Flags
```

```
DSFID UID or
Remainder
of UID
```

```
Block Data
CRC- 16 EOF
```

```
# bits 8 8 0 to 64 64 16
description CRC- 16
repeated as
needed
```

Response parameter:
DSFID (optional)
UID or Remainder of UID (optional)
Block Data

6.1.7. CHANGE PASSWORD COMMAND

Change Password is a custom command (command code = B4h) and is executed only in Addressed mode or Select mode, and
only after a successful Login. This command changes the current value for the HF Login Password if it is not locked.

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the password change. If Option_flag is
asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception returns its response. The
Interrogator must wait a minimum of TWRITE time before sending EOF in order to maintain a proper energy condition for the
Tag.

```
Change Password Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID Password
Value
Lock CRC- 16 EOF
```

```
# bits 8 8 8 64 32 8 16
description B4h 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID
Password Value
Lock (00h: Do not lock the password value after change, 01h: Lock the password value after change, others: RFU)

```
Change Password Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Change Password Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF^ Flags^ CRC-^16 EOF^
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

#### 6.1.8. Destroy Command

Destroy is a custom command (command code = B9h) and is executed only in Addressed mode or Select mode, and only after a
successful Login using a Session Password or nonzero HF Login Password. This command destroys/kills the Tag forever.

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the destroy/kill operation. If Option_flag
is asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception returns its response.
The Interrogator must wait a minimum of TWRITE time before sending EOF in order to maintain a proper energy condition for
the Tag.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
27 http://www.emmicroelectronic.com
```

```
Destroy Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description B9h 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID

```
Destroy Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Destroy Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF^ Flags^ CRC-^16 EOF^
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

#### 6.1.9. Enable Privacy Command

Enable Privacy is a custom command (command code = BAh) and is executed only in Addressed mode or Select mode, and only
after a successful Login using a Session Password or nonzero HF Login Password. This command forces the Tag to start a silent
mode of operation after every power-up and until an Interrogator can be authenticated by correctly providing the Session Password
or nonzero HF Login Password. The only commands processed during silent mode of operation are Login for all HF Crypto Modes
plus Authenticate for HF Crypto Mode = 2 and 3. NOTE: Use of web authentication disables the use of the silent mode of
operation.

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the enable of silent mode of operation.
If Option_flag is asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception returns
its response. The Interrogator must wait a minimum of TWRITE time before sending EOF in order to maintain a proper energy
condition for the Tag.

```
Enable Privacy Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description BAh 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID

```
Enable Privacy Response Format when Error_flag is set
Tag =>
Interrogator SOF^ Flags^ Error Code^ CRC-^16 EOF^
# bits 8 8 16
description CRC- 16
```

```
Enable Privacy Response Format when Error_flag is NOT set
Tag =>
Interrogator
SOF Flags CRC- 16 EOF
```

```
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
28 http://www.emmicroelectronic.com
```

#### 6.1.10. Disable Privacy Command

Disable Privacy is a custom command (command code = BBh) and is executed only in Addressed mode or Select mode, and only
after a successful Login using a Session Password or nonzero HF Login Password. This command forces the Tag to stop the
silent mode of operation.

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the disable of silent mode of operation.
If Option_flag is asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception returns
its response. The Interrogator must wait a minimum of TWRITE time before sending EOF in order to maintain a proper energy
condition for the Tag.

```
Disable Privacy Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description BBh 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID

```
Disable Privacy Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Disable Privacy Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF^ Flags^ CRC-^16 EOF^
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

#### 6.1.11. Enable Random ID Command

Enable Random ID is a custom command (command code = BEh) and is executed only in Addressed mode or Select mode, and
only after a successful Login using a Session Password or nonzero HF Login Password. This command forces the Tag to start
an untraceable mode of operation that generates a 32-bit random value after every power-up and creates a temporary HF UID.
The temporary HF UID uses the random value as the 32 lsb’s for the HF UID. This temporary HF UID supports untraceability of
the Tag as every power up the Tag will appear to have a different HF UID.

The temporary HF UID is used during the anti-collision scheme and in Addressed mode.

Get System Information and Extended Get System Information return the temporary HF UID, unless the commands occur after a
successful Login using a Session Password or nonzero HF Login Password, in which case the commands return the true HF UID.

Web authentication using OTP Crypto Signature Mode = 1 has precedence over the random ID feature and always returns the
true HF UID in the NDEF message because it is selected as the ID used for the crypto signature.

Starting the untraceable mode in HF will also start the corresponding functionality in UHF (i.e. UHF Untraceable Hide TID = 01 2 ).

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the enable of untraceable mode of
operation. If Option_flag is asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception
returns its response. The Interrogator must wait a minimum of TWRITE time before sending EOF in order to maintain a proper
energy condition for the Tag.

```
Enable Random ID Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID CRC- 16 EOF
```

```
# bits 8 8 8 64 16
description BEh 16h HF UID CRC- 16
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
29 http://www.emmicroelectronic.com
```

Request parameter:
(Optional) UID

```
Enable Random ID Response Format when Error_flag is set
Tag =>
Interrogator SOF^ Flags^ Error Code^ CRC-^16 EOF^
# bits 8 8 16
description CRC- 16
```

```
Enable Random ID Response Format when Error_flag is NOT set
Tag =>
Interrogator
SOF Flags CRC- 16 EOF
```

```
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

#### 6.1.12. Disable Random ID Command

Disable Random ID is a custom command (command code = BFh) and is executed only in Addressed mode or Select mode, and
only after a successful Login using a Session Password or nonzero HF Login Password. This command forces the Tag to stop an
untraceable mode of operation. Stopping the untraceable mode in HF will also stop the corresponding functionality in UHF
(i.e. UHF Untraceable Hide TID = 0 02 ).

If the Option_flag is deasserted (= 0), the Tag returns its response when it has completed the disable of untraceable mode of
operation. If Option_flag is asserted (= 1), the Tag waits for the reception of an EOF from the Interrogator and upon such reception
returns its response. The Interrogator must wait a minimum of TWRITE time before sending EOF in order to maintain a proper
energy condition for the Tag.

```
Disable Random ID Request Format
Interrogator
=> Tag SOF^ Flags^ Command Code^ IC Mfg Code^ UID^ CRC-^16 EOF^
# bits 8 8 8 64 16
description BFh 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID

```
Disable Random ID Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Disable Random ID Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF^ Flags^ CRC-^16 EOF^
# bits 8 16
description CRC- 16
```

Response parameter:
Error Code

#### 6.1.13. Fast Read Multiple Blocks Command

Fast Read Multiple Blocks is a custom command (command code = C3h) that is a double speed downlink data rate version of the
Read Multiple Blocks command. This allows communication speeds of 13 kbit/s or 53 kbit/s (2X faster than standard ISO/IEC
15693 - 3) depending on the selected Low / High data rate. The command response format and framing are the same as for the
Read Multiple Blocks command.

If the Option_flag is asserted (= 1), the Tag returns the block security status followed by the block data, sequentially block by
block. If Option_flag is deasserted (= 0), the Tag returns only the block data.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
30 http://www.emmicroelectronic.com
```

```
Fast Read Multiple Blocks Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID
First Block
Number
```

```
Number of
Blocks
CRC- 16 EOF
```

```
# bits 8 8 8 64 8 8 16
description C3h 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID
First Block Number
Number of Blocks (value N indicates to read N+1 blocks)

```
Fast Read Multiple Blocks Response Format when Error_flag is set
Tag =>
Interrogator
SOF Flags Error Code CRC- 16 EOF
```

```
# bits 8 8 16
description CRC- 16
```

```
Fast Read Multiple Blocks Response Format when Error_flag is NOT set
Tag =>
Interrogator SOF^ Flags^
```

```
Block Security
Status
```

```
Block Data
CRC- 16 EOF
```

```
# bits 8 8 64 16
description CRC- 16
repeated as needed
```

Response parameter:
Error Code
Block Security Status (optional)
Block Data

#### 6.1.14. Login Command

Login is a custom command (command code = C4h or E4h) and is used to authenticate an Interrogator and grant privileges to
use restricted commands or access protected (private) memory. An attempted Login with an incorrect password will result
in a security timeout (~75ms) which is applied to both the HF interface and the UHF interface.

```
Login Request Format
Interrogator
=> Tag
SOF Flags Command Code IC Mfg Code UID
Password
Value
CRC- 16 EOF
```

```
# bits 8 8 8 64 32 16
description C4h or E4h 16h HF UID CRC- 16
```

Request parameter:
(Optional) UID
Password Value

```
Login Response Format when Error_flag is set
Tag =>
Interrogator SOF^ Flags^ Error Code^ CRC-^16 EOF^
# bits 8 8 16
description CRC- 16
```

```
Login Response Format when Error_flag is NOT set
Tag =>
Interrogator
SOF Flags CRC- 16 EOF
```

```
# bits 8 16
description CRC- 16
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
31 http://www.emmicroelectronic.com
```

## 7. UHF Commands

```
Command Comment
```

```
QueryRep
ACK
Query
QueryAdjust
Select May not be used on Digital Signature memory or HF User memory.
```

NAK (^)
Req_RN
Read
Read operations on UHF User memory with WordCount = 0 will return data until the end of
a memory segment.
Write
Kill Failed Kill command sequence results in a security timeout (~75ms typical).
Lock
Locking or permalocking UHF User Memory Bank has no effect on locking Digital Signature
memory or HF User memory
Access Failed Access command sequence results in a security timeout (~75ms typical).
BlockWrite
Supports writing one to eight 16-bits words. All words must be within two memory blocks.
BlockWrite may be used for writing the Kill password and Access password but not both of
them simultaneously.
BlockPermalock User memory block size is four words.
Untraceable
(see below)
See Privacy Using Untraceable Command below. U indicator bit and EPC field are not
supported (ignored).
SQUARE_0
(see below)
Cannot be used until the System Configuration Block 1 is locked (Configuration Lock = 1).

###### SQUARE_N

```
(see below)
```

```
Cannot be used until the System Configuration Block 1 is locked (Configuration Lock = 1).
```

#### 7.1.1. Untraceable Command

```
The Untraceable command was introduced in Gen2V2 and may be used to hide some or all memory in the TID, EPC/UII, and/or
User memory banks. Untraceable may also be used to reduce the read range of the Tag. Untraceable may only be executed in
the Secured state and only by an Interrogator that has been granted the Untraceable privilege. See Privacy Using Untraceable
Command for further information. Tags reply to an Untraceable using a delayed Tag reply.
```

Interrogator
=> Tag

```
Command
Code
```

```
RFU U EPC TID User Range RN CRC
```

```
# bits 16 2 1 6 2 1 2 16 16
description E200h 002 not
supported
(don’t care)
```

```
msb:
0 = show memory
above L
1 = hide memory
above L
```

```
5 lsb’s (length):
L value must be
the same as the L
value in StoredPC
```

```
002 = hide none
012 = hide some
(memory above
20 h inclusive)
102 = hide all
112 = RFU
```

```
Hide/unhide TID
enables/disables
HF Random ID
```

```
0 = show
1 = hide
```

```
Hiding
User also
hides
Digital
Signature
memory
and HF
User
memory
```

```
002 = normal
012 = toggle
(no effect)
102 = reduced
(deactivated)
112 = RFU
```

```
handle CRC- 16
```

#### 7.1.2. SQUARE Commands

```
Using SQUARE commands is an easy way to suppress unwanted Gen2v2 Tags during inventory as they will not understand the
commands and will not respond to the Interrogator. SQUARE commands allow an Interrogator to rapidly singulate a Tag and
move it to Open state or Secured state depending on the Access password for the Tag. SQUARE commands may be used to
replace all select and inventory commands (Select, Query, QueryAdjust, QueryRep, ACK, NAK). Most Tag inventories can be
performed using a single SQUARE command per Tag. SQUARE commands may also be used to improve communication
reliability between Interrogators and Tags when operating in a multi-Interrogator environment using extended communication
dialogs. Lastly, SQUARE commands may be used to perform encoding of Tags in parallel.
```

```
SQUARE commands cannot be used until the System Configuration Block 1 is locked (Configuration Lock = 1). There are two
SQUARE commands, SQUARE_0 to initiate an inventory round (Slot = 0) and SQUARE_N to move to another slot within the
inventory round. SQUARE_N may also be used to NAK Tags or adjust Q in the same manner QueryAdjust.
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
32 http://www.emmicroelectronic.com
```

##### SQUARE_0 Command

Tags will execute the SQUARE_0 command in all states except Killed. A deactivated Tag will be temporarily reactivated (normal
operation) by any Interrogator using a SQUARE_0. Tags first process the Select field and transition to the Ready state, then
process the Query field and transition to the Arbitrate, Open, or Secured state as described below.

Upon receiving a SQUARE_0 with Q < 13 , Tags matching the criteria specified in the Query field will pick a random value in the
range (0, 2Q-1), inclusive, and load this value into their slot counter. Loading the slot counter establishes a binding handle
comprised of the 4 - bit Interrogator ID value concatenated with a 12 - bit value with the lsb's being MAX(1, Q) lsb’s of the slot
counter. If a Tag loads its slot counter with zero then it shall reply as directed by the Reply field and transition to the Open state
or Secured state, depending on the Access password for the Tag. Tags that load their slot counter with a non-zero value remain
silent and transition to the Arbitrate state and await a SQUARE_N.

Upon receiving a SQUARE_0 command with Q = 15, Tags matching the criteria specified in the Query field will load a zero value
into their slot counter. Loading the slot counter establishes a binding handle comprised of the 4 - bit Interrogator ID value
concatenated with ‘000h’. If a Tag loads its slot counter with zero then it shall reply as directed by the Reply field and transition to
the Open state or Secured state depending on the Access password for the Tag.

A SQUARE_0 may initiate an inventory round in a new session or in the prior session. If a Tag in the Acknowledged, Open, or
Secured states receives a SQUARE_0 whose session parameter matches the prior session it shall invert its inventoried flag (i.e.
A→B or B→A) for the session before it evaluates whether to transition to Ready, Arbitrate, Open, or Secured state. If a Tag in the
Acknowledged, Open, or Secured states receives a SQUARE_0 whose session parameter does not match the prior session it
shall leave its inventoried flag for the prior session unchanged. Note that tags with a binding handle will never use the Reply or
Acknowledged states and consequently a T 2 timeout can never occur.

A Tag that is bound via a binding handle will not execute QueryAdjust, QueryRep, ACK, or NAK commands, and will only process
access commands having the correct binding handle. The binding remains in effect until the Tag is released from the current
binding due to a transition to Ready state, a Query command, a SQUARE_N for a subsequent Tag, or a SQUARE_0 or
SQUARE_N which initiates a new inventory round.

```
Interrogator =>
Tag
```

```
Command
Code
```

```
Interrogator
ID
```

```
RFU Select Query
```

```
S1 Persistence Timing
(used only by SQUARE)
```

```
Reply CRC- 16
```

```
# of bits 8 4 1 16 15 1 2 16
```

```
Description CCh
```

###### (0 – 15)

```
ID for
Interrogator
that is
inventorying
the Tag
```

###### 0

```
see
below
```

```
see
below
```

```
0 = Use normal S1 flag
(S1 time is ~2 sec)
1 = Use extended S1 flag
(2 sec < S1 time < 18 sec)
Typical timing for Gen2v2
extended temperature range
```

```
see
below
```

```
NOTE: Select and Query commands always use the normal S1 persistence flag. The extended S1 persistence flag is shared with the HF
Quiet Store persistence flag and unpredictable results may occur if the UHF interface and HF interface use the flag simultaneously.
```

```
Select
```

```
Target Action Match Mask
```

```
3 3 2 8
```

```
Same as for
Select command
```

```
Same as for
Select command
```

```
002 : Ignore values for Target, Action, and Mask
012 : Tags match if EPC encoded and EPC header value = Mask value
102 : Tags match if ISO encoded and AFI value = Mask value
112 : Tags match if TID MDID (8 lsb’s) value = Mask value
```

```
Value to
match
```

```
Query
BLF M TRext Select Session Target Q
```

```
3 2 1 2 2 1 4
0002 : 640KHz
0012 : 320KHz
0102 : 256KHz
0112 : 160KHz
others: RFU
```

```
Same as
for Query
command
```

```
Same as
for Query
command
```

```
Same as
for Query
command
```

```
Same as
for Query
command
```

```
Same as
for Query
command
```

```
Same as for
Query command
Valid only when
Q is 15 or less
than 13
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
33 http://www.emmicroelectronic.com
```

```
Reply
```

```
Reply
```

```
2
Instructs all Tags in the inventory round how to reply to a SQUARE command :
002 : Send PacketPC (+ XPC_W1) (+ XPC_W2) + EPC/UII + binding handle + CRC- 16
012 : Not supported
102 : Send PacketPC (+ XPC_W1) (+ XPC_W2) + EPC/UII + UID + binding handle + CRC- 16
112 : Not supported
```

```
See binding handle below
UID = TID words 0 – 5 (any untraceably hidden data is read as 0000h)
```

```
Binding Handle
Interrogator ID RN Slot Number
# of bits 4 MIN(11,12-Q) MAX(1,Q)
```

```
Description
```

###### (0 – 15)

```
ID for Interrogator
that is inventorying
the Tag
```

###### (0 – MIN(2047,2^12 - Q-1))

```
Random value
```

###### (0 – 2 Q-1)

```
Slot counter
Random value
```

```
Tag =>
Interrogator
```

```
Reply
```

```
# of bits Variable
Description As directed by the SQUARE_0 command
```

SQUARE_0 initiates an inventory round and has an implied slot number = 0. The Interrogator increments the slot number and
uses SQUARE_N to advance to the next Tag in the inventory round. The Interrogator and the Tag use the binding handle for all
communication.

##### SQUARE_N Command

A Tag only executes SQUARE_N in Arbitrate, Open, or Secured state, and only if the 4-bit Interrogator ID value matches the 4
msb’s of the Tag’s binding handle and the Tag is not in the middle of a Kill or Access command sequence.

If the Tag is in Arbitrate state and Function = 010 2 or 111 2 , then SQUARE_N is ignored. If the Tag is in Open or Secured state
and either Function = 010 2 or 011 2 and the binding handle slot number does not match the Tag’s binding handle slot number, or
Function = 111 2 and the binding handle does not match the Tag’s binding handle, then SQUARE_N is ignored.

If the Tag is in Arbitrate state and either Function = 0 002 , 001 2 , or 011 2 , then the Tag shall decrement its slot counter and if zero
shall reply as directed by the Reply field in the prior SQUARE_0 and transition to the Open state or Secured state depending on
the Access password for the Tag.

If the Tag is in Open or Secured state and either Function = 000 and the binding handle slot number matches the Tag’s binding
handle slot number, or Function = 001 2 and the binding handle matches the Tag’s binding handle, then the Tag inverts its
inventoried flag (i.e. AB or BA), releases the binding for the Tag, and transitions to Ready state. If the Tag is in Open or
Secured state and either Function = 000 2 and the binding handle slot number does not match the Tag’s binding handle slot
number, or Function = 001 2 and the binding handle does not match the Tag’s binding handle, then the Tag shall transition to
Arbitrate state and no change to the inventoried flag occurs. If the Tag is in Open or Secured state and Function = 111 2 and the
binding handle matches the Tag’s binding handle, then the Tag shall reply as directed by the Reply field in the prior SQUARE_0
and no change to the inventoried flag occurs and no state change occurs.

If the Tag is in Open or Secured state and either Function = 010 2 or 011 2 and the binding handle slot number matches the Tag’s
binding handle slot number, then the Tag shall transition to Arbitrate state and no change to the inventoried flag occurs.

If Function = 100 2 or 101 2 or 110 2 , then the Tag will first adjust Q (leave unchanged, increment, or decrement within the range 0

- 12, inclusive), then pick a random value in the range (0, 2Q–1), inclusive, and load this value into the slot counter. Loading the
  slot counter establishes a binding handle comprised of the 4-bit Interrogator ID value concatenated with a 12 - bit value with the
  lsb's being MAX(1, Q) lsb’s of the slot counter. If a Tag loads its slot counter with zero then it shall reply as directed by the Reply
  field of the previous SQUARE_0 and transition to the Open state or Secured state, depending on the Access password for the

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
34 http://www.emmicroelectronic.com
```

Tag. Tags that load their slot counter with a non-zero value transition to the Arbitrate state where they remain silent and await a
SQUARE_N.

```
Interrogator =>
Tag
```

```
Command
Code
```

```
Function
```

```
Binding Handle
```

```
Interrogator CRC-^16
ID
```

```
Slot Number
or
RN || Slot Number
# of bits 8 3 4 12 16
```

```
Description CBh
```

```
0002 = Tag with matching binding handle
slot number inverts its inventoried flag.
Other Tags decrement slot counter and
Tag with slot counter = 0 replies
```

```
(ACK all tags in slot and move to next slot)
```

###### (0 – 15)

```
ID for
Interrogator
that is
inventorying
the Tag
```

```
Binding Handle
Slot Number
padded with
leading 0’s
```

```
0012 = Tag with matching binding handle
inverts its inventoried flag. Other Tags
decrement slot counter and Tag with slot
counter = 0 replies
```

```
(ACK one tag in slot and move to next slot)
```

```
Binding Handle RN
|| Binding Handle
Slot Number
```

```
0102 = NAK Tag with matching binding
handle slot number
```

```
(NAK all tags in slot)
```

```
Binding Handle
Slot Number
padded with
leading 0’s
```

```
0112 = NAK Tag with matching binding
handle slot number. Other Tags
decrement slot counter and Tag with slot
counter = 0 replies
```

```
(NAK all tags in slot and move to next slot)
```

```
Binding Handle
Slot Number
padded with
leading 0’s
```

```
1002 = Adjust Q with Q unchanged
1012 = Adjust Q with Q increment
1102 = Adjust Q with Q decrement
```

```
(Adjust Q)
```

```
Any value
```

```
1112 = ACK Tag with matching binding
handle and it replies
```

```
(monitor one tag in slot)
```

```
Binding Handle RN
|| Binding Handle
Slot Number
```

```
Tag =>
Interrogator
```

```
Reply
```

```
# of bits Variable
Description As directed by the prior SQUARE_0 command
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
35 http://www.emmicroelectronic.com
```

##### SQUARE Examples

```
SQUARE Examples
Gen2V2 Command Sequence SQUARE Command Sequence
Simple Tag Inventory Simple Tag Inventory
```

Reader => Tags : SELECT

Reader => Tags : QUERY

tag(slot=0) => Reader : RN16

Reader => Tags : ACK(RN16)

tag(slot=0) => Reader : PacketPC+(XPC_W1)+UII+PacketCRC

Reader => Tags : QUERYREP

tag(slot=1) => Reader : RN16

Reader => Tags : ACK(RN16)

tag(slot=1) => Reader : PacketPC+UII+PacketCRC

```
Reader => Tags : SQUARE_0(Reply=0)
tag(slot=0) => Reader : PacketPC+(XPC_W1)+UII+BindingHandle+PacketCRC
Reader => Tags : SQUARE_N(Slot= 0 )
tag(slot=1) => Reader : PacketPC+(XPC_W1)+UII+BindingHandle+PacketCRC
```

```
Tag Inventory + Read TID Tag Inventory + Read TID
```

Reader => Tags : SELECT

Reader => Tags : QUERY

tag(slot=0) => Reader : RN16

Reader => Tags : ACK(RN16)

tag(slot=0) => Reader : PacketPC+(XPC_W1)+UII+PacketCRC

Reader => Tags : REQ_RN

tag(slot=0) => Reader : Handle+PacketCRC

Reader => Tags : READ(Handle, TID words 0-5)

tag(slot=0) => Reader : 0+UID+Handle+PacketCRC

Reader => Tags : QUERYREP

tag(slot=1) => Reader : RN16

```
Reader => Tags : SQUARE_0(Reply=2)
tag(slot=0) => Reader :
PacketPC+(XPC_W1)+UII+UID+BindingHandle+PacketCRC
Reader => Tags : SQUARE_N(Slot= 0 )
tag(slot=1) => Reader :
PacketPC+(XPC_W1)+UII+UID+BindingHandle+PacketCRC
```

Parallel Tag Encoding

Reader => Tags : SQUARE_0(Q=15)

```
Tags => Reader : all tags respond simultaneously
PacketPC+(XPC_W1)+UII+BindingHandle+PacketCRC
Reader => Tags : BLOCKWRITE(BindingHandle, MemBank, WordPtr,
WordCount, Data)
Tags => Reader : all tags respond simultaneously
0+BindingHandle+PacketCRC
```

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
36 http://www.emmicroelectronic.com
```

## 8. Privacy Features

Support for privacy is provided using the UHF Untraceable command, and/or the HF Write Single Block command, and/or the HF
Enable Privacy command.

### 8.1. Privacy Using Untraceable Command

The Untraceable command may only be used by an Interrogator that asserts the Untraceable privilege. The Untraceable privilege
can be associated with the Access password and the selection is user programmable. Refer to User Configuration for further
information. An Interrogator must enter the Secured state using a non-zero Access password to use Untraceable.

Untraceable allows an Interrogator to instruct the em|echo-V to (a) hide memory from Interrogators with a deasserted Untraceable
privilege, and/or (b) reduce its operating range for all Interrogators. The memory that a Tag may hide includes the Tag serialization
in TID memory, all of TID memory, and/or User memory. Untraceable and traceable Tags behave identically from a state-machine
and command-response perspective; the difference between them is (a) the memory the Tag exposes to an Interrogator with a
deasserted Untraceable privilege and/or (b) the Tag’s operating range.

Untraceable may be used to change the operational read range of a Tag. em|echo-V supports this feature in a manner that permits
having either full read range (normal operation) or no read range (deactivated operation). A deactivated Tag always remains in
the Ready state and will not participate in any inventory operations.

The Range parameter in the Untraceable command is used to specify the persistent operational read range of the Tag. If Range
= 00 2 then the Tag persistently enables normal operation. If Range = 10 2 then the Tag persistently enables deactivation and the
Tag becomes deactivated immediately upon reply to the Untraceable command. If Range = 01 2 then it has no effect on the Tag.

A deactivated Tag may be temporarily reactivated (normal operation) by any Interrogator using a Select command with any of the
assigned EM Microelectronic Mask Designer ID’s (MDID’s) provided that the MDID is not untraceably hidden. The Select
command parameters are MemBank = 10 2 , Pointer = 08h, and either Length = 0Ch with matching Mask = 00Bh or = 40Bh or =
80Bh or = C0Bh, or Length = 1 0h with matching Mask = 00BXh or = 40BXh or = 80BXh or = C0BXh where X can be any
hexadecimal value. A deactivated Tag may also be temporarily reactivated by any Interrogator using SQUARE_0 to initiate an
inventory round. If a deactivated Tag has the MDID untraceably hidden then it may only be temporarily reactivated using
SQUARE_0 to initiate an inventory round. Refer to SQUARE Commands for further information. Whenever a Tag is temporarily
reactivated, it remains in the normal operational mode until the Tag loses power or executes another Untraceable with Range = 102.

## 9. Using the Tag Notification (TN) indicator

Tag Notification (TN) is an IC vendor defined indicator bit that is part of the XPC_W1 word and is reported to an Interrogator as
part of the Tag reply to an ACK command. em|echo-V provides the TN indicator bit and it may be used for an application defined
purpose. If used, the meaning of TN = 0 or TN = 1 is defined by the application. TN may only be modified by an authenticated
Interrogator that asserts the TN privilege. The TN indicator bit may be modified regardless of the lock/permalock status of the
EPC memory bank.

## 10. Security Services Using ISO/IEC 29167-10 (AES-128)

ISO/IEC 1 5693 provides cryptographic security for the air interface protocol. Commands are defined for Interrogators and Tags
for authentication using cryptographic means over the air interface. The commands are used in conjunction with ISO/IEC
standardized Cryptographic Suites. Every standardized Cryptographic Suite (CS) defines the security services that it provides and
how to use the air interface protocol commands for each security service.

em|echo-V supports the CS defined by ISO/IEC 29167 - 10 which is based on the AES symmetric block cipher. AES-128 uses 128-
bit keys and encrypts 128-bit blocks. The CS provides security services for Tag authentication with the option for authenticated
reading of Tag memory during Tag authentication. The Crypto Suite Identifier (CSI) for ISO/IEC 29167-10 is assigned CSI = 00h.

This device includes a true Random Number Generator (RNG) which passes NIST SP 800-22, “A Statistical Test Suite for Random
and Pseudorandom Number Generators for Cryptographic Applications”.

### 10.1. Commands

The following HF command is used to support this CS:

```
 Authenticate
```

The above Interrogator command and subsequent Tag replies contain a parameter field for an encapsulated CS message and/or
response between the Interrogator (or host) crypto engine and the Tag crypto engine. The Tag crypto engine only implements
encryption and uses it both for “decrypting” CS messages (commands) from the Interrogator to the Tag and “encrypting” CS
responses (replies) sent from the Tag to the Interrogator.

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
37 http://www.emmicroelectronic.com
```

### 10.2. Tag Authentication Message 1 (TAM1)

```
Tag authentication is defined in ISO/IEC 29167-10 as AuthMethod[1:0] = 002. The TAM1 message is encapsulated in an
Authenticate. The KeyID value indicates which crypto key the Tag shall use to perform the Tag authentication.
```

```
Interrogator TAM1 Crypto Command
Interrogator => Tag AuthMethod CustomData RFU KeyID IChallenge
# bits 2 1 5 8 80
description 002 0 000002 Crypto key to be used for TAM1 Random Interrogator challenge
```

```
Tag TAM1 Crypto Reply
Tag => Interrogator TResponse
# bits 128
description AES-ENC(Key[KeyID], (96C5h || TRnd[31:0] || IChallenge[79:0]))
```

### 10.3. Tag Authentication Message 2 (TAM2)

```
TAM2 is optimized to support a single memory read operation as part of the authentication sequence. The TAM2 message is
encapsulated in an Authenticate. The KeyID value indicates which crypto key the Tag shall use to perform the Tag authentication.
The first publication of ISO/IEC 29167-10 defined TAM2 in a manner that it is susceptible to security attacks such that the integrity
of the data read operation cannot be ensured. The subsequent revision to ISO/IEC 29167-10 corrected the TAM2 issues. The two
implementations of TAM2 are referred to as legacy (REV0) TAM2 and REV1 TAM2. The TAM2 message from the Interrogator to
the Tag selects which TAM2 format shall be used by the Tag for the response to the Interrogator.
```

```
The following restrictions apply to TAM2:
```

```
 Only REV1 TAM2 is supported.
 Only BlockSize = 0 (64-bit blocks) is supported.
 Only Profile = 0000 2 (HF UID), 00012 (HF User Memory Bank), 00102 (UHF EPC/UII Memory Bank), 00 112 (UHF TID
Memory Bank), 0 1002 (UHF User Memory Bank, a.k.a. File_0), and 01012 (Digital Signature Memory) are supported
 Only ProtMode = 0000 2 (plaintext) and 0001 2 (encryption) are supported.
 Only BlockCount = 0000 2 (1 64-bit block) is supported.
 Any private (protected) memory can be read that is within 0 1012 only when ProtMode = 00 012 and the key has the
Protected Read privilege.
 Any untraceably hidden memory can be read that is within Profile = 00 102 or 00 112 or 0 1002 but only when ProtMode =
00012 and the key has the UHF Untraceable privilege.
 NDEF Memory Substitution is disabled for Profile = 0001 2 (HF User Memory Bank)
```

Interrogator TAM2 Crypto Command
Interrogator
=> Tag

```
Auth
Method
```

```
Custom
Data
```

```
Block
Size
```

```
REV RFU KeyID IChallenge Profile Offset Block
Count
```

```
ProtMode
```

```
# bits 2 1 1 1 3 8 80 4 12 4 4
description 002 1 0 0 = REV0
1 = REV1
```

```
0002 Crypto
key to be
used for
TAM2
```

```
Random
Interrogator
challenge
```

```
Memory
profile to
read
(see
above)
```

```
Number of
multiple
64 - bit
blocks for
starting
word
offset for
data read
```

```
Number
of 64-bit
blocks
to read
```

- 1
  (see
  above)

```
Protection
mode for
data read
(see
above)
```

```
Tag TAM2 REV1 Crypto Reply for ProtMode = 0000 2 (plaintext)
Tag => Interrogator TResponse
# bits 256
```

description AES-ENC(Key[KeyID], (96C 0 h || TRnd[31:0] || IChallenge[79:0])) || ( 02 || Profile[3:0] || Offset[11:0] || (^0002)
|| 000 00000000h || Data[63:0])
Tag TAM2 REV1 Crypto Reply for ProtMode = 0001 2 (encryption)
Tag => Interrogator TResponse

# bits 256

description (^) AES-ENC(Key[KeyID], (96C1h || TRnd[31:0] || IChallenge[79:0])) ||
CBCENC_AES (IV = AES-ENC(Key[KeyID], (96C1h || TRnd[31:0] || IChallenge[79:0])), Key[KeyID], ( 02 || Profile[3:0]
|| Offset[11:0] || 0002 || 000 00000000h || Data[63:0]))

# Identiv

DATASHEET

```
Copyright  2021 , EM Microelectronic-Marin SA
38 http://www.emmicroelectronic.com
```

### 10.4. Dynamic Session Password Using TAM1 or TAM2

A dynamic Session Password is generated during AES-128 Tag authentication and is used for the HF Login Password value. The
dynamic Session Password is provided by the Tag to the Interrogator in ciphertext as the TRnd value in the Tag reply to TAM1 or
TAM2. The Session Password remains valid until an unsuccessful Login occurs or the Tag is no longer powered or when another
dynamic Session Password is generated. A dynamic Session Password from TAM1 or TAM2 can be used only for HF Crypto
Mode = 2 or 3, and only if the crypto key has the Session Password privilege. Note that the use of a Session Password is an
indirect crypto authentication of the NFC reader/writer or HF Interrogator.

## 11. User Configuration

em|echo-V provides a variety of options for users to configure the device for their applications. The device is in open transport
mode and configuration of the device is not protected when Access Password and HF Login password are both zero (default
delivery state from EM). The device is in private transport mode and configuration of the device is protected when either the
Access Password or HF Login password is non-zero, as a means to protect the devices during transport and prior to Inlay
Configuration. When configured for private transport mode, the Access sequence (UHF interface) or Login (HF interface) with
valid password is required first in order to write the configuration during Inlay Configuration.

UHF EPC/UII memory and UHF TID memory can be accessed but User configuration is required before the device will permit
access to HF User memory, UHF User memory, Digital Signature memory, or the use of any privacy features or security services.
The HF Crypto Mode = 0 during device configuration and until such time that a different HF Crypto Mode is activated (Activate HF
Crypto Mode = 1). Configuration of the device is not protected when the HF Login Password and the UHF Access Password are
both zeros, or once System Configuration Block 1 is locked (Configuration Lock = 1).

NOTE: Refer to Versions for additional information regarding version specific limitations for configuration options.

### 11.1. Configuration via the HF Interface

Configuration is performed using the System Configuration Blocks in system memory and the Crypto Key Blocks in UHF Reserved
memory. Configuration must be done in the following sequence:

1. Use Write Single Block to write System Configuration Block 1 and include setting the Configuration Lock (= 1). Once
   locked, then
   a. The HF User memory, HF AFI, HF DSFID, UHF EPC/UII memory, UHF User memory, and Digital Signature
   memory are now defined and can be accessed for read/write/lock operations.
   b. The HF Crypto Mode is now defined but not yet activated. The Crypto Key Blocks in UHF Reserved memory
   can now be accessed for read/write operations.
   c. System Configuration Block 2 can now be accessed for read/write/lock operations.
   d. System Configuration Block 1 will subsequently be read as all zeros and the only subsequent write operation
   allowed to System Configuration Block 1 is to Activate HF Crypto Mode (= 1).
2. Use Write Single Block or Write Multiple Blocks to write the Crypto Key Blocks 1 – 6 based on the HF Crypto Mode.
3. Use Write Single Block or Write Multiple Blocks to write the Crypto Key Block 7 (OTP NDEF Configuration) when HF
   Crypto Mode = 1 or 2. Use Write Single Block to write System Configuration Block 2 to set the OTP NDEF Configuration
   Lock (= 1).
4. Use Write Single Block to write System Configuration Block 2 to set Tamper Lock and enable Tamper Alarm logging:
   a. If Pads/Pins are configured for Tamper Loop then setting the Tamper Lock (= 1) enables writing of Tamper
   Alarm in NVM. Once locked, then if a Tamper Alarm occurs which can be written to NVM, the Tamper Alarm
   becomes unalterable.
5. Use Write Single Block to write System Configuration Block 1 and set Activate HF Crypto Mode (= 1).
   a. A crypto key cannot be used until the HF Crypto Mode is activated.

Steps 3, 4 and 5 can be done in any order. The System Configuration Blocks and Crypto Key Blocks are described on following
pages.

```

```
