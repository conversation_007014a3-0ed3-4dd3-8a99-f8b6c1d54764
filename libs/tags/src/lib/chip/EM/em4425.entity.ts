import { Directive, Field, HideField, InputType, ObjectType, OmitType, registerEnumType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { Exclude } from 'class-transformer'
import { IsEnum, IsN<PERSON>berString, IsOptional, IsString } from 'class-validator'

import type { Chip } from '../chip.entity'

// Example URL received from the EM4425V12 chip
// https://daptapgo.io/tap?uid=UlDl&token=[TOKEN]&f=[FLAGS]&sig=[SIGNATURE]&epc=[EPCCODE]'
// https://daptapgo.io/tap?uid=4BZ4ARoUbwM&token=AABt&f=w&sig=RANWLzDcNkds2OfSLmbIKA

export enum EM4425ChipType {
  EM4425V12 = 'EM4425V12',
  EM4425V13 = 'EM4425V13',
  EM4425V16 = 'EM4425V16',
}
registerEnumType(EM4425ChipType, {
  name: 'EM4425ChipType',
  description: 'The EM4425 chip type',
})

export enum EM4425Mode {
  CryptoMode0 = 'CryptoMode0', // No encryption
  CryptoMode1 = 'CryptoMode1', // Uses CryptoKey0
  CryptoMode2 = 'CryptoMode2', // Uses CryptoKey1
}
registerEnumType(EM4425Mode, {
  name: 'EM4425Mode',
  description: 'The EM4425 chip mode',
})

export interface CreateChipEM4425CSVRow {
  serialNumber: string
  type: EM4425ChipType
  mode: EM4425Mode
  accessPassword?: string
  killPassword?: string
  cryptoKeyId0?: string
  cryptoKeyId1?: string
  cryptoKeyId2?: string
}

@InputType()
export class EM4425ValidationArguments {
  @Field(() => String)
  @ApiProperty({ type: String })
  @IsString()
  token: string

  @Field(() => String)
  @ApiProperty({ type: String })
  @IsString()
  flags: string

  @Field(() => String)
  @ApiProperty({ type: String })
  @IsString()
  signature: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  epc?: string

  constructor(data: Partial<EM4425ValidationArguments>) {
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class EM4425 implements Chip {
  @Field(() => String, { description: 'The serial number of the chip.' })
  @ApiProperty({ type: String, description: 'The serial number of the chip.' })
  @IsString()
  serialNumber: string

  @Field(() => EM4425ChipType, { description: 'The type of the chip.' })
  @ApiProperty({ enum: EM4425ChipType, description: 'The type of the chip.' })
  @IsEnum(EM4425ChipType)
  type: EM4425ChipType

  @Field(() => String, { description: 'The UID of the chip.' })
  @ApiProperty({ type: String, description: 'The UID of the chip.' })
  @IsString()
  uid: string

  @Field(() => String, { nullable: true, description: 'The TID of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The TID of the chip.' })
  @IsString()
  @IsOptional()
  tid?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsString()
  @IsOptional()
  accessPassword?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsString()
  @IsOptional()
  killPassword?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsString()
  @IsOptional()
  cryptoKeyId0?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsString()
  @IsOptional()
  cryptoKeyId1?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsString()
  @IsOptional()
  cryptoKeyId2?: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  @IsNumberString()
  @IsOptional()
  counter?: number
}

@InputType()
export class CreateEM4425Chip {
  @Field(() => String, { description: 'The serial number of the chip.' })
  @ApiProperty({ type: String, description: 'The serial number of the chip.' })
  serialNumber: string

  @Field(() => EM4425ChipType, { description: 'The type of the chip.' })
  @ApiProperty({ enum: EM4425ChipType, description: 'The type of the chip.' })
  type: EM4425ChipType

  @Field(() => String, { nullable: true, description: 'The access password of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The access password of the chip.' })
  accessPassword?: string

  @Field(() => String, { nullable: true, description: 'The kill password of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The kill password of the chip.' })
  killPassword?: string

  @Field(() => String, { nullable: true, description: 'The crypto key ID 0 of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The crypto key ID 0 of the chip.' })
  cryptoKeyId0?: string

  @Field(() => String, { nullable: true, description: 'The crypto key ID 1 of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The crypto key ID 1 of the chip.' })
  cryptoKeyId1?: string

  @Field(() => String, { nullable: true, description: 'The crypto key ID 2 of the chip.' })
  @ApiProperty({ type: String, required: false, description: 'The crypto key ID 2 of the chip.' })
  cryptoKeyId2?: string
}

@ObjectType()
@Directive('@key(fields: "id")')
export class EM4425V12 extends OmitType(EM4425, ['cryptoKeyId2'], ObjectType) {
  @Field(() => String, { description: 'The TID of the chip.' })
  @ApiProperty({ type: String, description: 'The TID of the chip.' })
  tid: string

  constructor(data: Partial<EM4425V12>) {
    super()
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class EM4425V13 extends EM4425 {
  @Field(() => String, { description: 'The TID of the chip.' })
  @ApiProperty({ type: String, description: 'The TID of the chip.' })
  tid: string

  @HideField()
  @Exclude({ toPlainOnly: true })
  cryptoKeyId2?: string

  constructor(data: Partial<EM4425V13>) {
    super()
    Object.assign(this, data)
  }
}

@ObjectType()
@Directive('@key(fields: "id")')
export class EM4425V16 extends OmitType(EM4425, ['tid', 'cryptoKeyId2'], ObjectType) {
  constructor(data: Partial<EM4425V16>) {
    super()
    Object.assign(this, data)
  }
}
