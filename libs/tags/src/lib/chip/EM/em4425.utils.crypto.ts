import { createCipheriv, createDecipheriv } from 'crypto'

import { EM4425Mode } from './em4425.entity'

export interface Flags {
  idLength: number
  tamperAlarm: boolean
  uhfKillFlag: boolean
  uhfRangeReductionFlag: boolean
  uhfTnFlag: boolean
  rfu: boolean
}

export type ParsedDataBlock =
  | {
      tid: string
      flags: Flags
      token: string
      counter: number
      uid?: string
      sessionPassword?: number
    }
  | {
      uid: string
      flags: Flags
      token: string
      counter: number
      tid?: string
      sessionPassword?: number
    }

function parseToken(token: string): number {
  return Buffer.from(token, 'base64url').readUIntBE(0, 3)
}

const base64UrlChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'

function decodeFlags(flagChar: string) {
  const index = base64UrlChars.indexOf(flagChar)
  const flags = index & 0x3f

  return {
    idLength: (flags >> 5) & 0x01,
    tamperAlarm: Boolean((flags >> 4) & 0x01),
    uhfKillFlag: Boolean((flags >> 3) & 0x01),
    uhfRangeReductionFlag: Boolean((flags >> 2) & 0x01),
    uhfTnFlag: Boolean((flags >> 1) & 0x01),
    rfu: Boolean(flags & 0x01),
  }
}

function decrypt(encryptedData: string, key: string, iv: Buffer = Buffer.alloc(16, 0)) {
  const keyBuffer = Buffer.from(key, 'hex')
  const encryptedBuffer = Buffer.from(encryptedData, 'base64url')

  const decipher = createDecipheriv('aes-128-cbc', keyBuffer, iv)
  decipher.setAutoPadding(false) // Disable auto padding to maintain consistent block length

  return Buffer.concat([decipher.update(encryptedBuffer), decipher.final()])
}

export function signatureToDataBlock(signature: string, key?: string): ParsedDataBlock {
  let dataBlock: Buffer

  if (key) {
    // Encrypted mode: Decrypt the signature using the key
    const decrypted = decrypt(signature, key)

    if (decrypted.length < 12) {
      throw new Error('Decrypted data is too short.')
    }

    dataBlock = decrypted
  } else {
    // CryptoMode0 (No Encryption): The signature is the data block in plaintext
    const dataBlockBuffer = Buffer.from(signature, 'base64url')

    if (dataBlockBuffer.length < 12) {
      throw new Error('Data block is too short.')
    }

    dataBlock = dataBlockBuffer
  }

  // Now proceed to parse the data block as before
  const flagsValue = dataBlock[0] & 0x3f
  const flags = decodeFlags(base64UrlChars[flagsValue])

  const token = dataBlock.subarray(1, 4).toString('base64url')

  const counter = parseToken(token)

  if (flags.idLength === 0) {
    const tidBuffer = dataBlock.subarray(4, dataBlock.length)
    const tid = tidBuffer.toString('base64url')

    return {
      tid,
      flags,
      token,
      counter,
    }
  } else {
    const uidBuffer = dataBlock.subarray(4, 12)
    const uid = uidBuffer.toString('base64url')

    const sessionPasswordBuffer = dataBlock.subarray(12, dataBlock.length)
    const sessionPassword = sessionPasswordBuffer.equals(Buffer.alloc(4))
      ? undefined
      : sessionPasswordBuffer.readUInt32BE(0)

    return {
      uid,
      flags,
      token,
      counter,
      sessionPassword,
    }
  }
}

function encrypt(data: Buffer, key: string, iv: Buffer = Buffer.alloc(16, 0)) {
  const keyBuffer = Buffer.from(key, 'hex')
  const cipher = createCipheriv('aes-128-cbc', keyBuffer, iv)
  cipher.setAutoPadding(false) // Disable auto padding to maintain consistent b

  return Buffer.concat([cipher.update(data), cipher.final()])
}

function generateDataBlock(flag: string, token: string, id: string, sessionPassword?: number): Buffer {
  const flags = decodeFlags(flag)

  // Determine the data block length based on ID length flag
  const dataBlock = Buffer.alloc(flags.idLength === 0 ? 12 : 16)

  // Encode the flags and store them in the first byte of the data block
  dataBlock[0] = base64UrlChars.indexOf(flag) | 0x40 // Store flags in the first byte (use the full 8 bits)

  // Decode the security token (expected to be 3 bytes)
  const tokenBuffer = Buffer.from(token, 'base64url')
  if (tokenBuffer.length !== 3) {
    throw new Error('Invalid token length. Expected 3 bytes.')
  }
  tokenBuffer.copy(dataBlock, 1) // Copy all 3 bytes to dataBlock starting at index 1

  if (flags.idLength === 0) {
    // ID Length 0 indicates TID (expected to be 12 bytes)
    const idBuffer = Buffer.from(id, 'base64url')
    if (idBuffer.length !== 12) {
      throw new Error('Invalid TID length. Expected 12 bytes.')
    }
    idBuffer.copy(dataBlock, 4) // Copy all 12 bytes starting at index 4
  } else {
    // ID Length 1 indicates UID (expected to be 8 bytes)
    const idBuffer = Buffer.from(id, 'base64url')
    if (idBuffer.length !== 8) {
      throw new Error('Invalid UID length. Expected 8 bytes.')
    }
    idBuffer.copy(dataBlock, 4) // Copy all 8 bytes starting at index 4

    // If session password is provided, write it to the data block, otherwise fill with zeros
    if (sessionPassword != null) {
      dataBlock.writeUInt32BE(sessionPassword, 12)
    } else {
      dataBlock.fill(0, 12, 16) // Fill with zeros from index 12 to 16
    }
  }

  return dataBlock
}

export function generateOtpSignature(
  flags: string,
  token: string,
  id: string,
  cryptoMode: EM4425Mode,
  key?: string,
  sessionPassword?: number,
): string {
  const dataBlock = generateDataBlock(flags, token, id, sessionPassword)

  let signature: string

  if (cryptoMode === EM4425Mode.CryptoMode0) {
    // In CryptoMode0 (No Encryption), do not encrypt the data block
    signature = dataBlock.toString('base64url')
  } else {
    // In encrypted modes, encrypt the data block using the provided key
    if (!key) {
      throw new Error('Crypto key is required for encrypted modes.')
    }
    signature = encrypt(dataBlock, key).toString('base64url')
  }

  return signature
}
