import { randomBytes } from 'crypto'

import { EM4425ChipType, EM4425Mode } from './em4425.entity'
import { generateOtpSignature } from './em4425.utils.crypto'
import { generateEPC } from '../epc'
import { generateSecurePassword } from '../utils'

import type { EM4425 } from './em4425.entity'

export function generateUid(icSerialNumber: string) {
  // Remove colons and convert to uppercase
  const uid = icSerialNumber.replace(/[:\s]/g, '').toUpperCase()

  // Validate that the UID is 16 hex characters (8 bytes)
  if (uid.length !== 16 || !/^[0-9A-F]{16}$/.test(uid)) {
    throw new Error('Invalid IC serial number. Must be 8 bytes (16 hex characters).')
  }

  return Buffer.from(uid, 'hex').toString('base64url')
}

export function generateTid(icSerialNumber: string) {
  const manufacturerCode = 'E2' // TID manufacturer code for EM Microelectronic

  const tagModelNumber = '80B1' // Tag model number for EM4425 (2 bytes, 4 hex chars)

  const serialNumber = icSerialNumber.replace(/[:\s]/g, '').toUpperCase()

  const tid = manufacturerCode + tagModelNumber + serialNumber

  if (tid.length !== 22 || !/^[0-9A-F]{22}$/.test(tid)) {
    throw new Error('Generated TID must be 11 bytes (22 hex characters).')
  }

  return Buffer.from(tid, 'hex').toString('base64url')
}

function generateToken(counter = 0) {
  const token = (counter + Math.floor(Math.random() * 8) + 1) & 0xffffff
  const tokenBuffer = Buffer.alloc(3)
  tokenBuffer.writeUIntBE(token, 0, 3)
  const tokenBase64 = tokenBuffer.toString('base64url')

  if (tokenBase64.length !== 4) {
    throw new Error('Generated token is not 4 characters long')
  }
  return tokenBase64
}

export function generateEM4425Url(
  chip: EM4425,
  cryptoMode: EM4425Mode = EM4425Mode.CryptoMode1,
  sessionPassword?: number,
) {
  const counter = chip.counter ?? 0

  const token = generateToken(counter)
  const flags = 'g' // Adjust this based on your flag requirements
  const { epc } = generateEPC()

  // Determine the appropriate crypto key based on the cryptoMode
  let cryptoKey: string | undefined

  switch (cryptoMode) {
    case EM4425Mode.CryptoMode0:
      cryptoKey = undefined // No encryption
      break
    case EM4425Mode.CryptoMode1:
      cryptoKey = chip.cryptoKeyId0
      break
    case EM4425Mode.CryptoMode2:
      cryptoKey = chip.cryptoKeyId1
      break
    default:
      throw new Error('Unsupported Crypto Mode')
  }

  // Generate the signature using the appropriate crypto key
  const signature = generateOtpSignature(flags, token, chip.uid, cryptoMode, cryptoKey, sessionPassword)

  const url = new URL('https://daptapgo.io/tap')
  url.searchParams.append('uid', chip.uid)
  url.searchParams.append('token', token)
  url.searchParams.append('f', flags)
  url.searchParams.append('sig', signature)
  url.searchParams.append('EPC', epc)

  return url.toString()
}

/**
 * Generates a random IC serial number compliant with the EM4425 datasheet.
 * @param {string} [manufacturerCode] - Optional manufacturer code ('E0' or 'E2'). Defaults to 'E0'.
 * @returns {string} - The generated IC serial number in the format 'E0:XX:XX:XX:XX:XX:XX:XX'.
 */
function generateIcSerialNumber(manufacturerCode?: string): string {
  // Validate and set the manufacturer code
  const validManufacturerCodes = ['E0', 'E2']
  const mfgCode = (manufacturerCode || 'E0').toUpperCase()

  if (!validManufacturerCodes.includes(mfgCode)) {
    throw new Error(`Invalid manufacturer code. Must be one of ${validManufacturerCodes.join(', ')}.`)
  }

  // Generate 7 random bytes for the unique serial number
  const serialNumberBytes = []
  for (let i = 0; i < 7; i++) {
    const randomByte = Math.floor(Math.random() * 256) // Random byte between 0x00 and 0xFF
    serialNumberBytes.push(randomByte)
  }

  // Combine manufacturer code and serial number bytes
  const icSerialNumberBytes = [parseInt(mfgCode, 16), ...serialNumberBytes]

  // Convert bytes to hexadecimal strings and format as 'XX:XX:XX:XX:XX:XX:XX:XX'
  const icSerialNumberHexArray = icSerialNumberBytes.map((byte) => byte.toString(16).padStart(2, '0').toUpperCase())
  const icSerialNumber = icSerialNumberHexArray.join(':')

  return icSerialNumber
}

export function generateRandomEMChip(type: EM4425ChipType, mode: EM4425Mode = EM4425Mode.CryptoMode1): EM4425 {
  const sn = generateIcSerialNumber()

  const chip: EM4425 = {
    serialNumber: sn,
    uid: generateUid(sn),
    ...(type !== EM4425ChipType.EM4425V16 && { tid: generateTid(sn) }),
    type,
    accessPassword: generateSecurePassword(16),
    killPassword: generateSecurePassword(16),
    counter: 0, // Initialize the counter if needed
  }

  // Generate crypto keys based on the mode
  switch (mode) {
    case EM4425Mode.CryptoMode0:
      // No crypto keys needed for CryptoMode0 (No Encryption)
      break
    case EM4425Mode.CryptoMode1:
      chip.cryptoKeyId0 = randomBytes(16).toString('hex')
      break
    case EM4425Mode.CryptoMode2:
      chip.cryptoKeyId0 = randomBytes(16).toString('hex')
      chip.cryptoKeyId1 = randomBytes(16).toString('hex')
      break
    default:
      throw new Error('Unsupported Crypto Mode')
  }

  return chip
}
