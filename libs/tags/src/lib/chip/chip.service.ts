import { validate<PERSON>rReject } from 'class-validator'

import { EM4425ChipType, EM4425ValidationArguments, authenticateEM4425, createEM4425Chip } from './EM'
import { NXPChipType, NXPValidationArguments, authenticateNXP } from './NXP'
import { createNXPChip } from './NXP/nxp.utils'

import type { Chip, ChipUnion, ChipValidationArguments } from './chip.entity'
import type { EM4425, CreateEM4425Chip } from './EM'
import type { EPCComponents } from './epc'
import type { NXP, CreateNXPChip } from './NXP'

const chipTypes = {
  em4425: Object.values(EM4425ChipType),
  nxp: Object.values(NXPChipType),
}

function isChipValidationArguments(
  args: ChipValidationArguments | EM4425ValidationArguments | NXPValidationArguments,
): args is ChipValidationArguments {
  return (args as ChipValidationArguments).em !== undefined || (args as ChipValidationArguments).nxp !== undefined
}

function isEM4425(chip: Chip): chip is Chip & EM4425 {
  return chipTypes.em4425.includes(chip.type as EM4425ChipType)
}

function isNXP(chip: Chip): chip is Chip & NXP {
  return chipTypes.nxp.includes(chip.type as NXPChipType)
}

export async function parseValidationArguments(
  args: ChipValidationArguments | EM4425ValidationArguments | NXPValidationArguments,
) {
  let parseArgs: EM4425ValidationArguments | NXPValidationArguments

  if (isChipValidationArguments(args)) {
    if (args.em) {
      parseArgs = new EM4425ValidationArguments(args.em)
    }

    if (args.nxp) {
      parseArgs = new NXPValidationArguments(args.nxp)
    }
  } else {
    try {
      const testArguments = new EM4425ValidationArguments(args as EM4425ValidationArguments)
      await validateOrReject(testArguments)
      parseArgs = testArguments
    } catch {
      // DO NOTHING
    }

    try {
      const testArguments = new NXPValidationArguments(args as NXPValidationArguments)
      await validateOrReject(testArguments)
      parseArgs = testArguments
    } catch {
      // DO NOTHING
    }
  }

  if (!parseArgs) {
    throw new Error('Invalid chip type')
  }

  await validateOrReject(parseArgs)
  return parseArgs
}

function normalizeUID(uid: string): string {
  // Remove any existing colons
  const cleanUID = uid.replace(/:/g, '')

  // Check if the UID is in the correct format (14 or 16 hexadecimal characters)
  if (/^[0-9A-Fa-f]{14}$/.test(cleanUID)) {
    // For 7-byte UIDs (NTAG 224/424 DNA)
    return cleanUID.match(/.{1,2}/g).join(':')
  } else if (/^[0-9A-Fa-f]{16}$/.test(cleanUID)) {
    // For 8-byte UIDs (ICODE DNA)
    return cleanUID.match(/.{1,2}/g).join(':')
  } else {
    // If the UID doesn't match expected formats, return it unchanged
    return uid
  }
}

export function tagQueryFromArguments(id: string, args: EM4425ValidationArguments | NXPValidationArguments) {
  if (args instanceof EM4425ValidationArguments) {
    return {
      $or: [{ 'chip.uid': id }, { 'chip.tid': id }],
    }
  }

  if (args instanceof NXPValidationArguments) {
    return { 'chip.serialNumber': normalizeUID(id) }
  }

  throw new Error('Invalid validation arguments')
}

export function tagQueryFromChip(chip: Chip) {
  if (isEM4425(chip)) {
    return {
      $or: [
        { 'chip.uid': chip.uid },
        { 'chip.serialNumber': chip.serialNumber },
        ...(chip.tid ? [{ 'chip.tid': chip.tid }] : []),
      ],
    }
  }

  if (isNXP(chip)) {
    return { 'chip.serialNumber': chip.serialNumber }
  }

  throw new Error('Invalid chip type')
}

export function tagQueryFromChips(chips: ChipUnion[]) {
  const emChips = chips.filter(isEM4425)
  const nxpChips = chips.filter(isNXP)

  return {
    $or: [
      { 'chip.serialNumber': { $in: emChips.map((chip) => chip.serialNumber) } },
      { 'chip.serialNumber': { $in: nxpChips.map((chip) => chip.serialNumber) } },
    ],
  }
}

export async function createAndVerifyChip(data: CreateEM4425Chip | CreateNXPChip) {
  if (isEM4425(data)) {
    const chip = createEM4425Chip(data)
    await validateOrReject(chip, { whitelist: true })
    return chip
  }

  if (isNXP(data)) {
    const chip = createNXPChip(data)
    await validateOrReject(chip, { whitelist: true })
    return chip
  }

  throw new Error('Invalid chip type')
}

export interface AuthenticatedChip {
  counter?: number
  sessionPassword?: string
  epc?: EPCComponents
}

export function authenticateChip(
  chip: Chip,
  args: EM4425ValidationArguments | NXPValidationArguments,
): AuthenticatedChip {
  if (isEM4425(chip) && args instanceof EM4425ValidationArguments) {
    return authenticateEM4425(chip, args)
  }

  if (isNXP(chip) && args instanceof NXPValidationArguments) {
    return authenticateNXP(chip, args)
  }

  throw new Error('Invalid chip type')
}
