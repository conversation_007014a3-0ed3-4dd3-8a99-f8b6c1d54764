import { Directive, HideField, ObjectType } from '@nestjs/graphql'
import { Exclude } from 'class-transformer'
import { ObjectId } from 'mongodb'

import { FieldObjectID, MongoDBEntity } from '@phigital-loyalty/database'

@ObjectType()
@Directive('@extends')
@Directive('@key(fields: "_id")')
export class OidcClient extends MongoDBEntity {
  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Organization.',
  })
  organizationId?: ObjectId

  @HideField()
  @Exclude({ toPlainOnly: true })
  customDomains?: string[]
}
