import { Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Collection } from 'mongodb'

import { InjectCollection, InjectDataloader, MongoDBLoader, MongoDBEntityService } from '@phigital-loyalty/database'

import { OidcClient } from './oidc-client.entity'

@Injectable()
export class OidcClientService extends MongoDBEntityService<OidcClient> {
  constructor(
    @InjectCollection('oidcClient') collection: Collection<OidcClient>,
    @InjectDataloader('oidcClient') dataloader: MongoDBLoader<OidcClient>,
    moduleRef: ModuleRef,
  ) {
    super(OidcClient, collection, dataloader, moduleRef)
  }
}
