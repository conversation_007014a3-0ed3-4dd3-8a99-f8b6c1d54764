import { Directive, Field, ObjectType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'

import { OrganizationTemplate } from '@phigital-loyalty/accounts/lib/organization/organization-template.entity'
import { MongoDBEntity } from '@phigital-loyalty/database'

@ObjectType()
@Directive('@extends')
@Directive('@key(fields: "_id")')
export class Organization extends MongoDBEntity {
  @Field(() => String, { description: 'Name of the organization' })
  @ApiProperty({ type: String, description: 'Name of the organization' })
  @Directive('@shareable')
  name: string

  @Field(() => String, { nullable: true, description: 'The tag of the organization' })
  @ApiProperty({ type: String, required: false, description: 'The tag of the organization' })
  @Directive('@shareable')
  tag?: string

  @Field(() => String, { nullable: true, description: 'The description of the organization' })
  @ApiProperty({ type: String, required: false, description: 'The description of the organization' })
  @Directive('@shareable')
  description?: string

  @Field(() => String, { nullable: true, description: 'The URL to the logo of the organization' })
  @ApiProperty({ type: String, required: false, description: 'The URL to the logo of the organization' })
  @Directive('@shareable')
  logo?: string

  @Field(() => OrganizationTemplate, { nullable: true, description: 'The template of the organization' })
  @ApiProperty({ type: OrganizationTemplate, required: false, description: 'The template of the organization' })
  @Directive('@shareable')
  template?: OrganizationTemplate

  @Field(() => [String], { nullable: true, description: 'The custom domains of the organization' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The custom domains of the organization' })
  @Directive('@shareable')
  customDomains?: string[]

  // static excludeInputFields = ['followIds', 'blockedIds']
}
