import { Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Collection } from 'mongodb'

import { InjectCollection, InjectDataloader, MongoDBLoader, BaseMongoDBEntityService } from '@phigital-loyalty/database'

import { Organization } from './organization.entity'

@Injectable()
export class OrganizationService extends BaseMongoDBEntityService<Organization> {
  constructor(
    @InjectCollection('organization') collection: Collection<Organization>,
    @InjectDataloader('organization') dataloader: MongoDBLoader<Organization>,
    moduleRef: ModuleRef,
  ) {
    super(Organization, collection, dataloader, moduleRef)
  }
}
