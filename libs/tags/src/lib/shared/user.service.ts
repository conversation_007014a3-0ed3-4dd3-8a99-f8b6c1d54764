import { Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Collection } from 'mongodb'

import { InjectCollection, InjectDataloader, MongoDBLoader, BaseMongoDBEntityService } from '@phigital-loyalty/database'

import { User } from './user.entity'

@Injectable()
export class UserService extends BaseMongoDBEntityService<User> {
  constructor(
    @InjectCollection('user') collection: Collection<User>,
    @InjectDataloader('user') dataloader: MongoDBLoader<User>,
    moduleRef: ModuleRef,
  ) {
    super(User, collection, dataloader, moduleRef)
  }
}
