import { Directive, Field, GraphQLISODateTime, ObjectType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { Timezone } from 'graphql-timezone'

import {
  checkRole,
  EmailAddressScalar,
  paginated,
  PhoneNumberScalar,
  Private,
  systemRoles,
  TimeZone,
  TimeZoneScalar,
} from '@phigital-loyalty/core'
import { MongoDBEntity } from '@phigital-loyalty/database'

@ObjectType()
@Directive('@extends')
@Directive('@shareable')
export class Address {
  @Field(() => String)
  @ApiProperty({ type: String })
  streetAddress: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  formatted?: string

  @Field(() => String)
  @ApiProperty({ type: String })
  locality: string

  @Field(() => String)
  @ApiProperty({ type: String })
  postalCode: string

  @Field(() => String)
  @ApiProperty({ type: String })
  region: string

  @Field(() => String)
  @ApiProperty({ type: String })
  country: string
}

@ObjectType()
@Directive('@extends')
@Directive('@shareable')
@Directive('@key(fields: "_id")')
export class User extends MongoDBEntity {
  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  name?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  familyName?: string
  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  givenName?: string
  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  middleName?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  nickname?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  picture?: string

  @Field(() => GraphQLISODateTime, { nullable: true })
  @ApiProperty({ type: Date, required: false })
  birthdate?: Date

  // Emails

  @Private(systemRoles)
  @Field(() => EmailAddressScalar, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ type: String })
  email: string

  @Private(systemRoles)
  @Field(() => EmailAddressScalar, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ type: String, required: false })
  contactEmail?: string

  // Phone

  @Private(systemRoles)
  @Field(() => PhoneNumberScalar, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ type: String, required: false })
  phoneNumber?: string

  // Address
  @Private(systemRoles)
  @Field(() => Address, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ type: Address, required: false })
  address?: Address

  // Extras

  @Private(systemRoles)
  @Field(() => String, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ type: String, required: false })
  gender?: string

  @Field(() => String, { nullable: true })
  @ApiProperty({ type: String, required: false })
  website?: string

  @Private(systemRoles)
  @Field(() => String, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ type: String, required: false })
  locale?: string

  @Private(systemRoles)
  @Field(() => TimeZoneScalar, { nullable: true, middleware: [checkRole] })
  @ApiProperty({ enum: Timezone, required: false })
  zoneinfo?: TimeZone
}

@ObjectType()
@Directive('@extends')
export class PaginatedUser extends paginated(User) {}
