import { Directive, Field, ObjectType } from '@nestjs/graphql'
import { ApiProperty } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import { EntityType, paginated } from '@phigital-loyalty/core'
import { FieldObjectID, MongoDBEntity } from '@phigital-loyalty/database'

@ObjectType()
@Directive('@key(fields: "_id")')
@EntityType<Program>({
  stats: true,

  paginated: () => PaginatedPrograms,
})
export class Program extends MongoDBEntity {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => String, { description: 'Name of the program' })
  @ApiProperty({ type: String, description: 'Name of the program' })
  name: string

  @Field(() => String, { nullable: true, description: 'Custom redirect URL' })
  @ApiProperty({ type: String, required: false, description: 'Custom redirect URL' })
  customRedirect?: string
}

@ObjectType()
export class PaginatedPrograms extends paginated(Program) {}
