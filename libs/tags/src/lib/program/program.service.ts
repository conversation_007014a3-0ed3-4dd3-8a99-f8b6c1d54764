import { Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { Collection } from 'mongodb'

import { InjectCollection, InjectDataloader, MongoDBLoader, MongoDBEntityService } from '@phigital-loyalty/database'

import { Program } from './program.entity'

@Injectable()
export class ProgramService extends MongoDBEntityService<Program> {
  constructor(
    @InjectCollection('program') collection: Collection<Program>,
    @InjectDataloader('program') dataloader: MongoDBLoader<Program>,
    moduleRef: ModuleRef,
  ) {
    super(Program, collection, dataloader, moduleRef)
  }
}
