import { Modu<PERSON> } from '@nestjs/common'

import { MongodbModule } from '@phigital-loyalty/database'

import { ActivationService } from './activation'
import { ProductService } from './product'
import { ProgramService } from './program'
import { OidcClientService, OrganizationService, UserService } from './shared'
import { TagService } from './tag'

const services = [
  ActivationService,
  ProductService,
  ProgramService,
  TagService,
  OrganizationService,
  UserService,
  OidcClientService,
]

@Module({
  imports: [
    MongodbModule.forFeature([
      'activation',
      'product',
      'program',
      'registration',
      'tag',
      'organization',
      'user',
      'oidcClient',
    ]),
  ],
  controllers: [],
  providers: services,
  exports: services,
})
export class TagsModule {}
