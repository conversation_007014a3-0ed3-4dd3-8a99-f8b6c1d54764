import { finished } from 'node:stream/promises'

import { parse } from '@fast-csv/parse'
import { Injectable } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { type AnyBulkWriteOperation, type Collection, ObjectId, type OptionalId } from 'mongodb'

import { hash, NotifyUserInput, populateBasedOnDTOFields, Stats, StatsInput } from '@phigital-loyalty/core'
import {
  InjectCollection,
  InjectDataloader,
  MongoDBLoader,
  MongoDBEntityService,
  parseFilter,
  stats,
  Filter,
  WebhookService,
  NotifyService,
} from '@phigital-loyalty/database'

import { CreateTag, CreateTags, Tag, TagEntityEvent } from './tag.entity'
import {
  ChipValidationArguments,
  EM4425ValidationArguments,
  NXPValidationArguments,
  createAndVerifyChip,
  ChipUnion,
  CreateChipCSVRow,
  parseValidationArguments,
  tagQueryFromArguments,
  authenticateChip,
  tagQueryFromChip,
  tagQueryFromChips,
} from '../chip'
import { ProductDetails, ProductService } from '../product'
import { ProgramService } from '../program'
import { UserService } from '../shared'

const MAX_DB_OPERATIONS_PER_REQUEST =
  typeof process.env.MAX_DB_OPERATIONS_PER_REQUEST !== 'undefined'
    ? parseInt(process.env.MAX_DB_OPERATIONS_PER_REQUEST)
    : 1000

@Injectable()
export class TagService extends MongoDBEntityService<Tag> {
  constructor(
    @InjectCollection('tag') collection: Collection<Tag>,
    @InjectDataloader('tag') dataloader: MongoDBLoader<Tag>,
    moduleRef: ModuleRef,
    readonly userService: UserService,
    readonly programService: ProgramService,
    readonly productService: ProductService,
    readonly webhookService: WebhookService,
    readonly notifyService: NotifyService,
  ) {
    super(Tag, collection, dataloader, moduleRef)
  }

  async authenticate(id: string, args: ChipValidationArguments | EM4425ValidationArguments | NXPValidationArguments) {
    const parsedArgs = await parseValidationArguments(args)
    const query = tagQueryFromArguments(id, parsedArgs)
    const tag = await this.findOne(query)

    if (!tag) {
      throw new Error(`Tag not found: ${id}`)
    }

    const { counter, sessionPassword, epc } = authenticateChip(tag.chip, parsedArgs)

    if ((counter !== undefined && typeof counter !== 'boolean') || epc) {
      await this.updateOne(
        { _id: tag._id },
        {
          $set: {
            ...(counter && { 'chip.counter': counter }),
            ...(epc && { epc }),
          },
        },
      )
    }

    return { tag, sessionPassword, epc }
  }

  async createTag(data: CreateTag) {
    await this.validateCreateTags(data)

    const chip = await createAndVerifyChip(data.chip.em || data.chip.nxp)

    const exists = await this.findOne(tagQueryFromChip(chip))

    if (exists) {
      throw new Error(`Tag with Chip ${JSON.stringify(exists.chip)} already exists`)
    }

    return this.createOne({
      chip,
      organizationId: data.organizationId,
      ...(data.programId ? { programId: data.programId } : {}),
      ...(data.productId ? { productId: data.productId } : {}),
      ...(data.productDetails ? { productDetails: data.productDetails } : {}),
      ...(data.labels ? { labels: data.labels } : {}),
    })
  }

  async createTags(data: CreateTags) {
    await this.validateCreateTags(data)

    let numberOfCreatedTags = 0

    if (data.chips) {
      const chips: ChipUnion[] = []

      for (const chipData of data.chips) {
        chips.push(await createAndVerifyChip(chipData.em || chipData.nxp))
      }

      numberOfCreatedTags += await this.validateAndCreateTags(data, chips)
    }

    if (data.csv) {
      numberOfCreatedTags += await this.createTagsByCSV(data)
    }

    return numberOfCreatedTags
  }

  async claim(id: string | ObjectId, currentUserId: string | ObjectId) {
    const tag = await this.findById(id)

    if (!tag) {
      throw new Error(`Tag with id ${id} not found`)
    }

    if (tag.userId) {
      throw new Error(`Tag with id ${id} already claimed`)
    }

    const res = await this.updateOne(
      { _id: tag._id },
      { userId: new ObjectId(currentUserId), registeredAt: new Date() },
    )
    await this.registerAuditEvent(TagEntityEvent.claimed, res)
    return res
  }

  async release(id: string | ObjectId, currentUserId: string | ObjectId) {
    const tag = await this.findById(id)

    if (!tag) {
      throw new Error(`Tag with id ${id} not found`)
    }

    if (!tag.userId) {
      throw new Error(`Tag with id ${id} not claimed`)
    }

    if (tag.userId.toString() !== currentUserId.toString()) {
      throw new Error(`Tag with id ${id} not claimed by current user`)
    }

    await this.registerAuditEvent(TagEntityEvent.released, tag)

    return this.updateOne({ _id: tag._id }, { $unset: { userId: '', registeredAt: '' } })
  }

  async transfer(id: string | ObjectId, currentUserId: string | ObjectId, userId: string | ObjectId) {
    const tag = await this.findById(id)

    if (!tag) {
      throw new Error(`Tag with id ${id} not found`)
    }

    if (!tag.userId) {
      throw new Error(`Tag with id ${id} not claimed`)
    }

    if (tag.userId.toString() !== currentUserId.toString()) {
      throw new Error(`Tag with id ${id} not claimed by current user`)
    }

    await this.registerAuditEvent(TagEntityEvent.released, tag)

    const res = await this.updateOne({ _id: tag._id }, { userId: new ObjectId(userId), registeredAt: new Date() })
    await this.registerAuditEvent(TagEntityEvent.claimed, res)
    return res
  }

  async attachProductToTags(filter: Filter<Tag>, productId: string | ObjectId, productDetails: ProductDetails) {
    await this.updateMany(filter, { $set: { productId: new ObjectId(productId), productDetails } })

    return this.find(filter)
  }

  async attachProgramToTags(filter: Filter<Tag>, programId: string | ObjectId) {
    await this.updateMany(filter, { $set: { programId: new ObjectId(programId) } })

    return this.find(filter)
  }

  async detachProductFromTags(filter: Filter<Tag>) {
    await this.updateMany(filter, { $unset: { productId: '', productDetails: '' } })

    return this.find(filter)
  }

  async detachProgramFromTags(filter: Filter<Tag>) {
    await this.updateMany(filter, { $unset: { programId: '' } })

    return this.find(filter)
  }

  async peopleStats(scope: StatsInput, query: Filter<Tag>, projection: Array<keyof Stats>): Promise<Partial<Stats>> {
    const key = this.cache.generateKey('people', `stats:${hash(query)}:${hash(scope)}:${hash(projection || [])}`)
    const fromCache = await this.cache.getValue(key)

    if (fromCache) {
      return JSON.parse(fromCache)
    }

    const result = await stats<Tag, Stats>(
      this.statsCollection,
      this.collection,
      projection,
      scope,
      'registeredAt',
      this.options?.statsFactory,
      parseFilter(query),
      'userId',
    )

    this.cache.setValue(key, JSON.stringify(result), this.customTTL)
    return result
  }

  async notify(filter: Filter<Tag>, input: NotifyUserInput) {
    const tags = await this.find({ ...filter, userId: { $exists: true } }, { projection: { userId: 1 } })

    const users = await this.userService.find({ _id: { $in: tags.map((tag) => tag.userId) } })

    await this.notifyService.send(users, input)
  }

  async createTagsByCSV(data: CreateTags) {
    let numberOfCreatedTags = 0
    const operations: AnyBulkWriteOperation<Tag>[] = []

    const stream = parse<CreateChipCSVRow, ChipUnion>({
      headers: (headers) =>
        headers.map((h) => {
          const header = h.toLowerCase()
          if (header === 'chiptype' || header === 'chip.type') {
            return 'type'
          }
          if (header === 'id' || header === 'chipid' || header === 'chip.id') {
            return 'id'
          }
          if (header === 'serialNumber' || header === 'chipserialNumber' || header === 'chip.serialNumber') {
            return 'serialNumber'
          }
        }),
    })
      .validate((chip: ChipUnion) => {
        if (!chip?.type) {
          return false
        }

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if (!(chip as any)?.serialNumber && !(chip as any)?.id) {
          return false
        }

        return true
      })
      .transform(async (data: CreateChipCSVRow, callback) => callback(null, await createAndVerifyChip(data)))
      .on('error', (error) => {
        throw new Error(`Error parsing CSV: ${error.message}`)
      })
      .on('data-invalid', (row, rowNumber) => {
        throw new Error(`Invalid data in row ${rowNumber}: ${JSON.stringify(row)}`)
      })
      .on('data', async (chip: ChipUnion) => {
        operations.push({
          insertOne: {
            document: populateBasedOnDTOFields(
              {
                chip,
                organizationId: data.organizationId,
                ...(data.programId ? { programId: data.programId } : {}),
                ...(data.productId ? { productId: data.productId } : {}),
                ...(data.productDetails ? { productDetails: data.productDetails } : {}),
                ...(data.labels ? { labels: data.labels } : {}),
              },
              this.dtoFields,
            ) as OptionalId<Tag>,
          },
        })

        if (operations.length >= MAX_DB_OPERATIONS_PER_REQUEST) {
          stream.pause()

          const result = await this.bulkWrite(operations, false)
          operations.length = 0
          numberOfCreatedTags += result.reduce((acc, r) => acc + r.insertedCount, 0)

          stream.resume()
        }
      })

    stream.write(data.csv)
    stream.end()

    await finished(stream)

    return numberOfCreatedTags
  }

  private async validateCreateTags(data: CreateTag | CreateTags) {
    if (data.programId) {
      const programExists = await this.programService.findById(data.programId)

      if (!programExists) {
        throw new Error(`Program with id ${data.programId} not found`)
      }
    }

    if (data.productId) {
      const productExists = await this.productService.findById(data.productId)

      if (!productExists) {
        throw new Error(`Product with id ${data.productId} not found`)
      }
    }

    if (data.productDetails && !data.productId) {
      throw new Error('ProductDetails must be provided with productId')
    }
  }

  private async validateAndCreateTags(data: CreateTags, chips: ChipUnion[]) {
    const exists = await this.findOne(tagQueryFromChips(chips))

    if (exists) {
      throw new Error(`Tag with Chip ${JSON.stringify(exists.chip)} already exists`)
    }

    const operations: AnyBulkWriteOperation<Tag>[] = chips.map((chip) => ({
      insertOne: {
        document: populateBasedOnDTOFields(
          {
            chip,
            organizationId: data.organizationId,
            ...(data.programId ? { programId: data.programId } : {}),
            ...(data.productId ? { productId: data.productId } : {}),
            ...(data.productDetails ? { productDetails: data.productDetails } : {}),
            ...(data.labels ? { labels: data.labels } : {}),
          },
          this.dtoFields,
        ) as OptionalId<Tag>,
      },
    }))
    this.cache.clear(this.collection.collectionName)

    const result = await this.bulkWrite(operations, false)

    return result.reduce((acc, r) => acc + r.insertedCount, 0)
  }
}
