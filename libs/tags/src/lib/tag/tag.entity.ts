import { Directive, Field, GraphQLISODateTime, InputType, ObjectType } from '@nestjs/graphql'
import { ApiProperty, getSchemaPath } from '@nestjs/swagger'
import { ObjectId } from 'mongodb'

import { EntityType, paginated } from '@phigital-loyalty/core'
import { FieldObjectID, MongoDBEntity } from '@phigital-loyalty/database'

import { Chip, chipInterfaceResolver, CreateChip } from '../chip'
import {
  EPCComponentsUnion,
  SGTIN96Components,
  SSCC96Components,
  GID96Components,
  SGLN96Components,
  GRAI96Components,
  GIAI96Components,
  ISO15963Components,
} from '../chip/epc/epc.entities'
import { ProductDetails } from '../product'

export enum TagEntityEvent {
  claimed = 'claimed',
  released = 'released',
}

export enum TagWebhookEvent {
  tap = 'tap',
}

@ObjectType()
@Directive('@key(fields: "_id")')
@EntityType<Tag>({
  relations: {
    programId: 'Program',
    productId: 'Product',
  },

  stats: true,

  paginated: () => PaginatedTags,

  excludeInputFields: ['chip', 'epc'],
})
export class Tag extends MongoDBEntity {
  @Field(() => Chip)
  @ApiProperty({ type: Chip })
  chip: Chip

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Organization.',
  })
  organizationId?: ObjectId

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated User.',
  })
  userId?: ObjectId

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Program.',
  })
  programId?: ObjectId

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Product.',
  })
  productId?: ObjectId

  @Field(() => ProductDetails, { nullable: true, description: 'Product details' })
  @ApiProperty({ type: ProductDetails, required: false, description: 'Product details' })
  productDetails?: ProductDetails

  @Field(() => GraphQLISODateTime, { nullable: true, description: 'The time at which the tag was registered.' })
  @ApiProperty({ type: Date, required: false, description: 'The time at which the tag was registered.' })
  registeredAt?: Date

  @Field(() => [String], { nullable: true, description: 'The labels associated with the tag.' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The labels associated with the tag.' })
  labels?: string[]

  @Field(() => EPCComponentsUnion, { nullable: true, description: 'Decoded EPC components.' })
  @ApiProperty({
    description: 'Decoded EPC components.',
    discriminator: { propertyName: 'scheme' },
    oneOf: [
      { $ref: getSchemaPath(SGTIN96Components) },
      { $ref: getSchemaPath(SSCC96Components) },
      { $ref: getSchemaPath(GID96Components) },
      { $ref: getSchemaPath(SGLN96Components) },
      { $ref: getSchemaPath(GRAI96Components) },
      { $ref: getSchemaPath(GIAI96Components) },
      { $ref: getSchemaPath(ISO15963Components) },
    ],
    required: false,
  })
  epc?: typeof EPCComponentsUnion

  @Field(() => Boolean, { nullable: true, description: 'Whether the tag is disabled.' })
  @ApiProperty({ type: Boolean, required: false, description: 'Whether the tag is disabled.' })
  disabled?: boolean

  @Field(() => String, { nullable: true, description: 'The reason the tag is disabled.' })
  @ApiProperty({ type: String, required: false, description: 'The reason the tag is disabled.' })
  disabledReason?: string

  constructor(data: Tag) {
    data.chip = chipInterfaceResolver(data.chip)
    super(data)
  }
}

@ObjectType()
export class PaginatedTags extends paginated(Tag) {}

@InputType()
export class CreateTag {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => CreateChip, { description: 'The chip to create.' })
  @ApiProperty({ type: CreateChip, description: 'The chip to create.' })
  chip: CreateChip

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Program.',
  })
  programId?: ObjectId

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Product.',
  })
  productId?: ObjectId

  @Field(() => ProductDetails, { nullable: true, description: 'Product details' })
  @ApiProperty({ type: ProductDetails, required: false, description: 'Product details' })
  productDetails?: ProductDetails

  @Field(() => [String], { nullable: true, description: 'The labels associated with the tag.' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The labels associated with the tag.' })
  labels?: string[]
}

@InputType()
export class CreateTags {
  @FieldObjectID({
    description: 'The ID of the associated Organization.',
  })
  organizationId: ObjectId

  @Field(() => [CreateChip], { nullable: true, description: 'The chips to create.' })
  @ApiProperty({ type: CreateChip, isArray: true, required: false, description: 'The chips to create.' })
  chips?: CreateChip[]

  @Field(() => String, { nullable: true, description: 'CSV data representing the chips to create.' })
  @ApiProperty({ type: String, required: false, description: 'CSV data representing the chips to create.' })
  csv?: string

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Program.',
  })
  programId?: ObjectId

  @FieldObjectID({
    nullable: true,
    description: 'The ID of the associated Product.',
  })
  productId?: ObjectId

  @Field(() => ProductDetails, { nullable: true, description: 'Product details' })
  @ApiProperty({ type: ProductDetails, required: false, description: 'Product details' })
  productDetails?: ProductDetails

  @Field(() => [String], { nullable: true, description: 'The labels associated with the tag.' })
  @ApiProperty({ type: String, isArray: true, required: false, description: 'The labels associated with the tag.' })
  labels?: string[]
}

@ObjectType()
export class Tap {
  @Field(() => Tag, { description: 'The tag that was tapped.' })
  @ApiProperty({ type: Tag, description: 'The tag that was tapped.' })
  tag: Tag

  @Field(() => String, { nullable: true, description: 'The session password.' })
  @ApiProperty({ type: String, required: false, description: 'The session password.' })
  sessionPassword?: string
}
