{"name": "@phigital-loyalty/tags", "version": "0.0.0", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/phigital-loyalty/daptap-backend.git", "directory": "libs/database"}, "main": "src/index", "typings": "src/index.d.ts", "publishConfig": {"access": "restricted"}, "dependencies": {"@phigital-loyalty/database": "0.0.0", "@phigital-loyalty/core": "0.0.0", "@nestjs/common": "^11.0.7", "mongodb": "^6.7.0", "@nestjs/graphql": "13.1.0", "@nestjs/swagger": "11.2.0", "graphql-timezone": "^2.0.7", "tslib": "^2.6.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "@phigital-loyalty/accounts": "0.0.0", "@nestjs/core": "11.1.6", "@fast-csv/parse": "^5.0.2"}}