import { ForbiddenException } from '@nestjs/common'
import { GraphQLError } from 'graphql'

/**
 * Exception thrown when attempting to access forbidden fields
 */
export class ForbiddenFieldsException extends ForbiddenException {
  constructor(fields: string[]) {
    super(`Unauthorized access to fields: [${fields.join(', ')}]`)
    this.name = 'ForbiddenFieldsException'
  }

  /**
   * Converts the exception to a GraphQL-compatible error
   */
  toGraphQLError(): GraphQLError {
    return new GraphQLError(this.message, {
      extensions: {
        code: 'FORBIDDEN_FIELDS',
        http: { status: 403 },
        fields: this.message.replace('Unauthorized access to fields: ', ''),
      },
    })
  }
}
