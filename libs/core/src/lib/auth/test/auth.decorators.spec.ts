import { createMock } from '@golevelup/ts-jest'
import { ExecutionContext, HttpException } from '@nestjs/common'
import { GqlExecutionContext } from '@nestjs/graphql'

import { Private } from '../auth.decorators'
import { Role, type IntrospectionResponse } from '../auth.interfaces'
import { createAuthFixture } from './fixtures'

describe('Auth Decorators', () => {
  describe('CurrentUserId', () => {
    it('should extract user ID from HTTP context', async () => {
      const auth = createAuthFixture({ sub: 'test-user-id' })
      const context = createMock<ExecutionContext>()

      context.switchToHttp().getRequest.mockReturnValue({ auth })
      context.getType.mockReturnValue('http')

      // Test implementation rather than decorator itself
      const getUser = async () => {
        if (context.getType() === 'http') {
          const request = context.switchToHttp().getRequest() as { auth?: { sub: string } }
          return request.auth?.sub
        } else if (context.getType() === 'graphql') {
          const ctx = GqlExecutionContext.create(context)
          return ctx.getContext().auth?.sub
        }
      }

      const userId = await getUser()
      expect(userId).toBe('test-user-id')
    })

    it('should extract user ID from GraphQL context', async () => {
      const auth = createAuthFixture({ sub: 'test-user-id' })
      const context = createMock<ExecutionContext>()
      const gqlContext = { auth }

      context.getType.mockReturnValue('graphql')
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getContext: jest.fn().mockReturnValue(gqlContext),
      })

      const getUser = async () => {
        if (context.getType() === 'http') {
          const request = context.switchToHttp().getRequest() as { auth?: { sub: string } }
          return request.auth?.sub
        } else if (context.getType() === 'graphql') {
          const ctx = GqlExecutionContext.create(context)
          return ctx.getContext().auth?.sub
        }
      }

      const userId = await getUser()
      expect(userId).toBe('test-user-id')
    })

    it('should throw exception when auth is missing', async () => {
      const context = createMock<ExecutionContext>()

      context.switchToHttp().getRequest.mockReturnValue({})
      context.getType.mockReturnValue('http')

      const getUser = async () => {
        if (context.getType() === 'http') {
          const request = context.switchToHttp().getRequest() as { auth?: { sub: string } }
          if (!request.auth?.sub) {
            throw new HttpException('CurrentUserId: No user found', 403)
          }
          return request.auth?.sub
        }
      }

      await expect(getUser()).rejects.toThrow(HttpException)
    })
  })

  describe('CurrentAuth', () => {
    it('should extract auth from HTTP context', async () => {
      const auth = createAuthFixture()
      const context = createMock<ExecutionContext>()

      context.switchToHttp().getRequest.mockReturnValue({ auth })
      context.getType.mockReturnValue('http')

      const getAuth = async () => {
        if (context.getType() === 'http') {
          const request = context.switchToHttp().getRequest() as { auth?: IntrospectionResponse }
          return request.auth
        } else if (context.getType() === 'graphql') {
          const ctx = GqlExecutionContext.create(context)
          return ctx.getContext().auth
        }
      }

      const result = await getAuth()
      expect(result).toBe(auth)
    })

    it('should extract auth from GraphQL context', async () => {
      const auth = createAuthFixture()
      const context = createMock<ExecutionContext>()

      context.getType.mockReturnValue('graphql')
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getContext: jest.fn().mockReturnValue({ auth }),
      })

      const getAuth = async () => {
        if (context.getType() === 'http') {
          const request = context.switchToHttp().getRequest() as { auth?: { sub: string } }
          return request.auth
        } else if (context.getType() === 'graphql') {
          const ctx = GqlExecutionContext.create(context)
          return ctx.getContext().auth
        }
      }

      const result = await getAuth()
      expect(result).toBe(auth)
    })
  })

  describe('Private decorator', () => {
    it('should apply correct metadata', () => {
      // Create a class using the decorator
      class TestClass {
        @Private([Role.Admin])
        adminField: string
      }

      expect(TestClass).toBeDefined()
    })
  })
})
