// Mock modules with ES module issues first
jest.mock(
  'openid-client',
  () => ({
    discovery: jest.fn(),
    tokenIntrospection: jest.fn(),
  }),
  { virtual: true },
)

jest.mock('../../core', () => ({
  getContextParams: jest.fn().mockReturnValue({}),
  ServiceMap: { get: jest.fn().mockReturnValue(undefined) },
}))

jest.mock('../utils', () => {
  const original = jest.requireActual('../utils')
  return {
    ...original,
    can: jest.fn().mockReturnValue(true),
    getAuthFromContext: jest.fn().mockImplementation((context) => {
      if (context.getType() === 'http') {
        return context.switchToHttp().getRequest().auth
      } else if (context.getType() === 'graphql') {
        return GqlExecutionContext.create(context).getContext().auth
      }
    }),
    isResolvingGraphQLField: jest.fn().mockReturnValue(false),
    checkFieldsPermissions: jest.fn(),
  }
})

jest.mock('../policy.handler', () => {
  const original = jest.requireActual('../policy.handler')
  return {
    ...original,
    isPolicyHandlerOptionsParent: jest.fn().mockReturnValue(false),
  }
})

// Import after mocks are set up
import { createMongoAbility } from '@casl/ability'
import { createMock } from '@golevelup/ts-jest'
import { ForbiddenException } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { GqlExecutionContext } from '@nestjs/graphql'

import { Action } from '../action.enum'
import { CHECK_POLICIES_KEY, ROLES_KEY } from '../auth.decorators'
import { IsAuthenticated, PoliciesGuard, authGuardProvider } from '../auth.guards'
import { Role } from '../auth.interfaces'
import { can, checkFieldsPermissions, isResolvingGraphQLField } from '../utils'
import { TestUser, createPolicyHandler, regularUserAuth, systemAdminAuth } from './fixtures'
import { getContextParams } from '../../core'

import type { Ability } from '../auth.interfaces'
import type { ExecutionContext, Logger } from '@nestjs/common'

describe('Auth Guards', () => {
  describe('IsAuthenticated', () => {
    let guard: IsAuthenticated

    beforeEach(() => {
      guard = new IsAuthenticated()
    })

    it('should return true when auth is active', () => {
      const context = createMock<ExecutionContext>()
      const request = { auth: { active: true } }

      context.switchToHttp().getRequest.mockReturnValue(request)
      context.getType.mockReturnValue('http')

      expect(guard.canActivate(context)).toBe(true)
    })

    it('should return false when auth is not active or missing', () => {
      const context = createMock<ExecutionContext>()

      context.switchToHttp().getRequest.mockReturnValue({ auth: { active: false } })
      context.getType.mockReturnValue('http')
      expect(guard.canActivate(context)).toBe(false)

      context.switchToHttp().getRequest.mockReturnValue({})
      expect(guard.canActivate(context)).toBe(false)
    })
  })

  describe('PoliciesGuard', () => {
    let guard: PoliciesGuard
    let reflector: Reflector
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let abilityFactory: any
    let logger: Logger
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let moduleRef: any

    beforeEach(() => {
      reflector = new Reflector()
      jest.clearAllMocks()

      abilityFactory = {
        createForUser: jest.fn().mockResolvedValue({
          can: jest.fn().mockReturnValue(true),
          rules: [],
        }),
      }

      logger = { debug: jest.fn(), error: jest.fn() } as unknown as Logger

      moduleRef = {
        get: jest.fn().mockReturnValue({
          get: jest.fn().mockReturnValue({}),
        }),
      }

      guard = new PoliciesGuard(reflector, abilityFactory, logger, moduleRef)
    })

    it('should allow access for public endpoints', async () => {
      const context = createMock<ExecutionContext>()
      jest.spyOn(reflector, 'get').mockReturnValue(true)

      const result = await guard.canActivate(context)
      expect(result).toBe(true)
      expect(abilityFactory.createForUser).not.toHaveBeenCalled()
    })

    it('should allow access for system admin without checking policies', async () => {
      const context = createMock<ExecutionContext>()
      const auth = systemAdminAuth

      context.switchToHttp().getRequest.mockReturnValue({ auth })
      context.getType.mockReturnValue('http')

      abilityFactory.createForUser.mockClear()
      const result = await guard.canActivate(context)

      expect(result).toBe(true)
      expect(abilityFactory.createForUser).not.toHaveBeenCalled()
    })

    it('should check policies for users with appropriate roles', async () => {
      const context = createMock<ExecutionContext>()
      const auth = regularUserAuth

      jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key) => {
        if (key === CHECK_POLICIES_KEY) {
          return [{ action: Action.Read, dto: TestUser, options: {} }]
        }
        if (key === ROLES_KEY) {
          return [Role.User]
        }
        return null
      })

      context.switchToHttp().getRequest.mockReturnValue({ auth })
      context.getType.mockReturnValue('http')

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(guard as any, 'execPolicyHandler').mockReturnValue(true)

      const result = await guard.canActivate(context)
      expect(result).toBe(true)
      expect(abilityFactory.createForUser).toHaveBeenCalledWith(auth)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect((guard as any).execPolicyHandler).toHaveBeenCalled()
    })

    it('should deny access when policy check fails', async () => {
      const context = createMock<ExecutionContext>()
      const auth = regularUserAuth

      jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key) => {
        if (key === CHECK_POLICIES_KEY) {
          return [{ action: Action.Update, dto: TestUser, options: {} }]
        }
        return null
      })

      context.switchToHttp().getRequest.mockReturnValue({ auth })
      context.getType.mockReturnValue('http')

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(guard as any, 'execPolicyHandler').mockReturnValue(false)

      const result = await guard.canActivate(context)

      expect(result).toBe(false)
      expect(abilityFactory.createForUser).toHaveBeenCalledWith(auth)
      expect(logger.error).toHaveBeenCalled()
      expect(logger.error).toHaveBeenCalledWith(expect.any(ForbiddenException))
    })

    it('should check field permissions for GraphQL requests', async () => {
      const context = createMock<ExecutionContext>()
      const auth = regularUserAuth
      const gqlContext = { auth, req: {} as { ability?: Ability } }

      jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key) => {
        if (key === CHECK_POLICIES_KEY) {
          return [{ action: Action.Read, dto: TestUser, options: {} }]
        }
        return null
      })

      context.getType.mockReturnValue('graphql')

      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getContext: jest.fn().mockReturnValue(gqlContext),
        getInfo: jest.fn().mockReturnValue({
          fieldNodes: [],
          parentType: { name: 'Query' },
        }),
        getArgs: jest.fn().mockReturnValue({}),
      })

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(guard as any, 'execPolicyHandler').mockRestore()

      const ability = {
        can: jest.fn().mockReturnValue(true),
        rules: [],
      }

      abilityFactory.createForUser.mockResolvedValue(ability)

      await guard.canActivate(context)

      expect(checkFieldsPermissions).toHaveBeenCalled()
      expect(gqlContext.req.ability).toBe(ability)
    })
  })

  describe('authGuardProvider integration', () => {
    let mockAbility: Ability
    let context: ExecutionContext

    beforeEach(() => {
      jest.clearAllMocks()
      ;(can as jest.Mock).mockReturnValue(true)
      mockAbility = createMongoAbility([]) as Ability
      context = createMock<ExecutionContext>()
      ;(getContextParams as jest.Mock).mockReturnValue({})
    })

    it('should handle all parameter variations', () => {
      // Test basic permissions
      const handlerBasic = createPolicyHandler(Action.Read, TestUser)
      expect(authGuardProvider(handlerBasic, mockAbility, context)).toBe(true)

      // Test id-based permissions
      const handlerId = createPolicyHandler(Action.Read, TestUser, { id: 'userId' })
      ;(getContextParams as jest.Mock).mockReturnValue({ userId: 'test123' })
      expect(authGuardProvider(handlerId, mockAbility, context)).toBe(true)

      // Test object-based permissions
      const handlerObj = createPolicyHandler(Action.Create, TestUser, { object: 'userData' })
      ;(getContextParams as jest.Mock).mockReturnValue({
        userData: { name: 'Test User', email: '<EMAIL>' },
      })
      expect(authGuardProvider(handlerObj, mockAbility, context)).toBe(true)

      // Test reference-based permissions
      const handlerRef = createPolicyHandler(Action.Read, TestUser, { reference: 'refObj' })
      ;(getContextParams as jest.Mock).mockReturnValue({
        refObj: { _id: 'test123', name: 'Test' },
      })
      expect(authGuardProvider(handlerRef, mockAbility, context)).toBe(true)
    })

    it('should handle GraphQL field resolvers with array IDs', () => {
      const handler = createPolicyHandler(Action.Read, TestUser)
      ;(isResolvingGraphQLField as jest.Mock).mockReturnValue(true)

      // Single ID
      const parentSingle = { _id: 'test123' }
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getArgByIndex: jest.fn().mockReturnValue(parentSingle),
      })
      expect(authGuardProvider(handler, mockAbility, context)).toBe(true)
      expect(can).toHaveBeenCalledTimes(1)

      jest.clearAllMocks()

      // Array of IDs
      const parentArray = { _id: ['id1', 'id2', 'id3'] }
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getArgByIndex: jest.fn().mockReturnValue(parentArray),
      })
      expect(authGuardProvider(handler, mockAbility, context)).toBe(true)
      expect(can).toHaveBeenCalledTimes(3)
    })

    it('should properly deny access when permissions fail', () => {
      const handler = createPolicyHandler(Action.Update, TestUser)
      ;(can as jest.Mock).mockReturnValue(false)

      const result = authGuardProvider(handler, mockAbility, context)
      expect(result).toBe(false)
    })
  })
})
