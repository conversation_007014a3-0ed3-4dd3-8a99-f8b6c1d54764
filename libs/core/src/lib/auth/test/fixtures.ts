import { ObjectId } from 'mongodb'

import { Action } from '../action.enum'
import { Role } from '../auth.interfaces'

import type { IntrospectionResponse, UserRole } from '../auth.interfaces'

/**
 * Test user entity for authorization tests
 */
export class TestUser {
  static options = {}
  static type = 'memory'

  public _id: string
  public name?: string
  public email?: string
  public organizationId?: string

  constructor(data?: Partial<TestUser>) {
    Object.assign(
      this,
      {
        _id: new ObjectId().toString(),
      },
      data,
    )
  }
}

/**
 * Test organization entity for authorization tests
 */
export class TestOrganization {
  static options = {}
  static type = 'memory'

  public _id: string

  constructor(data?: Partial<TestOrganization>) {
    Object.assign(
      this,
      {
        _id: new ObjectId().toString(),
      },
      data,
    )
  }
}

/**
 * Test document entity with owner field for authorization tests
 */
export class TestDocument {
  static options = { ownerField: 'creatorId' }
  static type = 'memory'

  public _id: string
  public title?: string
  public content?: string
  public creatorId?: string
  public organizationId?: string

  constructor(data?: Partial<TestDocument>) {
    Object.assign(
      this,
      {
        _id: new ObjectId().toString(),
      },
      data,
    )
  }
}

/**
 * Creates an authentication fixture with default values
 * @param overrides - Optional properties to override defaults
 */
export const createAuthFixture = (overrides?: Partial<IntrospectionResponse>): IntrospectionResponse => ({
  active: true,
  sub: 'user123',
  client_id: 'test-client',
  roles: [],
  ...overrides,
})

/**
 * Creates a role fixture
 * @param role - The role to assign
 * @param orgId - Optional organization ID
 */
export const createRoleFixture = (role: Role, orgId?: string): UserRole => ({
  role,
  ...(orgId ? { organizationId: orgId } : {}),
})

/**
 * Creates an auth fixture with specific roles
 */
export const createAuthWithRoles = (roles: UserRole[]): IntrospectionResponse => createAuthFixture({ roles })

// Common auth scenarios
export const regularUserAuth = createAuthFixture({
  roles: [createRoleFixture(Role.User)],
})

export const adminUserAuth = createAuthFixture({
  roles: [createRoleFixture(Role.Admin, 'org123')],
})

export const systemAdminAuth = createAuthFixture({
  roles: [createRoleFixture(Role.SystemAdmin)],
})

export const orgOwnerAuth = (orgId: string) =>
  createAuthFixture({
    roles: [createRoleFixture(Role.Owner, orgId)],
  })

/**
 * Creates a policy handler for testing
 */
export const createPolicyHandler = (action = Action.Read, dto = TestUser, options = {}) => ({
  action,
  dto,
  options,
  type: undefined,
})
