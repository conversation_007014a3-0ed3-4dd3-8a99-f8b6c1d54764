import { createMongoAbility } from '@casl/ability'
import { createMock } from '@golevelup/ts-jest'

import { Action } from '../action.enum'
import { ForbiddenFieldsException } from '../auth.exceptions'
import { checkFieldsPermissions, forbiddenFieldsOf } from '../utils'
import { TestUser, createPolicyHandler } from './fixtures'

import type { GqlExecutionContext } from '@nestjs/graphql'

// Simplify the mock to just return the needed test data
jest.mock('graphql-fields', () => jest.fn().mockImplementation(() => global.__FIELD_STRUCTURE__ || {}))

// Simplified mock for GraphQL context
jest.mock('@nestjs/graphql', () => ({
  GqlExecutionContext: {
    create: jest.fn().mockReturnValue({
      getContext: jest.fn(),
      getInfo: jest.fn(),
      getArgs: jest.fn(),
      getType: jest.fn().mockReturnValue('graphql'),
    }),
  },
}))

describe('Complex Field Permissions', () => {
  beforeEach(() => {
    global.__FIELD_STRUCTURE__ = {}
  })

  describe('Deeply nested fields', () => {
    it('should identify forbidden fields at any nesting level', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'profile.publicInfo', 'profile.preferences.theme'],
        },
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['profile.privateInfo', 'profile.preferences.notifications'],
          inverted: true,
        },
      ])

      const user = new TestUser()

      const forbiddenFields = forbiddenFieldsOf(ability, Action.Read, user, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      expect(forbiddenFields).toContain('profile.privateInfo')
      expect(forbiddenFields).toContain('profile.preferences.notifications')
      expect(forbiddenFields).not.toContain('profile.publicInfo')
      expect(forbiddenFields).not.toContain('profile.preferences.theme')
    })

    it('should detect and block access to deep nested forbidden fields', () => {
      global.__FIELD_STRUCTURE__ = {
        name: true,
        profile: {
          publicInfo: true,
          privateInfo: true,
          preferences: {
            theme: true,
            notifications: true,
          },
        },
      }

      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name', 'profile.publicInfo', 'profile.preferences.theme'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()

      expect(() => {
        checkFieldsPermissions(handler, ability, gqlContext)
      }).toThrow(ForbiddenFieldsException)
    })

    it('should detect and block access to deep nested forbidden fields with real subject detection', () => {
      global.__FIELD_STRUCTURE__ = {
        name: true,
        profile: {
          publicInfo: true,
          privateInfo: true,
          preferences: {
            theme: true,
            notifications: true,
          },
        },
      }

      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name', 'profile.publicInfo', 'profile.preferences.theme'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()

      expect(() => {
        checkFieldsPermissions(handler, ability, gqlContext)
      }).toThrow(ForbiddenFieldsException)
    })

    it('should detect and block access to deep nested forbidden fields with error message validation', () => {
      global.__FIELD_STRUCTURE__ = {
        name: true,
        profile: {
          publicInfo: true,
          privateInfo: true,
          preferences: {
            theme: true,
            notifications: true,
          },
        },
      }

      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name', 'profile.publicInfo', 'profile.preferences.theme'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()

      try {
        checkFieldsPermissions(handler, ability, gqlContext)
        fail('Expected ForbiddenFieldsException to be thrown')
      } catch (error) {
        expect(error).toBeInstanceOf(ForbiddenFieldsException)
        expect(error.message).toContain('profile.privateInfo')
        expect(error.message).toContain('profile.preferences.notifications')
      }
    })

    it('should distinguish between no field rules vs restrictive field rules', () => {
      global.__FIELD_STRUCTURE__ = {
        name: true,
        email: true,
        sensitiveField: true,
      }

      const handler = createPolicyHandler(Action.Read, TestUser)

      const abilityWithoutFields = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
        },
      ])

      const abilityWithRestrictiveFields = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()

      const graphqlFields = require('graphql-fields')
      const originalMock = graphqlFields.mockImplementation
      graphqlFields.mockImplementation(() => ({
        name: true,
        email: true,
        sensitiveField: true,
      }))

      expect(() => {
        checkFieldsPermissions(handler, abilityWithoutFields, gqlContext)
      }).not.toThrow()

      expect(() => {
        checkFieldsPermissions(handler, abilityWithRestrictiveFields, gqlContext)
      }).toThrow(ForbiddenFieldsException)

      graphqlFields.mockImplementation(originalMock)
    })
  })

  describe('Conditional field permissions', () => {
    it('should apply field permissions based on resource conditions', () => {
      const ability = createMongoAbility([
        // Admin users can see sensitive fields in active documents
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'email', 'sensitiveField'],
          conditions: { status: 'active', role: 'admin' },
        },
        // Regular users only see basic fields
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'email'],
          conditions: { status: 'active' },
        },
        // Explicitly deny sensitiveField for non-admins
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['sensitiveField'],
          inverted: true,
          conditions: { role: { $ne: 'admin' } },
        },
        // Explicitly deny all fields for inactive users
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'email', 'sensitiveField'],
          inverted: true,
          conditions: { status: 'inactive' },
        },
      ])

      const adminUser = new TestUser()
      Object.assign(adminUser, { status: 'active', role: 'admin' })

      const regularUser = new TestUser()
      Object.assign(regularUser, { status: 'active', role: 'user' })

      const inactiveUser = new TestUser()
      Object.assign(inactiveUser, { status: 'inactive', role: 'admin' })

      const adminFields = forbiddenFieldsOf(ability, Action.Read, adminUser, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      const regularFields = forbiddenFieldsOf(ability, Action.Read, regularUser, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      const inactiveFields = forbiddenFieldsOf(ability, Action.Read, inactiveUser, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      expect(adminFields).not.toContain('sensitiveField')
      expect(regularFields).toContain('sensitiveField')
      expect(inactiveFields).toEqual(['name', 'email', 'sensitiveField'])
    })
  })

  describe('Inverted vs Regular Field Rules', () => {
    it('should properly handle the difference between inverted and regular field permissions', () => {
      const regularFieldsAbility = createMongoAbility([
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'email'],
        },
      ])

      const invertedFieldsAbility = createMongoAbility([
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['sensitiveField'],
          inverted: true,
        },
      ])

      const combinedAbility = createMongoAbility([
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'email'],
        },
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['sensitiveField'],
          inverted: true,
        },
      ])

      const user = new TestUser()

      const regularForbidden = forbiddenFieldsOf(regularFieldsAbility, Action.Read, user, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      const invertedForbidden = forbiddenFieldsOf(invertedFieldsAbility, Action.Read, user, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      const combinedForbidden = forbiddenFieldsOf(combinedAbility, Action.Read, user, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      // Regular field rules only allow listed fields
      expect(regularForbidden).not.toContain('name')
      expect(regularForbidden).not.toContain('email')
      expect(regularForbidden).toEqual([])

      // Inverted field rules explicitly forbid listed fields
      expect(invertedForbidden).toContain('sensitiveField')

      // Combined rules should match the union of permissions
      expect(combinedForbidden).not.toContain('name')
      expect(combinedForbidden).not.toContain('email')
      expect(combinedForbidden).toContain('sensitiveField')
    })
  })

  afterEach(() => {
    jest.restoreAllMocks()
    jest.resetModules()
  })

  afterAll(() => {
    delete global.__FIELD_STRUCTURE__
  })
})
