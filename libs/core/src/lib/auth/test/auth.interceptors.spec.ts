import { createMock } from '@golevelup/ts-jest'
import { ExecutionContext, type PlainLiteralObject } from '@nestjs/common'
import { of } from 'rxjs'

import { ClassSerializerAuthInterceptor } from '../auth.interceptors'
import { Role, systemRoles, type UserRole } from '../auth.interfaces'
import { createAuthFixture, createRoleFixture } from './fixtures'
import { Private, Hidden } from '../auth.decorators'

// Only mock the utility function to get auth from context
jest.mock('../utils', () => ({
  getAuthFromContext: jest.fn(),
  isClass: jest.fn().mockImplementation((obj) => !!obj?.constructor && obj.constructor !== Object),
}))

// Extend the interceptor to access protected methods for testing
class TestSerializerInterceptor extends ClassSerializerAuthInterceptor {
  public testParseGroupsForSource(source: PlainLiteralObject, userId: string, orgRoles?: UserRole[]) {
    return this.parseGroupsForSource(source, userId, orgRoles)
  }
}

// Create a test class that mirrors actual system behavior
class TestUser {
  // Public by default, always visible
  id = '123'

  name = 'Test User'

  @Private(systemRoles)
  email = '<EMAIL>'

  @Private([Role.Admin, Role.SystemAdmin])
  internalId = 'internal123'

  @Private([Role.Owner, Role.SystemAdmin])
  ownerField = 'ownerData'

  @Private([Role.Editor, Role.Admin, Role.SystemAdmin])
  editorField = 'editorData'

  @Private([Role.Support, Role.SystemAdmin])
  supportField = 'supportData'

  @Hidden()
  password = 'secret'

  organizationId = 'org123'

  creatorId?: string

  static options = {
    ownerField: 'creatorId',
  }
}

describe('ClassSerializerAuthInterceptor', () => {
  let interceptor: TestSerializerInterceptor

  beforeEach(() => {
    jest.clearAllMocks()

    const mockReflector = {
      get: jest.fn(),
      getAllAndMerge: jest.fn(),
      getAllAndOverride: jest.fn(),
    }

    interceptor = new TestSerializerInterceptor(mockReflector)

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    jest.spyOn(interceptor as any, 'getContextOptions').mockReturnValue({})
  })

  it('should be defined', () => {
    expect(interceptor).toBeDefined()
  })

  describe('role-based serialization', () => {
    it('should apply organization roles when organizationId matches', () => {
      const testUser = new TestUser()
      const auth = createAuthFixture({
        sub: 'different-user',
        roles: [createRoleFixture(Role.Admin, 'org123')],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const groups = interceptor.testParseGroupsForSource(testUser, auth.sub, auth.roles)
      expect(groups).toContain(Role.Admin)
      expect(groups).not.toContain('self')
      expect(groups).not.toContain('owner')

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.name).toBe('Test User')
        expect(result.id).toBe('123')
        expect(result.email).toBeUndefined()
        expect(result.internalId).toBe('internal123')
        expect(result.editorField).toBe('editorData')
        expect(result.ownerField).toBeUndefined()
        expect(result.supportField).toBeUndefined()
        expect(result.password).toBeUndefined()
      })
    })

    it('should add "self" group when accessing own User entity', () => {
      class User {
        id = 'user123'
        name = 'Test User'
        static options = {}
      }

      const userEntity = new User()

      const auth = createAuthFixture({
        sub: 'user123',
        roles: [createRoleFixture(Role.User)],
      })

      const groups = interceptor.testParseGroupsForSource(userEntity, auth.sub, auth.roles)
      expect(groups).toContain('self')
    })

    it('should add "owner" group when user owns an entity', () => {
      const ownedEntity = new TestUser()
      ownedEntity.creatorId = 'user123'

      const auth = createAuthFixture({
        sub: 'user123',
        roles: [createRoleFixture(Role.User)],
      })

      const groups = interceptor.testParseGroupsForSource(ownedEntity, auth.sub, auth.roles)
      expect(groups).toContain('owner')
    })

    it('should expose only public fields when accessing other users', () => {
      const testUser = new TestUser()

      const auth = createAuthFixture({
        sub: 'different-user',
        roles: [createRoleFixture(Role.User)],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.name).toBe('Test User')
        expect(result.id).toBe('123')
        expect(result.email).toBeUndefined()
        expect(result.internalId).toBeUndefined()
        expect(result.ownerField).toBeUndefined()
        expect(result.password).toBeUndefined()
      })
    })

    it('should expose admin-only fields to admins', () => {
      const testUser = new TestUser()

      const auth = createAuthFixture({
        sub: 'admin-user',
        roles: [createRoleFixture(Role.Admin, 'org123')],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.name).toBe('Test User')
        expect(result.id).toBe('123')
        expect(result.email).toBeUndefined()
        expect(result.internalId).toBe('internal123')
        expect(result.editorField).toBe('editorData')
        expect(result.ownerField).toBeUndefined()
        expect(result.supportField).toBeUndefined()
      })
    })

    it('should handle arrays of objects with system permissions', () => {
      const users = [new TestUser(), Object.assign(new TestUser(), { id: '456' })]

      const auth = createAuthFixture({
        sub: 'support-user',
        roles: [createRoleFixture(Role.Support)],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(users)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(Array.isArray(result)).toBe(true)
        expect(result.length).toBe(2)

        expect(result[0].email).toBe('<EMAIL>')
        expect(result[1].email).toBe('<EMAIL>')
        expect(result[0].supportField).toBe('supportData')
        expect(result[1].supportField).toBe('supportData')
        expect(result[0].internalId).toBeUndefined()
        expect(result[1].internalId).toBeUndefined()
      })
    })

    it('should respect Role.Owner as an organization role', () => {
      const resource = Object.assign(new TestUser(), {
        id: 'resource123',
        organizationId: 'org456',
      })

      const auth = createAuthFixture({
        sub: 'owner-user',
        roles: [createRoleFixture(Role.Owner, 'org456')],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const groups = interceptor.testParseGroupsForSource(resource, auth.sub, auth.roles)
      expect(groups).toContain(Role.Owner)

      const next = { handle: jest.fn().mockReturnValue(of(resource)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.ownerField).toBe('ownerData')
        expect(result.email).toBeUndefined()
        expect(result.supportField).toBeUndefined()
      })
    })

    it('should handle pagination results with nodes array', () => {
      const paginatedResult = {
        nodes: [new TestUser(), Object.assign(new TestUser(), { id: '456' })],
        totalCount: 2,
        hasNextPage: false,
      }

      const auth = createAuthFixture({
        sub: 'system-user',
        roles: [createRoleFixture(Role.SystemAdmin)],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(paginatedResult)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.nodes.length).toBe(2)
        expect(result.totalCount).toBe(2)

        expect(result.nodes[0].email).toBe('<EMAIL>')
        expect(result.nodes[1].email).toBe('<EMAIL>')
        expect(result.nodes[0].internalId).toBe('internal123')
        expect(result.nodes[0].ownerField).toBe('ownerData')
      })
    })

    it('should expose all fields to system admin except hidden ones', () => {
      const testUser = new TestUser()

      const auth = createAuthFixture({
        sub: 'system-admin',
        roles: [createRoleFixture(Role.SystemAdmin)],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.name).toBe('Test User')
        expect(result.id).toBe('123')
        expect(result.email).toBe('<EMAIL>')
        expect(result.internalId).toBe('internal123')
        expect(result.ownerField).toBe('ownerData')
        expect(result.editorField).toBe('editorData')
        expect(result.supportField).toBe('supportData')
        expect(result.password).toBeUndefined()
      })
    })

    it('should expose correct fields to Support role', () => {
      const testUser = new TestUser()

      const auth = createAuthFixture({
        sub: 'support-user',
        roles: [createRoleFixture(Role.Support)],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.email).toBe('<EMAIL>')
        expect(result.supportField).toBe('supportData')
        expect(result.internalId).toBeUndefined()
        expect(result.ownerField).toBeUndefined()
        expect(result.editorField).toBeUndefined()
      })
    })

    it('should expose correct fields to Editor role', () => {
      const testUser = new TestUser()

      const auth = createAuthFixture({
        sub: 'editor-user',
        roles: [createRoleFixture(Role.Editor, 'org123')],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.editorField).toBe('editorData')
        expect(result.email).toBeUndefined()
        expect(result.internalId).toBeUndefined()
        expect(result.ownerField).toBeUndefined()
        expect(result.supportField).toBeUndefined()
      })
    })

    it('should handle organization-specific roles correctly', () => {
      const testUser = Object.assign(new TestUser(), {
        organizationId: 'org123',
      })

      const auth = createAuthFixture({
        sub: 'org-admin',
        roles: [createRoleFixture(Role.Admin, 'org123')],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const groups = interceptor.testParseGroupsForSource(testUser, auth.sub, auth.roles)
      expect(groups).toContain(Role.Admin)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.internalId).toBe('internal123')
        expect(result.editorField).toBe('editorData')

        expect(result.email).toBeUndefined()
        expect(result.ownerField).toBeUndefined()
        expect(result.supportField).toBeUndefined()
      })
    })

    it('should not grant access across organizations', () => {
      const testUser = Object.assign(new TestUser(), {
        organizationId: 'org456',
      })

      const auth = createAuthFixture({
        sub: 'org-admin',
        roles: [createRoleFixture(Role.Admin, 'org123')],
      })

      const context = createMock<ExecutionContext>()
      jest.spyOn(require('../utils'), 'getAuthFromContext').mockReturnValue(auth)

      const groups = interceptor.testParseGroupsForSource(testUser, auth.sub, auth.roles)
      expect(groups).not.toContain(Role.Admin)

      const next = { handle: jest.fn().mockReturnValue(of(testUser)) }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const resultPromise = new Promise<any>((resolve) => {
        interceptor.intercept(context, next).subscribe((value) => {
          resolve(value)
        })
      })

      return resultPromise.then((result) => {
        expect(result.internalId).toBeUndefined()
        expect(result.email).toBeUndefined()
        expect(result.ownerField).toBeUndefined()
        expect(result.editorField).toBeUndefined()
        expect(result.supportField).toBeUndefined()
        expect(result.id).toBe('123')
        expect(result.name).toBe('Test User')
      })
    })
  })
})
