import { createMongoAbility } from '@casl/ability'
import { createMock } from '@golevelup/ts-jest'
import { GqlExecutionContext } from '@nestjs/graphql'

import { Action } from '../action.enum'
import { ForbiddenFieldsException } from '../auth.exceptions'
import { can, checkFieldsPermissions, flatten, forbiddenFieldsOf, getAuthFromContext } from '../utils'
import { TestUser, createPolicyHandler } from './fixtures'

import type { ExecutionContext } from '@nestjs/common'

// Simplify the GraphQL mock
jest.mock('@nestjs/graphql', () => ({
  GqlExecutionContext: {
    create: jest.fn().mockReturnValue({
      getContext: jest.fn(),
      getInfo: jest.fn().mockReturnValue({
        fieldNodes: [],
        parentType: { name: 'Query' },
      }),
      getArgs: jest.fn().mockReturnValue({}),
    }),
  },
}))

// Simplify graphql-fields mock
jest.mock('graphql-fields', () => jest.fn().mockReturnValue({ name: true, email: true, privateField: true }))

describe('Auth Utilities', () => {
  describe('getAuthFromContext', () => {
    it('should extract auth from http context', () => {
      const context = createMock<ExecutionContext>()
      const auth = { sub: 'user123', active: true }

      context.switchToHttp().getRequest.mockReturnValue({ auth })
      context.getType.mockReturnValue('http')

      expect(getAuthFromContext(context)).toBe(auth)
    })

    it('should extract auth from graphql context', () => {
      const context = createMock<ExecutionContext>()
      const auth = { sub: 'user123', active: true }

      context.getType.mockReturnValue('graphql')
      GqlExecutionContext.create = jest.fn().mockReturnValue({
        getContext: jest.fn().mockReturnValue({ auth }),
        getInfo: jest.fn(),
        getArgs: jest.fn(),
      })

      expect(getAuthFromContext(context)).toBe(auth)
    })

    it('should return undefined if auth is not in context', () => {
      const context = createMock<ExecutionContext>()
      context.switchToHttp().getRequest.mockReturnValue({})
      context.getType.mockReturnValue('http')

      expect(getAuthFromContext(context)).toBeUndefined()
    })

    it('should handle rpc context type', () => {
      const context = createMock<ExecutionContext>()
      context.getType.mockReturnValue('rpc')

      expect(getAuthFromContext(context)).toBeUndefined()
    })
  })

  describe('can', () => {
    it('should check if action is allowed', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
        },
      ])
      const user = new TestUser()

      expect(can(ability, Action.Read, user)).toBe(true)
      expect(can(ability, Action.Update, user)).toBe(false)
    })

    it('should warn when no rules found in development', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'

      const spy = jest.spyOn(console, 'warn').mockImplementation(() => {
        // Do nothing
      })

      const ability = createMongoAbility([])
      const user = new TestUser()

      can(ability, 'nonExistentAction', user)
      expect(spy).toHaveBeenCalled()

      process.env.NODE_ENV = originalEnv
      spy.mockRestore()
    })
  })

  describe('forbiddenFieldsOf', () => {
    it('should identify fields user cannot access', () => {
      const ability = createMongoAbility([{ action: Action.Read, subject: 'TestUser', fields: ['name', 'email'] }])

      const user = new TestUser()

      const forbiddenFields = forbiddenFieldsOf(ability, Action.Read, user, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      expect(forbiddenFields).not.toContain('name')
      expect(forbiddenFields).not.toContain('email')
      expect(forbiddenFields).toEqual([])
    })

    it('should handle rules with conditions', () => {
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['name', 'email'],
          conditions: { _id: 'user123' },
          inverted: false,
        },
        {
          action: Action.Read,
          subject: 'TestUser',
          fields: ['privateField'],
          inverted: true,
        },
      ])

      const user = new TestUser({ _id: 'user123' })

      const forbiddenFields = forbiddenFieldsOf(ability, Action.Read, user, {
        fieldsFrom: (rule) => rule.fields || [],
      })

      expect(forbiddenFields).toContain('privateField')
      expect(forbiddenFields).not.toContain('name')
      expect(forbiddenFields).not.toContain('email')
    })
  })

  describe('flatten', () => {
    it('should flatten nested objects', () => {
      const nestedObject = {
        name: 'Test',
        profile: {
          age: 30,
          address: {
            city: 'TestCity',
            country: 'TestCountry',
          },
        },
        tags: ['one', 'two'],
      }

      const flattened = flatten(nestedObject)

      expect(flattened).toBeDefined()
      expect(flattened['name']).toBe('Test')
      expect(flattened['profile.age']).toBe(30)
      expect(flattened['profile.address.city']).toBe('TestCity')
      expect(flattened['profile.address.country']).toBe('TestCountry')
      expect(flattened['tags.0']).toBe('one')
      expect(flattened['tags.1']).toBe('two')
    })

    it('should respect maxDepth option', () => {
      const nestedObject = {
        profile: {
          address: {
            city: 'TestCity',
          },
        },
      }

      const flattened = flatten(nestedObject, { maxDepth: 1 })
      expect(flattened['profile']).toBeDefined()
      expect(flattened['profile.address']).toBeUndefined()
    })

    it('should handle arrays appropriately', () => {
      const objectWithArrays = {
        items: [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' },
        ],
      }

      const flattened = flatten(objectWithArrays)

      expect(flattened['items.0.id']).toBe(1)
      expect(flattened['items.0.name']).toBe('Item 1')
      expect(flattened['items.1.id']).toBe(2)
      expect(flattened['items.1.name']).toBe('Item 2')
    })

    it('should handle buffers correctly', () => {
      const buffer = Buffer.from('test')
      const objectWithBuffer = {
        data: buffer,
      }

      const flattened = flatten(objectWithBuffer)
      expect(flattened['data']).toBe(buffer)
    })
  })

  describe('checkFieldsPermissions', () => {
    it('should throw exception when requesting forbidden fields', () => {
      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name'],
        },
      ])
      const gqlContext = createMock<GqlExecutionContext>()

      try {
        checkFieldsPermissions(handler, ability, gqlContext)
        fail('Expected ForbiddenFieldsException to be thrown')
      } catch (err) {
        expect(err).toBeInstanceOf(ForbiddenFieldsException)
      }
    })

    it('should allow access to permitted fields', () => {
      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name', 'email', 'privateField'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()

      expect(() => {
        checkFieldsPermissions(handler, ability, gqlContext)
      }).not.toThrow()
    })

    it('should handle non-read operations with object options', () => {
      const handler = {
        ...createPolicyHandler(Action.Update, TestUser),
        options: { object: 'input' },
      }
      const ability = createMongoAbility([
        {
          action: Action.Update,
          subject: TestUser,
          fields: ['name'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()
      gqlContext.getArgs.mockReturnValue({
        input: { name: 'Test', email: '<EMAIL>' },
      })

      expect(() => {
        checkFieldsPermissions(handler, ability, gqlContext)
      }).toThrow()
    })
  })
})
