import { createMongoAbility } from '@casl/ability'
import { createMock } from '@golevelup/ts-jest'

import { Action } from '../action.enum'
import { ForbiddenFieldsException } from '../auth.exceptions'
import { Role } from '../auth.interfaces'
import { can, checkFieldsPermissions } from '../utils'
import { TestUser, createAuthFixture, createPolicyHandler } from './fixtures'

import type { GqlExecutionContext } from '@nestjs/graphql'

// Mock graphql-fields for field access testing
jest.mock('graphql-fields', () =>
  jest.fn().mockImplementation((_info, _opts, _options) => {
    return global.__REQUESTED_FIELDS__ || { name: true }
  }),
)

describe('Authorization Security Edge Cases', () => {
  describe('Role validation', () => {
    it('should recognize valid and invalid roles', () => {
      const validRole = Role.User
      const invalidRole = 'NonExistentRole'

      const validRoleExists = Object.values(Role).includes(validRole)
      const invalidRoleExists = Object.values(Role).includes(invalidRole as Role)

      expect(validRoleExists).toBe(true)
      expect(invalidRoleExists).toBe(false)

      const validAuth = createAuthFixture({
        roles: [{ role: Role.User }],
      })

      const invalidAuth = createAuthFixture({
        roles: [{ role: 'NonExistentRole' as Role }],
      })

      const hasValidRole = validAuth.roles?.some((r) => Object.values(Role).includes(r.role as Role))
      const hasInvalidRole = invalidAuth.roles?.some((r) => Object.values(Role).includes(r.role as Role))

      expect(hasValidRole).toBe(true)
      expect(hasInvalidRole).toBe(false)
    })
  })

  describe('Field permission security', () => {
    it('should block access to nested restricted fields', () => {
      global.__REQUESTED_FIELDS__ = {
        name: true,
        profile: {
          sensitiveData: true, // This field is restricted
        },
      }

      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name', 'profile'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()

      expect(() => {
        checkFieldsPermissions(handler, ability, gqlContext)
      }).toThrow(ForbiddenFieldsException)
    })

    it('should reject access when ability cannot be determined', () => {
      global.__REQUESTED_FIELDS__ = {
        name: true,
        email: true,
        sensitiveField: true,
      }

      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        {
          action: Action.Read,
          subject: TestUser,
          fields: ['name', 'email'],
        },
      ])

      const gqlContext = createMock<GqlExecutionContext>()
      gqlContext.getType.mockReturnValue('http')

      expect(() => {
        checkFieldsPermissions(handler, ability, gqlContext)
      }).toThrow(ForbiddenFieldsException)

      try {
        checkFieldsPermissions(handler, ability, gqlContext)
      } catch (error) {
        expect(error.message).toContain('sensitiveField')
      }
    })
  })

  describe('Permission hierarchies', () => {
    it('should respect hierarchical boundaries', () => {
      const orgId = 'org123'
      const otherOrgId = 'org456'

      const _auth = createAuthFixture({
        roles: [{ role: Role.Admin, organizationId: orgId }],
      })

      const ability = createMongoAbility([
        { action: Action.Manage, subject: 'TestUser', conditions: { organizationId: orgId } },
      ])

      // User should be able to access resources in their own org
      const userInOrg = new TestUser({
        name: 'Test',
        email: '<EMAIL>',
        organizationId: orgId,
      })
      expect(can(ability, Action.Update, userInOrg)).toBe(true)

      // But not in other orgs
      const userInOtherOrg = new TestUser({
        name: 'Test',
        email: '<EMAIL>',
        organizationId: otherOrgId,
      })
      expect(can(ability, Action.Update, userInOtherOrg)).toBe(false)
    })
  })

  afterAll(() => {
    delete global.__REQUESTED_FIELDS__
  })
})
