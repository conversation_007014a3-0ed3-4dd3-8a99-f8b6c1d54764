import { ForbiddenError } from '@nestjs/apollo'

import { Role } from '../auth.interfaces'
import { checkRole } from '../auth.middleware'
import { createAuthFixture, createRoleFixture } from './fixtures'

import type { GraphQLResolveInfo } from 'graphql'

describe('GraphQL Field Middleware', () => {
  describe('checkRole middleware', () => {
    /**
     * Creates a testing context with proper GraphQL structure
     */
    const createContext = (extensions, source = {}, auth = null) => {
      const info = {
        fieldName: 'testField',
        parentType: {
          name: 'User',
          getFields: () => ({
            testField: { extensions },
          }),
        },
        fieldNodes: [],
        returnType: {},
        path: { key: 'testField' },
        schema: {},
        fragments: {},
        rootValue: {},
        operation: {
          kind: 'OperationDefinition',
          operation: 'query',
          selectionSet: { kind: 'SelectionSet', selections: [] },
        },
        variableValues: {},
      } as unknown as GraphQLResolveInfo

      return {
        info,
        source,
        context: { auth },
        args: {},
      }
    }

    const next = jest.fn().mockResolvedValue('result')

    beforeEach(() => {
      next.mockClear()
    })

    it('should throw error when no roles are defined', async () => {
      const ctx = createContext({})

      try {
        await checkRole(ctx, next)
        fail('Expected checkRole to throw an error but it did not')
      } catch (error) {
        expect(error).toBeInstanceOf(ForbiddenError)
      }
    })

    it('should allow access when user is accessing their own data', async () => {
      const auth = createAuthFixture()
      const ctx = createContext({ roles: [Role.Admin], hideFromSelf: false }, { _id: auth.sub }, auth)

      await expect(checkRole(ctx, next)).resolves.toBe('result')
      expect(next).toHaveBeenCalled()
    })

    it('should block access to own data when hideFromSelf is true', async () => {
      const auth = createAuthFixture()
      const ctx = createContext({ roles: [Role.Admin], hideFromSelf: true }, { _id: auth.sub }, auth)

      const result = await checkRole(ctx, next)
      expect(result).toBeNull()
      expect(next).not.toHaveBeenCalled()
    })

    it('should allow access when user is the owner of the resource', async () => {
      const auth = createAuthFixture()
      const ctx = createContext(
        { roles: [Role.Admin], hideFromOwner: false },
        {
          _id: 'test123',
          userId: auth.sub,
          constructor: { options: { ownerField: 'userId' } },
        },
        auth,
      )

      await expect(checkRole(ctx, next)).resolves.toBe('result')
      expect(next).toHaveBeenCalled()
    })

    it('should block access for owner when hideFromOwner is true', async () => {
      const auth = createAuthFixture()
      const ctx = createContext(
        { roles: [Role.Admin], hideFromOwner: true },
        {
          _id: 'test123',
          userId: auth.sub,
          constructor: { options: { ownerField: 'userId' } },
        },
        auth,
      )

      const result = await checkRole(ctx, next)
      expect(result).toBeNull()
      expect(next).not.toHaveBeenCalled()
    })

    it('should allow access when user has required role', async () => {
      const auth = createAuthFixture({
        roles: [{ role: Role.Admin }],
      })

      const ctx = createContext({ roles: [Role.Admin] }, { _id: 'different-id' }, auth)

      await expect(checkRole(ctx, next)).resolves.toBe('result')
      expect(next).toHaveBeenCalled()
    })

    it('should allow access when user has org-specific role and is accessing that org', async () => {
      const orgId = 'org123'
      const auth = createAuthFixture({
        roles: [createRoleFixture(Role.Admin, orgId)],
      })

      const ctx = createContext({ roles: [Role.Admin] }, { _id: orgId, organizationId: orgId }, auth)

      await expect(checkRole(ctx, next)).resolves.toBe('result')
      expect(next).toHaveBeenCalled()
    })

    it('should block access when user lacks required role', async () => {
      const auth = createAuthFixture({
        roles: [{ role: Role.User }],
      })

      const ctx = createContext({ roles: [Role.Admin] }, { _id: 'different-id' }, auth)

      const result = await checkRole(ctx, next)
      expect(result).toBeNull()
      expect(next).not.toHaveBeenCalled()
    })

    it('should allow access for SystemAdmin regardless of required roles', async () => {
      const auth = createAuthFixture({
        roles: [{ role: Role.SystemAdmin }],
      })

      const ctx = createContext({ roles: [Role.Admin] }, { _id: 'different-id' }, auth)

      await expect(checkRole(ctx, next)).resolves.toBe('result')
      expect(next).toHaveBeenCalled()
    })

    it('should return null when auth is not present', async () => {
      const ctx = createContext({ roles: [Role.Admin] }, { _id: 'some-id', id: 'some-id' }, null)

      const result = await checkRole(ctx, next)
      expect(result).toBeNull()
      expect(next).not.toHaveBeenCalled()
    })
  })
})
