import { Test } from '@nestjs/testing'
import { ObjectId } from 'mongodb'

import { CacheService } from '../../utils/cache'
import { BaseAbilityFactory } from '../ability.factory'
import { Action } from '../action.enum'
import { Role } from '../auth.interfaces'
import {
  createAuthFixture,
  createAuthWithRoles,
  createRoleFixture,
  systemAdminAuth,
  TestDocument,
  TestOrganization,
  TestUser,
} from './fixtures'

/**
 * Test implementation of AbilityFactory with predictable rules
 */
class TestAbilityFactory extends BaseAbilityFactory {
  async rules(auth, can, _cannot) {
    // Anonymous users can only read users
    can(Action.Read, TestUser)

    if (!auth) return

    const rolesMap = this.buildRolesMap(auth)

    // Anyone can read their own user
    can(Action.Update, TestUser, { _id: auth.sub })

    // Owner can manage documents they created
    can(Action.Manage, TestDocument, { [TestDocument.options.ownerField]: auth.sub })

    // Admin can manage users in their organization
    const orgsAsAdmin = this.orgIdsForRoles(['Admin'], rolesMap)
    can(Action.Update, TestUser, { organizationId: { $in: orgsAsAdmin } })

    // Organization owners can delete organizations
    const orgsAsOwner = this.orgIdsForRoles(['Owner'], rolesMap)
    can(Action.Delete, TestOrganization, { _id: { $in: orgsAsOwner } })

    // System admins can do everything
    if (this.hasRoles(['SystemAdmin'], auth.roles)) {
      can(Action.Manage, 'all')
    }
  }
}

describe('AbilityFactory', () => {
  let abilityFactory: TestAbilityFactory

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        {
          provide: CacheService,
          useValue: {
            setValue: jest.fn(),
            getValue: jest.fn(),
            generateKey: jest.fn().mockImplementation((...args) => args.join(':')),
          },
        },
        {
          provide: 'ABILITY_CACHE_CONFIG',
          useValue: { enabled: true, useLocalCache: true },
        },
        TestAbilityFactory,
      ],
    }).compile()

    abilityFactory = moduleRef.get<TestAbilityFactory>(TestAbilityFactory)
  })

  describe('createForUser', () => {
    it('should create ability for anonymous user', async () => {
      const ability = await abilityFactory.createForUser(null)
      expect(ability.can(Action.Read, TestUser)).toBe(true)
      expect(ability.can(Action.Update, TestUser)).toBe(false)
    })

    it('should create ability for regular user', async () => {
      const auth = createAuthFixture()
      const ability = await abilityFactory.createForUser(auth)

      // User can update their own profile
      expect(ability.can(Action.Update, new TestUser({ _id: auth.sub }))).toBe(true)
      expect(ability.can(Action.Update, new TestUser())).toBe(false)

      // User can manage their own documents
      const ownDocument = new TestDocument()
      ownDocument.creatorId = auth.sub
      expect(ability.can(Action.Update, ownDocument)).toBe(true)
    })

    it('should create ability for admin user', async () => {
      const orgId = 'org123'
      const auth = createAuthWithRoles([createRoleFixture(Role.Admin, orgId)])
      const ability = await abilityFactory.createForUser(auth)

      // Admin can update users in their organization
      const orgUser = new TestUser()
      orgUser.organizationId = orgId
      expect(ability.can(Action.Update, orgUser)).toBe(true)

      // Admin cannot update users outside their organization
      const otherOrgUser = new TestUser()
      otherOrgUser.organizationId = 'different-org'
      expect(ability.can(Action.Update, otherOrgUser)).toBe(false)
    })

    it('should create ability for organization owner', async () => {
      const orgId = 'org456'
      const auth = createAuthWithRoles([createRoleFixture(Role.Owner, orgId)])
      const ability = await abilityFactory.createForUser(auth)

      // Owner can delete their organization
      expect(ability.can(Action.Delete, new TestOrganization({ _id: orgId }))).toBe(true)

      // Owner cannot delete other organizations
      expect(ability.can(Action.Delete, new TestOrganization())).toBe(false)
    })

    it('should create ability for system admin', async () => {
      const auth = systemAdminAuth
      const ability = await abilityFactory.createForUser(auth)

      // SystemAdmin can do anything
      expect(ability.can(Action.Manage, 'all')).toBe(true)
      expect(ability.can(Action.Delete, new TestOrganization())).toBe(true)
      expect(ability.can(Action.Delete, new TestUser())).toBe(true)
    })

    it('should use cached ability when available', async () => {
      const auth = createAuthFixture()
      const mockRules = [{ action: 'read', subject: 'TestUser' }]

      const getValue = jest.fn().mockResolvedValue(JSON.stringify(mockRules))
      const setValue = jest.fn().mockResolvedValue(undefined)

      // Replace the entire cacheService for this test
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(abilityFactory as any).cacheService = {
        getValue,
        setValue,
        generateKey: jest.fn().mockReturnValue('test-key'),
      }

      const ability = await abilityFactory.createForUser(auth)

      expect(getValue).toHaveBeenCalled()
      expect(JSON.stringify(ability.rules)).toEqual(JSON.stringify(mockRules))
    })

    it('should store new ability in cache', async () => {
      const auth = createAuthFixture()

      const getValue = jest.fn().mockResolvedValue(null)
      const setValue = jest.fn().mockResolvedValue(undefined)

      // Replace the entire cacheService
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(abilityFactory as any).cacheService = {
        getValue,
        setValue,
        generateKey: jest.fn().mockReturnValue('test-key'),
      }

      await abilityFactory.createForUser(auth)

      expect(setValue).toHaveBeenCalled()
    })
  })

  describe('utility functions', () => {
    it('should correctly build roles map', () => {
      const auth = createAuthFixture({
        roles: [
          createRoleFixture(Role.Admin, 'org1'),
          createRoleFixture(Role.User, 'org1'),
          createRoleFixture(Role.User, 'org2'),
          createRoleFixture(Role.SystemAdmin),
        ],
      })

      const rolesMap = abilityFactory.buildRolesMap(auth)

      expect(rolesMap[Role.Admin]).toEqual(['org1'])
      expect(rolesMap[Role.User]).toEqual(['org1', 'org2'])
      expect(rolesMap[Role.SystemAdmin]).toEqual([])
    })

    it('should correctly identify organization IDs for roles', () => {
      const rolesMap = {
        [Role.Admin]: ['org1', 'org2'],
        [Role.User]: ['org1', 'org3'],
      }

      const adminOrgs = abilityFactory.orgIdsForRoles(['Admin'], rolesMap)
      const userOrgs = abilityFactory.orgIdsForRoles(['User'], rolesMap)
      const combinedOrgs = abilityFactory.orgIdsForRoles(['Admin', 'User'], rolesMap)

      expect(adminOrgs).toEqual(['org1', 'org2'])
      expect(userOrgs).toEqual(['org1', 'org3'])
      expect(combinedOrgs).toEqual(['org1', 'org2', 'org1', 'org3'])
    })

    it('should correctly check if user has role', () => {
      const roles = [createRoleFixture(Role.Admin, 'org1'), createRoleFixture(Role.SystemAdmin)]

      expect(abilityFactory.hasRoles(['Admin'], roles)).toBe(true)
      expect(abilityFactory.hasRoles(['SystemAdmin'], roles)).toBe(true)
      expect(abilityFactory.hasRoles(['User'], roles)).toBe(false)
      expect(abilityFactory.hasRoles(['Admin', 'User'], roles)).toBe(true)
    })

    it('should handle different id types when building roles map', () => {
      const orgId = new ObjectId().toString()
      const auth = createAuthFixture({
        roles: [createRoleFixture(Role.Admin, orgId)],
      })

      // With string IDs
      const stringMap = abilityFactory.buildRolesMap(auth)
      expect(stringMap[Role.Admin][0]).toBe(orgId)

      // With ObjectId conversion
      const objectIdMap = abilityFactory.buildRolesMap(auth, ObjectId)
      expect(objectIdMap[Role.Admin][0]).toBeInstanceOf(ObjectId)
      expect(objectIdMap[Role.Admin][0].toString()).toBe(orgId)
    })
  })
})
