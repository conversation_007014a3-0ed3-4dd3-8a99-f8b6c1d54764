jest.mock(
  'openid-client',
  () => ({
    discovery: jest.fn(),
    tokenIntrospection: jest.fn(),
  }),
  { virtual: true },
)

jest.mock('../../core', () => ({
  getContextParams: jest.fn().mockReturnValue({}),
}))

import { createMongoAbility } from '@casl/ability'
import { createMock } from '@golevelup/ts-jest'

import { Action } from '../action.enum'
import { Role } from '../auth.interfaces'
import { checkRole } from '../auth.middleware'
import { can } from '../utils'
import { TestDocument, TestUser, createAuthFixture, createPolicyHandler, createRoleFixture } from './fixtures'
import { authGuardProvider } from '../auth.guards'

import type { ExecutionContext } from '@nestjs/common'

describe('Authorization Negative Test Cases', () => {
  describe('CRUD operations denial', () => {
    it('should deny create operation without proper permissions', () => {
      const ability = createMongoAbility([{ action: Action.Read, subject: 'TestDocument' }])
      const document = new TestDocument()

      expect(can(ability, Action.Create, document)).toBe(false)
      expect(can(ability, Action.Read, document)).toBe(true)
    })

    it('should properly handle empty rules', () => {
      const ability = createMongoAbility([])
      const document = new TestDocument()

      expect(can(ability, Action.Read, document)).toBe(false)
      expect(can(ability, Action.Create, document)).toBe(false)
      expect(can(ability, Action.Update, document)).toBe(false)
      expect(can(ability, Action.Delete, document)).toBe(false)
    })
  })

  describe('Role-based access denial', () => {
    it('should deny access to fields requiring higher role', async () => {
      const ctx = {
        info: {
          fieldName: 'adminField',
          parentType: {
            name: 'User',
            getFields: () => ({
              adminField: { extensions: { roles: [Role.Admin] } },
            }),
          },
        },
        source: { _id: 'user1' },
        context: {
          auth: createAuthFixture({
            roles: [createRoleFixture(Role.User)],
          }),
        },
        args: {},
      }

      const next = jest.fn()

      const result = await checkRole(ctx as never, next)
      expect(result).toBeNull()
      expect(next).not.toHaveBeenCalled()
    })

    it('should deny cross-organization access', async () => {
      const orgId = 'org123'
      const differentOrgId = 'org456'

      const ctx = {
        info: {
          fieldName: 'orgField',
          parentType: {
            name: 'Organization',
            getFields: () => ({
              orgField: { extensions: { roles: [Role.Admin] } },
            }),
          },
        },
        source: {
          _id: differentOrgId,
        },
        context: {
          auth: createAuthFixture({
            roles: [createRoleFixture(Role.Admin, orgId)],
          }),
        },
        args: {},
      }

      const next = jest.fn()

      const result = await checkRole(ctx as never, next)
      expect(result).toBeNull()
      expect(next).not.toHaveBeenCalled()
    })
  })

  describe('Policy guard denials', () => {
    it('should deny access when policy conditions are not met', () => {
      const handler = createPolicyHandler(Action.Read, TestUser)
      const ability = createMongoAbility([
        { action: Action.Read, subject: 'TestUser', conditions: { status: 'active' } },
      ])

      const context = createMock<ExecutionContext>()
      const params = { id: 'user123' }

      const getContextParams = require('../../core').getContextParams
      getContextParams.mockReturnValue(params)

      const result = authGuardProvider(handler, ability, context)

      expect(result).toBe(false)
    })
  })
})
