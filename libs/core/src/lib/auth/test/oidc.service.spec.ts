import { UnauthorizedException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Test } from '@nestjs/testing'
import { GraphQLError } from 'graphql'

import { CacheService } from '../../utils/cache'
import { OidcService } from '../oidc.service'

// Mock dependencies that Je<PERSON> can't handle
jest.mock('openid-client', () => ({
  discovery: jest.fn().mockResolvedValue({
    issuer: 'https://example.com',
    token_endpoint: 'https://example.com/token',
  }),
  tokenIntrospection: jest.fn().mockImplementation(async (config, token) => {
    if (token === 'valid-token') {
      return { active: true, sub: 'user123', client_id: 'test-client' }
    } else {
      return { active: false }
    }
  }),
}))

// Also mock the core module components
jest.mock('../../core', () => ({
  generateFingerPrint: jest.fn().mockReturnValue({
    ip: '127.0.0.1',
    userAgent: 'test-agent',
    sessionId: 'test-session',
  }),
}))

describe('OidcService', () => {
  let service: OidcService
  let configService: ConfigService
  let cacheService: CacheService

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        OidcService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key) => {
              const config = {
                OIDC_ISSUER: 'https://example.com',
                OIDC_CLIENT_ID: 'client-id',
                OIDC_CLIENT_SECRET: 'client-secret',
                TOKEN_INTROSPECTION_CACHE_SECONDS: 30,
              }
              return config[key]
            }),
          },
        },
        {
          provide: CacheService,
          useValue: {
            setValue: jest.fn(),
            getValue: jest.fn(),
            generateKey: jest.fn().mockImplementation((...args) => args.join(':')),
          },
        },
      ],
    }).compile()

    service = moduleRef.get<OidcService>(OidcService)
    configService = moduleRef.get<ConfigService>(ConfigService)
    cacheService = moduleRef.get<CacheService>(CacheService)
  })

  describe('clientConfig', () => {
    it('should get OIDC configuration', async () => {
      const config = await service.clientConfig()

      expect(configService.get).toHaveBeenCalledWith('OIDC_ISSUER')
      expect(configService.get).toHaveBeenCalledWith('OIDC_CLIENT_ID')
      expect(configService.get).toHaveBeenCalledWith('OIDC_CLIENT_SECRET')

      expect(config).toHaveProperty('issuer', 'https://example.com')
      expect(config).toHaveProperty('token_endpoint', 'https://example.com/token')
    })

    it('should reuse cached configuration', async () => {
      const { discovery } = require('openid-client')

      // First call should trigger discovery
      const firstConfig = await service.clientConfig()

      // Reset the mock to verify it doesn't get called again
      discovery.mockClear()

      // Second call should use cached config
      const secondConfig = await service.clientConfig()

      expect(discovery).not.toHaveBeenCalled()
      expect(secondConfig).toBe(firstConfig)
    })
  })

  describe('validate', () => {
    it('should return only fingerprint if no token', async () => {
      const req = { headers: {} }
      const result = await service.validate(req as never)

      expect(result.auth).toBeUndefined()
      expect(result.fingerprint).toBeDefined()
    })

    it('should validate valid token', async () => {
      const req = { headers: { authorization: 'Bearer valid-token' } }
      const introspectResult = { active: true, sub: 'user123', client_id: 'test-client' }

      // Mock cached introspection result
      jest.spyOn(cacheService, 'getValue').mockResolvedValue(JSON.stringify(introspectResult))

      const result = await service.validate(req as never)

      expect(result.auth).toEqual(introspectResult)
      expect(result.fingerprint).toBeDefined()
    })

    it('should call introspect endpoint for uncached token', async () => {
      const req = { headers: { authorization: 'Bearer valid-token' } }

      // Mock cache miss
      jest.spyOn(cacheService, 'getValue').mockResolvedValue(null)

      const result = await service.validate(req as never)

      expect(result.auth).toEqual({ active: true, sub: 'user123', client_id: 'test-client' })
      expect(result.fingerprint).toBeDefined()
      expect(cacheService.setValue).toHaveBeenCalled()
    })

    it('should throw unauthorized for invalid token (REST)', async () => {
      const req = { headers: { authorization: 'Bearer invalid-token' } }

      // Mock failed token validation
      jest.spyOn(cacheService, 'getValue').mockResolvedValue(null)

      await expect(service.validate(req as never)).rejects.toThrow(UnauthorizedException)
    })

    it('should throw GraphQL error for invalid token (GraphQL)', async () => {
      const req = { headers: { authorization: 'Bearer invalid-token' } }

      // Mock failed token validation
      jest.spyOn(cacheService, 'getValue').mockResolvedValue(null)

      await expect(service.validate(req as never, true)).rejects.toThrow(GraphQLError)
      try {
        await service.validate(req as never, true)
      } catch (error) {
        expect(error.extensions.code).toBe('UNAUTHENTICATED')
        expect(error.extensions.http.status).toBe(401)
      }
    })

    it('should handle token format correctly', async () => {
      // Test different token formats
      const reqs = [
        { headers: { authorization: 'Bearer valid-token' } },
        { headers: { authorization: 'bearer valid-token' } },
        { headers: { authorization: '  Bearer valid-token  ' } },
      ]

      // Mock the parseRequestForToken method explicitly
      const parseTokenSpy = jest
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .spyOn(service as any, 'parseRequestForToken')
        .mockImplementation((req: { headers?: { authorization?: string } }) => {
          const auth = req.headers?.authorization || ''
          const match = auth.match(/bearer\s+(.+)/i)
          return match ? match[1].trim() : null
        })

      // Mock tokenIntrospection for all cases
      const tokenIntrospection = require('openid-client').tokenIntrospection
      tokenIntrospection.mockImplementation(() => {
        return { active: true, sub: 'user123', client_id: 'test-client' }
      })

      for (const req of reqs) {
        // First ensure getValue returns null to force introspection
        jest.spyOn(cacheService, 'getValue').mockResolvedValue(null)

        const result = await service.validate(req as never)

        expect(result).toBeDefined()
        expect(result.auth).toBeDefined()
        expect(result.auth.active).toBe(true)
      }

      parseTokenSpy.mockRestore()
    })

    it('should handle malformed authorization header', async () => {
      const reqs = [
        { headers: { authorization: 'invalid-format' } },
        { headers: { authorization: 'Bearer' } },
        { headers: { authorization: '' } },
      ]

      for (const req of reqs) {
        const result = await service.validate(req as never)
        expect(result.auth).toBeUndefined()
        expect(result.fingerprint).toBeDefined()
      }
    })
  })
})
