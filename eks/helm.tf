# Required for Cloudflare support until:
# https://github.com/kubernetes-sigs/external-dns/issues/1961
resource "helm_release" "external-dns-cloudflare" {
  name       = "external-dns-cloudflare"
  repository = "oci://registry-1.docker.io/bitnamicharts"
  chart      = "external-dns"
  version    = "8.8.2"

  namespace        = "external-dns"
  create_namespace = true

  atomic = true

  values = [yamlencode({
    sources : [
      "ingress",
      "istio-gateway",
      "gateway-httproute",
      "gateway-grpcroute",
      "gateway-tlsroute",
      "gateway-tcproute",
      "gateway-udproute"
    ],
    provider : "cloudflare",
    cloudflare : {
      apiToken : var.cloudflare_api_token,
      proxied : true,
    }
    domainFilters : var.domain_name["cloudflare"],
    zoneIDFilter : var.zone_id_filter["cloudflare"],
    policy : "sync",
    txtOwnerId : local.resource_name,
    metrics : {
      enabled : true,
      podAnnotations : {
        "prometheus.io/scrape" : "true"
      }
      serviceMonitor : {
        enabled : true,
        namespace : "kube-prometheus-stack",
        interval : "30s"
      }
    },
    # logLevel : "debug"
  })]

  depends_on = [module.addons]
}

resource "helm_release" "redis" {
  # https://github.com/bitnami/charts/blob/main/bitnami/redis/Chart.yaml
  name       = "redis"
  repository = "oci://registry-1.docker.io/bitnamicharts"
  chart      = "redis"
  version    = "22.0.1"

  namespace        = "redis"
  create_namespace = true

  atomic = true

  values = [yamlencode({
    global : {
      storageClass : "standard",
      redis : {
        password : "EIqqycRwUb"
      },
      labels : {
        "istio.io/dataplane-mode" : "ambient"
      }
    },
    master : {
      persistence : {
        enabled : false,
        storageClass : "standard",
      }
    },
    replica : {
      persistence : {
        enabled : false,
        storageClass : "standard",
      }
    },
    metrics : {
      enabled : true,
      serviceMonitor : {
        enabled : true,
        namespace : "kube-prometheus-stack"
      }
    }
  })]

  depends_on = [kubernetes_storage_class_v1.gp3]
}
