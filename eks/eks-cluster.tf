module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> 21.0"

  name               = local.resource_name
  kubernetes_version = "1.33"

  endpoint_public_access = true

  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets

  eks_managed_node_groups = {
    managed = {
      min_size     = local.managed_node_group.min_size
      max_size     = local.managed_node_group.max_size
      desired_size = local.managed_node_group.desired_size

      ami_type = "AL2023_ARM_64_STANDARD"

      instance_types = [
        # "a1.xlarge",
        "t4g.xlarge"
      ]

      disk_size = local.managed_node_group.disk_size
    }
  }

  # Cluster access entry
  # To add the current caller identity as an administrator
  enable_cluster_creator_admin_permissions = true

  authentication_mode = "API"

  tags = merge(
    local.common_tags,
    { Name = "${local.resource_name}-mng" }
  )
}
