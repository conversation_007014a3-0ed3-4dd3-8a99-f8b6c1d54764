module "addons" {
  source  = "aws-ia/eks-blueprints-addons/aws"
  version = "~> 1.22"

  cluster_name      = module.eks.cluster_name
  cluster_endpoint  = module.eks.cluster_endpoint
  cluster_version   = module.eks.cluster_version
  oidc_provider_arn = module.eks.oidc_provider_arn

  # EKS Managed Add-ons
  eks_addons = {
    aws-ebs-csi-driver = {
      most_recent = true
    }
    coredns = {
      most_recent = true
    }
    eks-pod-identity-agent = {
      most_recent = true
    }
    vpc-cni = {
      most_recent    = true
      before_compute = true
      configuration_values = jsonencode({
        env = {
          # Reference docs https://docs.aws.amazon.com/eks/latest/userguide/cni-increase-ip-addresses.html
          ENABLE_PREFIX_DELEGATION = "true"
          WARM_PREFIX_TARGET       = "1"
        }
      })
    }
    kube-proxy = {
      most_recent = true
    }

  }

  # Add-ons

  # enable_aws_efs_csi_driver = true
  # enable_aws_fsx_csi_driver = true
  # enable_aws_gateway_api_controller = true
  # aws_gateway_api_controller = {
  #   set = [{
  #     name  = "clusterVpcId"
  #     value = module.vpc.vpc_id
  #   }]
  # }

  enable_aws_load_balancer_controller = true
  aws_load_balancer_controller = {
    set = [
      {
        name  = "vpcId"
        value = module.vpc.vpc_id
      },
      {
        name  = "podDisruptionBudget.maxUnavailable"
        value = 1
      },
      {
        name  = "enableServiceMutatorWebhook"
        value = "false"
      }
    ]
  }

  # enable_aws_node_termination_handler = true
  # enable_cluster_proportional_autoscaler = true
  # enable_karpenter                       = true
  enable_kube_prometheus_stack = true
  enable_metrics_server        = true
  # enable_external_dns                    = true

  enable_cert_manager = true
  cert_manager = {
    most_recent = true
  }

  tags = merge(local.common_tags)
}