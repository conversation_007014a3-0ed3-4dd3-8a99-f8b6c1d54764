model: litellm_proxy/Claude 3.7 Sonnet
editor-model: litellm_proxy/Claude 3.7 Sonnet
weak-model: litellm_proxy/Gemini 2.0 Flash Pro

alias:
  - claude: litellm_proxy/Claude 3.7 Sonnet
  - claude-35: litellm_proxy/Claude 3.5 Sonnet
  - gemini: litellm_proxy/Gemini 2.5 Pro
  - flash: litellm_proxy/Gemini 2.0 Flash Pro
  - r1: litellm_proxy/DeepSeek R1
  - deep: litellm_proxy/DeepSeek V3
  - gpt: litellm_proxy/ChatGPT 4o
  - 4o: litellm_proxy/OpenAI 4o
  - o1: litellm_proxy/OpenAI o1
  - mini: litellm_proxy/OpenAI o3 Mini
  - mini-high: litellm_proxy/OpenAI o3 Mini High

  # - "fast:github_copilot/gpt-4o"
  # - "code:github_copilot/claude-3.7-sonnet"
  # - "think:github_copilot/claude-3.7-sonnet-thought"

env:
  - LITELLM_PROXY_API_BASE=https://litellm.realityconnect.tech/v1

read:
  - .github/copilot-instructions.md
  - .github/instructions/seed.instructions.md
  - .github/instructions/nx.instructions.md
