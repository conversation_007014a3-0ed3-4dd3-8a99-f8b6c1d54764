const e = {
    name: 'main',
    subcommands: [
      {
        name: 'run-script',
        options: [{ name: ['-h', '--help'], description: 'display help for command', priority: 49 }],
        args: [{ name: 'script', isOptional: !0 }],
      },
      {
        name: 'api',
        options: [{ name: ['-h', '--help'], description: 'display help for command', priority: 49 }],
        args: [{ name: 'method', isOptional: !0 }],
      },
      {
        name: 'completion',
        hidden: !0,
        options: [{ name: ['-h', '--help'], description: 'display help for command', priority: 49 }],
      },
      {
        name: 'completion-script',
        hidden: !0,
        options: [{ name: ['-h', '--help'], description: 'display help for command', priority: 49 }],
      },
      {
        name: 'help',
        description: 'display help for command',
        priority: 49,
        args: { name: 'command', isOptional: !0, template: 'help' },
      },
    ],
    options: [
      { name: ['-V', '--version'], description: 'output the version number' },
      { name: ['-h', '--help'], description: 'display help for command', priority: 49 },
    ],
  },
  i = e
export { i as default }
