// Autogenerated by @fig/complete-commander

const completionSpec: Fig.Spec = {
  name: 'main',
  subcommands: [
    {
      name: 'run-script',
      options: [
        {
          name: ['-h', '--help'],
          description: 'display help for command',
          priority: 49,
        },
      ],
      args: [{ name: 'script', isOptional: true }],
    },
    {
      name: 'api',
      options: [
        {
          name: ['-h', '--help'],
          description: 'display help for command',
          priority: 49,
        },
      ],
      args: [{ name: 'method', isOptional: true }],
    },
    {
      name: 'completion',
      hidden: true,
      options: [
        {
          name: ['-h', '--help'],
          description: 'display help for command',
          priority: 49,
        },
      ],
    },
    {
      name: 'completion-script',
      hidden: true,
      options: [
        {
          name: ['-h', '--help'],
          description: 'display help for command',
          priority: 49,
        },
      ],
    },
    {
      name: 'help',
      description: 'display help for command',
      priority: 49,
      args: { name: 'command', isOptional: true, template: 'help' },
    },
  ],
  options: [
    { name: ['-V', '--version'], description: 'output the version number' },
    {
      name: ['-h', '--help'],
      description: 'display help for command',
      priority: 49,
    },
  ],
}

export default completionSpec
