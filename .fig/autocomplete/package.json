{"name": "boilerplate-fig-autocomplete", "version": "0.0.0", "description": "Boilerplate Fig autocomplete spec package", "scripts": {"dev": "npx @withfig/autocomplete-tools dev", "create-spec": "npx @withfig/autocomplete-tools create-spec", "publish-spec": "npx @fig/publish-spec -i", "test": "tsc --noEmit && echo 'All specs passed validation.'", "build": "npx @withfig/autocomplete-tools compile", "lint": "eslint '**/*.ts' && npx prettier --check '**/*.ts'", "lint:fix": "eslint '**/*.ts' --fix && npx prettier --write '**/*.ts'"}, "engines": {"node": ">=16"}, "fig": {"dev": {"description": "Watching and compile .ts files in ./src", "icon": "fig://template?badge=🛠", "priority": 100}, "create-spec": {"description": "Create a new spec with the provided name in ./src"}, "publish-spec": {"description": "Publish a spec to Fig teams"}, "test": {"description": "Typecheck all .ts files in ./src"}, "build": {"description": "Compile all files in ./src"}, "lint": {"description": "Check for linting issues"}, "lint:fix": {"description": "Fix linting issues"}}, "lint-staged": {"*.ts": ["eslint --fix .", "pretty-quick --staged"]}, "author": "", "license": "MIT", "devDependencies": {"@fig/eslint-config-autocomplete": "latest", "@fig/publish-spec": "^1.2.3", "@types/node": "^22.7.5", "@withfig/autocomplete-tools": "^2.10.0", "@withfig/autocomplete-types": "latest", "eslint": "^9.12.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "pretty-quick": "^4.0.0", "typescript": "^5.6.3"}}